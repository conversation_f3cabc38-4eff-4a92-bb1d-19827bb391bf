﻿[Config,STATUS,7/30 11:38:14,#00002] Initializing persistence in folder C:\ProgramData\i-net software\reporting_Temp_#60imported config#62
[Reporting,STATUS,7/30 11:38:15,#00002] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt in 236ms.
[Reporting,INFO  ,7/30 11:38:16,#00002] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/lns/LMS1201R34_zh_TW.rpt
[Reporting,DEBUG ,7/30 11:38:16,#00002] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/30 11:38:16,#00003] no datasource configuration for user scope
[Reporting,DEBUG ,7/30 11:38:16,#00002] [DatabaseConfiguration]setUseQuoteLowerCase false true
[Reporting,DEBUG ,7/30 11:38:16,#00002] [DatabaseConfiguration]setUseOrderBy true true
[Reporting,DEBUG ,7/30 11:38:16,#00002] [DatabaseConfiguration]setUseParenthesiseForJoin true true
[Reporting,DEBUG ,7/30 11:38:16,#00002] [DatabaseConfiguration]setIdentifierQuoteString  true
[Reporting,DEBUG ,7/30 11:38:16,#00002] [DatabaseConfiguration]setUseSQL92syntax true false true
[Reporting,DEBUG ,7/30 11:38:16,#00002] [DatabaseConfiguration]setUseWhereClause true true
[Reporting,DEBUG ,7/30 11:38:16,#00002] [DatabaseConfiguration]setUseEscapeEverything false true
[Reporting,DEBUG ,7/30 11:38:16,#00002] [DatabaseConfiguration]setAliasToken  true
[Reporting,DEBUG ,7/30 11:38:17,#00002] [DatabaseConfiguration]setUseQuoteLowerCase false true
[Reporting,DEBUG ,7/30 11:38:17,#00002] [DatabaseConfiguration]setUseParenthesiseForJoin true true
[Reporting,DEBUG ,7/30 11:38:17,#00002] [DatabaseConfiguration]setUseWhereClause true true
[Reporting,DEBUG ,7/30 11:38:17,#00002] [DatabaseConfiguration]setUseOrderBy true true
[Reporting,DEBUG ,7/30 11:38:17,#00002] [DatabaseConfiguration]setIdentifierQuoteString  true
[Reporting,DEBUG ,7/30 11:38:17,#00002] [DatabaseConfiguration]setUseSQL92syntax true false true
[Reporting,DEBUG ,7/30 11:38:17,#00002] [DatabaseConfiguration]setAliasToken  true
[Reporting,DEBUG ,7/30 11:38:17,#00002] [DatabaseConfiguration]setUseEscapeEverything false true
[Reporting,DEBUG ,7/30 11:38:17,#00002] Reference counter: Init references took (4ms)
[Reporting,DEBUG ,7/30 11:38:17,#00002] Memory at "afterSetReportFile": max=512 MB	total=508 MB	free=139 MB	used=369 MB
[Reporting,INFO  ,7/30 11:38:17,#00002] Set Prompt[C160M01A.CASETYPE]: 1
[Reporting,INFO  ,7/30 11:38:17,#00002] Set Prompt[BRANCHNAME]: 新店分行
[Reporting,INFO  ,7/30 11:38:17,#00002] Set Prompt[L160M01A.RANDOMCODE]: 65b44d8c89b44ab4aec06967f8e4fd41
[Reporting,INFO  ,7/30 11:38:17,#00002] Set Prompt[L160M01A.CASENO]: ２０２０新店(兆)授字第００６４０號
[Reporting,INFO  ,7/30 11:38:17,#00002] Set Prompt[L160M01A.CUSTNAME]: 邱F159894590
[Reporting,INFO  ,7/30 11:38:17,#00002] Set Prompt[L160M01B.CNTRNO]: 046110600178
[Reporting,INFO  ,7/30 11:38:17,#00002] [Engine.setData] with array for id: 1
[Reporting,DEBUG ,7/30 11:38:17,#00002] [Engine.setData] number of Fields: 200
[Reporting,DEBUG ,7/30 11:38:17,#00002] [Engine.setData] data array with 200 columns and 1 rows.
[Reporting,INFO  ,7/30 11:38:17,#00002] [Engine.setData] with array for id: 0
[Reporting,DEBUG ,7/30 11:38:17,#00002] [Engine.setData] number of Fields: 200
[Reporting,DEBUG ,7/30 11:38:17,#00002] [Engine.setData] data array with 200 columns and 1 rows.
[Reporting,INFO  ,7/30 11:38:17,#00002] Set locale: zh_TW
[Reporting,STATUS,7/30 11:38:17,#00002] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/lns/LMS1201R34_zh_TW.rpt in 986ms.
[Reporting,DEBUG ,7/30 11:38:17,#00002] Engine Statistics:
[Reporting,DEBUG ,7/30 11:38:17,#00002]   report loading cpu time: 46ms
[Reporting,DEBUG ,7/30 11:38:17,#00002]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/30 11:38:17,#00002]   main execution cpu time: 0ms
[Reporting,DEBUG ,7/30 11:38:17,#00002]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/30 11:38:17,#00002]   total running  cpu time: 46ms
[Reporting,DEBUG ,7/30 11:38:17,#00002]   report loading system time: 986ms
[Reporting,DEBUG ,7/30 11:38:17,#00002]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/30 11:38:17,#00002]   execution      system time: 0ms
[Reporting,DEBUG ,7/30 11:38:17,#00002]   total running  system time: 986ms
[Reporting,INFO  ,7/30 11:38:17,#00002] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt
[Reporting,DEBUG ,7/30 11:38:17,#00002] Reference counter: Init references took (1ms)
[Reporting,DEBUG ,7/30 11:38:17,#00002] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/30 11:38:17,#00002] Memory at "afterSetReportFile": max=512 MB	total=508 MB	free=178 MB	used=330 MB
[Reporting,INFO  ,7/30 11:38:17,#00002] Set Prompt[ERRORMSG]: EFD0066:Report Error [308] Invalid key: Bad Padding: message must be same length as key for rsa decryption
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at com.inet.report.ReportExceptionFactory.createReportException(SourceFile:44)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at com.inet.report.Engine.execute(SourceFile:1086)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at tw.com.jcs.common.report.ReportGenerator.generateReport(ReportGenerator.java:281)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at tw.com.jcs.common.report.ReportGenerator.generateReport(ReportGenerator.java:183)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl.genVacantLandReport(CLS1161RptServiceImpl.java:257)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl.generateReport(CLS1161RptServiceImpl.java:173)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl.getContent(CLS1161RptServiceImpl.java:109)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl$$FastClassBySpringCGLIB$$5b0d09e8.invoke(<generated>)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl$$EnhancerBySpringCGLIB$$be691e0e.getContent(<generated>)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.getContent(AbstractFileDownloadPage.java:306)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.execute(AbstractFileDownloadPage.java:260)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at tw.com.iisi.cap.base.pages.AbstractCapPage.processForm(AbstractCapPage.java:185)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at sun.reflect.GeneratedMethodAccessor241.invoke(Unknown Source)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:55)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at java.lang.reflect.Method.invoke(Method.java:508)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:707)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:790)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.servlet.ServletHolder$NotAsync.service(ServletHolder.java:1459)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.servlet.ServletHolder.handle(ServletHolder.java:799)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1656)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:352)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:117)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:83)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:164)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.csrf.CsrfFilter.doFilterInternal(CsrfFilter.java:132)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:117)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:225)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:190)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at tw.com.iisi.cap.web.filter.CapForwardFilter.doFilter(CapForwardFilter.java:109)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at tw.com.iisi.cap.log.CapLogContextFilter.doFilter(CapLogContextFilter.java:155)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:201)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.apache.logging.log4j.web.Log4jServletFilter.doFilter(Log4jServletFilter.java:71)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.servlet.ServletHandler.doHandle(ServletHandler.java:552)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:143)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:600)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:235)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.server.session.SessionHandler.doHandle(SessionHandler.java:1624)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:233)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.server.handler.ContextHandler.doHandle(ContextHandler.java:1440)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:188)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.servlet.ServletHandler.doScope(ServletHandler.java:505)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.server.session.SessionHandler.doScope(SessionHandler.java:1594)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:186)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.server.handler.ContextHandler.doScope(ContextHandler.java:1355)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:141)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.server.Server.handle(Server.java:516)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.server.HttpChannel.lambda$handle$1(HttpChannel.java:487)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.server.HttpChannel.dispatch(HttpChannel.java:732)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.server.HttpChannel.handle(HttpChannel.java:479)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.server.HttpConnection.onFillable(HttpConnection.java:277)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:311)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:105)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.io.ChannelEndPoint$1.run(ChannelEndPoint.java:104)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.runTask(EatWhatYouKill.java:338)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.doProduce(EatWhatYouKill.java:315)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.tryProduce(EatWhatYouKill.java:173)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.run(EatWhatYouKill.java:131)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:409)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:883)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1034)
[Reporting,INFO  ,7/30 11:38:17,#00002] 	at java.lang.Thread.run(Thread.java:825)
[Reporting,STATUS,7/30 11:38:17,#00002] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt in 17ms.
[Reporting,DEBUG ,7/30 11:38:17,#00002] Engine Statistics:
[Reporting,DEBUG ,7/30 11:38:17,#00002]   report loading cpu time: 15ms
[Reporting,DEBUG ,7/30 11:38:17,#00002]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/30 11:38:17,#00002]   main execution cpu time: 0ms
[Reporting,DEBUG ,7/30 11:38:17,#00002]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/30 11:38:17,#00002]   total running  cpu time: 15ms
[Reporting,DEBUG ,7/30 11:38:17,#00002]   report loading system time: 17ms
[Reporting,DEBUG ,7/30 11:38:17,#00002]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/30 11:38:17,#00002]   execution      system time: 0ms
[Reporting,DEBUG ,7/30 11:38:17,#00002]   total running  system time: 17ms
[Reporting,INFO  ,7/30 11:38:21,#00001] Host name/IP address:                CurryTheGoat/*************; CurryTheGoat/************
[Reporting,INFO  ,7/30 11:38:28,#00004] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt
[Reporting,DEBUG ,7/30 11:38:28,#00004] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/30 11:38:28,#00004] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/30 11:38:28,#00004] Memory at "afterSetReportFile": max=512 MB	total=508 MB	free=167 MB	used=341 MB
[Reporting,INFO  ,7/30 11:38:28,#00004] Set Prompt[ERRORMSG]: EFD0066:java.lang.NullPointerException
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at com.iisigroup.cap.component.impl.CapMvcParameters.getString(CapMvcParameters.java:335)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at com.iisigroup.cap.component.impl.CapMvcParameters.getString(CapMvcParameters.java:111)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at com.mega.eloan.lms.base.service.AbstractReportService.generateReport(AbstractReportService.java:60)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at com.mega.eloan.lms.base.service.AbstractReportService$$FastClassBySpringCGLIB$$5811df4d.invoke(<generated>)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at com.mega.eloan.lms.cls.report.impl.CLS1161R01RptServiceImpl$$EnhancerBySpringCGLIB$$d2ef5791.generateReport(<generated>)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl.genR01(CLS1161RptServiceImpl.java:641)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl.generateReport(CLS1161RptServiceImpl.java:135)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl.getContent(CLS1161RptServiceImpl.java:109)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl$$FastClassBySpringCGLIB$$5b0d09e8.invoke(<generated>)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl$$EnhancerBySpringCGLIB$$be691e0e.getContent(<generated>)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.getContent(AbstractFileDownloadPage.java:306)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.execute(AbstractFileDownloadPage.java:260)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at tw.com.iisi.cap.base.pages.AbstractCapPage.processForm(AbstractCapPage.java:185)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at sun.reflect.GeneratedMethodAccessor241.invoke(Unknown Source)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:55)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at java.lang.reflect.Method.invoke(Method.java:508)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:707)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:790)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.servlet.ServletHolder$NotAsync.service(ServletHolder.java:1459)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.servlet.ServletHolder.handle(ServletHolder.java:799)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1656)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:352)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:117)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:83)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:164)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.csrf.CsrfFilter.doFilterInternal(CsrfFilter.java:132)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:117)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:225)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:190)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at tw.com.iisi.cap.web.filter.CapForwardFilter.doFilter(CapForwardFilter.java:109)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at tw.com.iisi.cap.log.CapLogContextFilter.doFilter(CapLogContextFilter.java:155)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:201)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.apache.logging.log4j.web.Log4jServletFilter.doFilter(Log4jServletFilter.java:71)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.servlet.ServletHandler.doHandle(ServletHandler.java:552)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:143)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:600)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:235)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.server.session.SessionHandler.doHandle(SessionHandler.java:1624)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:233)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.server.handler.ContextHandler.doHandle(ContextHandler.java:1440)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:188)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.servlet.ServletHandler.doScope(ServletHandler.java:505)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.server.session.SessionHandler.doScope(SessionHandler.java:1594)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:186)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.server.handler.ContextHandler.doScope(ContextHandler.java:1355)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:141)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.server.Server.handle(Server.java:516)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.server.HttpChannel.lambda$handle$1(HttpChannel.java:487)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.server.HttpChannel.dispatch(HttpChannel.java:732)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.server.HttpChannel.handle(HttpChannel.java:479)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.server.HttpConnection.onFillable(HttpConnection.java:277)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:311)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:105)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.io.ChannelEndPoint$1.run(ChannelEndPoint.java:104)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.runTask(EatWhatYouKill.java:338)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.doProduce(EatWhatYouKill.java:315)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.tryProduce(EatWhatYouKill.java:173)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.run(EatWhatYouKill.java:131)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:409)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:883)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1034)
[Reporting,INFO  ,7/30 11:38:28,#00004] 	at java.lang.Thread.run(Thread.java:825)
[Reporting,INFO  ,7/30 11:38:28,#00004] i-net Clear Reports Plus - Plus Trial License for 90 days (will expire on 2025/10/15) with 2 clients
[Reporting,INFO  ,7/30 11:38:28,#00004] engineThreadPool.start(this)
[Reporting,DEBUG ,7/30 11:38:28,#00004] pdf export: user properties not defined
[Reporting,INFO  ,7/30 11:38:28,#00004] Fetch data for mainreport file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt
[Reporting,INFO  ,7/30 11:38:28,#00004] No datasource name or no database request required
[Reporting,DEBUG ,7/30 11:38:28,#00004] [Datasource#0] used DataFactory com.inet.report.Database@630e7e40
[Reporting,INFO  ,7/30 11:38:28,#00004] Sort checkpoint reached.
[Reporting,DEBUG ,7/30 11:38:28,#00004] Memory at "beforeSort": max=512 MB	total=508 MB	free=153 MB	used=355 MB
[Reporting,INFO  ,7/30 11:38:28,#00004] Must sort on RowSource ...
[Reporting,INFO  ,7/30 11:38:28,#00004] Sort on RowSource completed.
[Reporting,DEBUG ,7/30 11:38:28,#00004] Memory at "afterSort": max=512 MB	total=508 MB	free=153 MB	used=355 MB
[Reporting,DEBUG ,7/30 11:38:28,#00004] Memory at "beforeRendering": max=512 MB	total=508 MB	free=152 MB	used=356 MB
[Reporting,DEBUG ,7/30 11:38:29,#00004] addPage:2
[Reporting,DEBUG ,7/30 11:38:29,#00004] Memory at "afterRendering": max=512 MB	total=508 MB	free=143 MB	used=365 MB
[Reporting,DEBUG ,7/30 11:38:29,#00004] addPage:3
[Reporting,DEBUG ,7/30 11:38:29,#00004] addPage:4
[Reporting,DEBUG ,7/30 11:38:29,#00004] addPage:5
[Reporting,INFO  ,7/30 11:38:29,#00004] engine isFinish
[Reporting,STATUS,7/30 11:38:29,#00004] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt in 617ms.
[Reporting,DEBUG ,7/30 11:38:29,#00004] Engine Statistics:
[Reporting,DEBUG ,7/30 11:38:29,#00004]   report loading cpu time: 31ms
[Reporting,DEBUG ,7/30 11:38:29,#00004]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/30 11:38:29,#00004]   main execution cpu time: 468ms
[Reporting,DEBUG ,7/30 11:38:29,#00004]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/30 11:38:29,#00004]   total running  cpu time: 500ms
[Reporting,DEBUG ,7/30 11:38:29,#00004]   report loading system time: 31ms
[Reporting,DEBUG ,7/30 11:38:29,#00004]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/30 11:38:29,#00004]   execution      system time: 586ms
[Reporting,DEBUG ,7/30 11:38:29,#00004]   total running  system time: 617ms
[Reporting,INFO  ,7/30 11:38:29,#00004] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/lns/LMS1201R34_zh_TW.rpt
[Reporting,DEBUG ,7/30 11:38:29,#00004] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/30 11:38:29,#00004] [DatabaseConfiguration]setUseQuoteLowerCase false true
[Reporting,DEBUG ,7/30 11:38:29,#00004] [DatabaseConfiguration]setUseOrderBy true true
[Reporting,DEBUG ,7/30 11:38:29,#00004] [DatabaseConfiguration]setUseParenthesiseForJoin true true
[Reporting,DEBUG ,7/30 11:38:29,#00004] [DatabaseConfiguration]setIdentifierQuoteString  true
[Reporting,DEBUG ,7/30 11:38:29,#00004] [DatabaseConfiguration]setUseSQL92syntax true false true
[Reporting,DEBUG ,7/30 11:38:29,#00004] [DatabaseConfiguration]setUseWhereClause true true
[Reporting,DEBUG ,7/30 11:38:29,#00004] [DatabaseConfiguration]setUseEscapeEverything false true
[Reporting,DEBUG ,7/30 11:38:29,#00004] [DatabaseConfiguration]setAliasToken  true
[Reporting,DEBUG ,7/30 11:38:29,#00004] [DatabaseConfiguration]setUseQuoteLowerCase false true
[Reporting,DEBUG ,7/30 11:38:29,#00004] [DatabaseConfiguration]setUseParenthesiseForJoin true true
[Reporting,DEBUG ,7/30 11:38:29,#00004] [DatabaseConfiguration]setUseWhereClause true true
[Reporting,DEBUG ,7/30 11:38:29,#00004] [DatabaseConfiguration]setUseOrderBy true true
[Reporting,DEBUG ,7/30 11:38:29,#00004] [DatabaseConfiguration]setIdentifierQuoteString  true
[Reporting,DEBUG ,7/30 11:38:29,#00004] [DatabaseConfiguration]setUseSQL92syntax true false true
[Reporting,DEBUG ,7/30 11:38:29,#00004] [DatabaseConfiguration]setAliasToken  true
[Reporting,DEBUG ,7/30 11:38:29,#00004] [DatabaseConfiguration]setUseEscapeEverything false true
[Reporting,DEBUG ,7/30 11:38:29,#00004] Reference counter: Init references took (4ms)
[Reporting,DEBUG ,7/30 11:38:29,#00004] Memory at "afterSetReportFile": max=512 MB	total=508 MB	free=134 MB	used=374 MB
[Reporting,INFO  ,7/30 11:38:29,#00004] Set Prompt[C160M01A.CASETYPE]: 1
[Reporting,INFO  ,7/30 11:38:29,#00004] Set Prompt[BRANCHNAME]: 新店分行
[Reporting,INFO  ,7/30 11:38:29,#00004] Set Prompt[L160M01A.RANDOMCODE]: d16a3b145b1a4e1aa42448ef702a807e
[Reporting,INFO  ,7/30 11:38:29,#00004] Set Prompt[L160M01A.CASENO]: ２０２０新店(兆)授字第００６４０號
[Reporting,INFO  ,7/30 11:38:29,#00004] Set Prompt[L160M01A.CUSTNAME]: 邱F159894590
[Reporting,INFO  ,7/30 11:38:29,#00004] Set Prompt[L160M01B.CNTRNO]: 046110600178
[Reporting,INFO  ,7/30 11:38:29,#00004] [Engine.setData] with array for id: 1
[Reporting,DEBUG ,7/30 11:38:29,#00004] [Engine.setData] number of Fields: 200
[Reporting,DEBUG ,7/30 11:38:29,#00004] [Engine.setData] data array with 200 columns and 1 rows.
[Reporting,INFO  ,7/30 11:38:29,#00004] [Engine.setData] with array for id: 0
[Reporting,DEBUG ,7/30 11:38:29,#00004] [Engine.setData] number of Fields: 200
[Reporting,DEBUG ,7/30 11:38:29,#00004] [Engine.setData] data array with 200 columns and 1 rows.
[Reporting,INFO  ,7/30 11:38:29,#00004] Set locale: zh_TW
[Reporting,INFO  ,7/30 11:38:29,#00004] i-net Clear Reports Plus - Plus Trial License for 90 days (will expire on 2025/10/15) with 2 clients
[Reporting,INFO  ,7/30 11:38:29,#00004] engineThreadPool.start(this)
[Reporting,DEBUG ,7/30 11:38:29,#00004] pdf export: user properties not defined
[Reporting,INFO  ,7/30 11:38:29,#00004] Fetch data for mainreport file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/lns/LMS1201R34_zh_TW.rpt
[Reporting,DEBUG ,7/30 11:38:29,#00004] [Datasource#0] used DataFactory com.inet.report.Database@9f6766b1
[Reporting,INFO  ,7/30 11:38:29,#00004] Sort checkpoint reached.
[Reporting,DEBUG ,7/30 11:38:29,#00004] Memory at "beforeSort": max=512 MB	total=508 MB	free=134 MB	used=374 MB
[Reporting,INFO  ,7/30 11:38:29,#00004] Must sort on RowSource ...
[Reporting,INFO  ,7/30 11:38:29,#00004] Sort on RowSource completed.
[Reporting,DEBUG ,7/30 11:38:29,#00004] Memory at "afterSort": max=512 MB	total=508 MB	free=134 MB	used=374 MB
[Reporting,INFO  ,7/30 11:38:29,#00004] Fetch data for subreport1 空地貸款註記
[Reporting,DEBUG ,7/30 11:38:29,#00004] [Datasource#0] used DataFactory com.inet.report.Database@b9556fde
[Reporting,DEBUG ,7/30 11:38:29,#00004] Memory at "beforeRendering": max=512 MB	total=508 MB	free=134 MB	used=374 MB
[Reporting,DEBUG ,7/30 11:38:29,#00004] setSubreportLinksToSF(com.inet.report.Subreport@f4ac29b9)
[Reporting,DEBUG ,7/30 11:38:29,#00004] pass 0 prompts to 空地貸款註記
[Reporting,INFO  ,7/30 11:38:29,#00004] Sort checkpoint reached.
[Reporting,DEBUG ,7/30 11:38:29,#00004] Memory at "beforeSort": max=512 MB	total=508 MB	free=163 MB	used=345 MB
[Reporting,INFO  ,7/30 11:38:29,#00004] Must sort on RowSource ...
[Reporting,INFO  ,7/30 11:38:29,#00004] Sort on RowSource completed.
[Reporting,DEBUG ,7/30 11:38:29,#00004] Memory at "afterSort": max=512 MB	total=508 MB	free=163 MB	used=345 MB
[Reporting,DEBUG ,7/30 11:38:29,#00004] Memory at "beforeRendering": max=512 MB	total=508 MB	free=163 MB	used=345 MB
[Reporting,DEBUG ,7/30 11:38:29,#00004] Memory at "afterRendering": max=512 MB	total=508 MB	free=161 MB	used=347 MB
[Reporting,DEBUG ,7/30 11:38:29,#00004] addPage:2
[Reporting,DEBUG ,7/30 11:38:29,#00004] Memory at "afterRendering": max=512 MB	total=508 MB	free=161 MB	used=347 MB
[Reporting,DEBUG ,7/30 11:38:29,#00004] addPage:2
[Reporting,DEBUG ,7/30 11:38:29,#00004] addPage:3
[Reporting,INFO  ,7/30 11:38:29,#00004] engine isFinish
[Reporting,STATUS,7/30 11:38:29,#00004] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/lns/LMS1201R34_zh_TW.rpt in 612ms.
[Reporting,DEBUG ,7/30 11:38:29,#00004] Engine Statistics:
[Reporting,DEBUG ,7/30 11:38:29,#00004]   report loading cpu time: 31ms
[Reporting,DEBUG ,7/30 11:38:29,#00004]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/30 11:38:29,#00004]   main execution cpu time: 453ms
[Reporting,DEBUG ,7/30 11:38:29,#00004]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/30 11:38:29,#00004]   total running  cpu time: 484ms
[Reporting,DEBUG ,7/30 11:38:29,#00004]   report loading system time: 58ms
[Reporting,DEBUG ,7/30 11:38:29,#00004]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/30 11:38:29,#00004]   execution      system time: 553ms
[Reporting,DEBUG ,7/30 11:38:29,#00004]   total running  system time: 612ms
[Reporting,INFO  ,7/30 11:38:29,#00004] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt
[Reporting,DEBUG ,7/30 11:38:29,#00004] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/30 11:38:29,#00004] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/30 11:38:29,#00004] Memory at "afterSetReportFile": max=512 MB	total=508 MB	free=149 MB	used=359 MB
[Reporting,INFO  ,7/30 11:38:29,#00004] Set Prompt[ERRORMSG]: EFD0066:ExceptionConverter: java.io.IOException: The document has no pages.
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at com.lowagie.text.pdf.PdfPages.writePageTree(PdfPages.java:119)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at com.lowagie.text.pdf.PdfWriter.close(PdfWriter.java:1173)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at com.lowagie.text.pdf.PdfDocument.close(PdfDocument.java:868)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at com.lowagie.text.Document.close(Document.java:478)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at tw.com.jcs.common.report.PdfTools.mergeReWritePagePdf(PdfTools.java:1148)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at tw.com.jcs.common.report.PdfTools.mergeReWritePagePdf(PdfTools.java:967)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl.generateReport(CLS1161RptServiceImpl.java:182)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl.getContent(CLS1161RptServiceImpl.java:109)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl$$FastClassBySpringCGLIB$$5b0d09e8.invoke(<generated>)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at com.mega.eloan.lms.cls.report.impl.CLS1161RptServiceImpl$$EnhancerBySpringCGLIB$$be691e0e.getContent(<generated>)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.getContent(AbstractFileDownloadPage.java:306)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at com.mega.eloan.lms.base.pages.AbstractFileDownloadPage.execute(AbstractFileDownloadPage.java:260)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at tw.com.iisi.cap.base.pages.AbstractCapPage.processForm(AbstractCapPage.java:185)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at sun.reflect.GeneratedMethodAccessor241.invoke(Unknown Source)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:55)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at java.lang.reflect.Method.invoke(Method.java:508)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:707)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at javax.servlet.http.HttpServlet.service(HttpServlet.java:790)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.servlet.ServletHolder$NotAsync.service(ServletHolder.java:1459)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.servlet.ServletHolder.handle(ServletHolder.java:799)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1656)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:352)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:117)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:83)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:164)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.csrf.CsrfFilter.doFilterInternal(CsrfFilter.java:132)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:117)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:225)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:190)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.doFilterInternal(OpenEntityManagerInViewFilter.java:186)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at tw.com.iisi.cap.web.filter.CapForwardFilter.doFilter(CapForwardFilter.java:109)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at tw.com.iisi.cap.log.CapLogContextFilter.doFilter(CapLogContextFilter.java:155)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:201)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.apache.logging.log4j.web.Log4jServletFilter.doFilter(Log4jServletFilter.java:71)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.servlet.FilterHolder.doFilter(FilterHolder.java:193)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1626)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.servlet.ServletHandler.doHandle(ServletHandler.java:552)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:143)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:600)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:235)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.server.session.SessionHandler.doHandle(SessionHandler.java:1624)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:233)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.server.handler.ContextHandler.doHandle(ContextHandler.java:1440)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:188)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.servlet.ServletHandler.doScope(ServletHandler.java:505)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.server.session.SessionHandler.doScope(SessionHandler.java:1594)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:186)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.server.handler.ContextHandler.doScope(ContextHandler.java:1355)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:141)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:127)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.server.Server.handle(Server.java:516)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.server.HttpChannel.lambda$handle$1(HttpChannel.java:487)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.server.HttpChannel.dispatch(HttpChannel.java:732)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.server.HttpChannel.handle(HttpChannel.java:479)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.server.HttpConnection.onFillable(HttpConnection.java:277)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:311)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:105)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.io.ChannelEndPoint$1.run(ChannelEndPoint.java:104)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.runTask(EatWhatYouKill.java:338)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.doProduce(EatWhatYouKill.java:315)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.tryProduce(EatWhatYouKill.java:173)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.run(EatWhatYouKill.java:131)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:409)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:883)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1034)
[Reporting,INFO  ,7/30 11:38:29,#00004] 	at java.lang.Thread.run(Thread.java:825)
[Reporting,INFO  ,7/30 11:38:29,#00004] i-net Clear Reports Plus - Plus Trial License for 90 days (will expire on 2025/10/15) with 2 clients
[Reporting,INFO  ,7/30 11:38:29,#00004] engineThreadPool.start(this)
[Reporting,DEBUG ,7/30 11:38:29,#00004] pdf export: user properties not defined
[Reporting,INFO  ,7/30 11:38:29,#00004] Fetch data for mainreport file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt
[Reporting,INFO  ,7/30 11:38:29,#00004] No datasource name or no database request required
[Reporting,DEBUG ,7/30 11:38:29,#00004] [Datasource#0] used DataFactory com.inet.report.Database@68be329f
[Reporting,INFO  ,7/30 11:38:29,#00004] Sort checkpoint reached.
[Reporting,DEBUG ,7/30 11:38:29,#00004] Memory at "beforeSort": max=512 MB	total=508 MB	free=149 MB	used=359 MB
[Reporting,INFO  ,7/30 11:38:29,#00004] Must sort on RowSource ...
[Reporting,INFO  ,7/30 11:38:29,#00004] Sort on RowSource completed.
[Reporting,DEBUG ,7/30 11:38:29,#00004] Memory at "afterSort": max=512 MB	total=508 MB	free=149 MB	used=359 MB
[Reporting,DEBUG ,7/30 11:38:29,#00004] Memory at "beforeRendering": max=512 MB	total=508 MB	free=149 MB	used=359 MB
[Reporting,DEBUG ,7/30 11:38:29,#00004] addPage:2
[Reporting,DEBUG ,7/30 11:38:29,#00004] Memory at "afterRendering": max=512 MB	total=508 MB	free=147 MB	used=361 MB
[Reporting,DEBUG ,7/30 11:38:29,#00004] addPage:3
[Reporting,DEBUG ,7/30 11:38:29,#00004] addPage:4
[Reporting,DEBUG ,7/30 11:38:29,#00004] addPage:5
[Reporting,INFO  ,7/30 11:38:29,#00004] engine isFinish
[Reporting,STATUS,7/30 11:38:29,#00004] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/EXCEPTION_zh_TW.rpt in 59ms.
[Reporting,DEBUG ,7/30 11:38:29,#00004] Engine Statistics:
[Reporting,DEBUG ,7/30 11:38:29,#00004]   report loading cpu time: 15ms
[Reporting,DEBUG ,7/30 11:38:29,#00004]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/30 11:38:29,#00004]   main execution cpu time: 31ms
[Reporting,DEBUG ,7/30 11:38:29,#00004]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/30 11:38:29,#00004]   total running  cpu time: 46ms
[Reporting,DEBUG ,7/30 11:38:29,#00004]   report loading system time: 20ms
[Reporting,DEBUG ,7/30 11:38:29,#00004]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/30 11:38:29,#00004]   execution      system time: 39ms
[Reporting,DEBUG ,7/30 11:38:29,#00004]   total running  system time: 59ms
[Reporting,INFO  ,7/30 11:39:04,#00004] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt
[Reporting,DEBUG ,7/30 11:39:04,#00004] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/30 11:39:04,#00004] [DatabaseConfiguration]setUseQuoteLowerCase false true
[Reporting,DEBUG ,7/30 11:39:04,#00004] [DatabaseConfiguration]setUseOrderBy true true
[Reporting,DEBUG ,7/30 11:39:04,#00004] [DatabaseConfiguration]setUseParenthesiseForJoin true true
[Reporting,DEBUG ,7/30 11:39:04,#00004] [DatabaseConfiguration]setIdentifierQuoteString  true
[Reporting,DEBUG ,7/30 11:39:04,#00004] [DatabaseConfiguration]setUseSQL92syntax true false true
[Reporting,DEBUG ,7/30 11:39:04,#00004] [DatabaseConfiguration]setUseWhereClause true true
[Reporting,DEBUG ,7/30 11:39:04,#00004] [DatabaseConfiguration]setUseEscapeEverything false true
[Reporting,DEBUG ,7/30 11:39:04,#00004] [DatabaseConfiguration]setAliasToken  true
[Reporting,DEBUG ,7/30 11:39:04,#00004] Reference counter: Init references took (1ms)
[Reporting,DEBUG ,7/30 11:39:04,#00004] Memory at "afterSetReportFile": max=512 MB	total=508 MB	free=169 MB	used=339 MB
[Reporting,INFO  ,7/30 11:39:04,#00004] Set Prompt[C160M01A.BrNo]: 新店分行
[Reporting,INFO  ,7/30 11:39:04,#00004] Set Prompt[C160M01A.CUSTID]: F159894590  邱F159894590
[Reporting,INFO  ,7/30 11:39:04,#00004] [Engine.setData] with array for id: 0
[Reporting,DEBUG ,7/30 11:39:04,#00004] [Engine.setData] number of Fields: 200
[Reporting,DEBUG ,7/30 11:39:04,#00004] [Engine.setData] data array with 200 columns and 22 rows.
[Reporting,INFO  ,7/30 11:39:04,#00004] Set locale: zh_TW
[Reporting,INFO  ,7/30 11:39:04,#00004] i-net Clear Reports Plus - Plus Trial License for 90 days (will expire on 2025/10/15) with 2 clients
[Reporting,INFO  ,7/30 11:39:04,#00004] engineThreadPool.start(this)
[Reporting,DEBUG ,7/30 11:39:04,#00004] pdf export: user properties not defined
[Reporting,INFO  ,7/30 11:39:04,#00004] Fetch data for mainreport file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt
[Reporting,DEBUG ,7/30 11:39:04,#00004] [Datasource#0] used DataFactory com.inet.report.Database@906c7a02
[Reporting,INFO  ,7/30 11:39:04,#00004] Sort checkpoint reached.
[Reporting,DEBUG ,7/30 11:39:04,#00004] Memory at "beforeSort": max=512 MB	total=508 MB	free=168 MB	used=340 MB
[Reporting,INFO  ,7/30 11:39:04,#00004] Must sort on RowSource ...
[Reporting,INFO  ,7/30 11:39:04,#00004] Sort on RowSource completed.
[Reporting,DEBUG ,7/30 11:39:04,#00004] Memory at "afterSort": max=512 MB	total=508 MB	free=168 MB	used=340 MB
[Reporting,DEBUG ,7/30 11:39:04,#00004] Memory at "beforeRendering": max=512 MB	total=508 MB	free=168 MB	used=340 MB
[Reporting,DEBUG ,7/30 11:39:04,#00004] Memory at "afterRendering": max=512 MB	total=508 MB	free=168 MB	used=340 MB
[Reporting,DEBUG ,7/30 11:39:04,#00004] addPage:2
[Reporting,DEBUG ,7/30 11:39:04,#00004] addPage:3
[Reporting,DEBUG ,7/30 11:39:04,#00004] addPage:4
[Reporting,INFO  ,7/30 11:39:04,#00004] engine isFinish
[Reporting,STATUS,7/30 11:39:04,#00004] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt in 72ms.
[Reporting,DEBUG ,7/30 11:39:04,#00004] Engine Statistics:
[Reporting,DEBUG ,7/30 11:39:04,#00004]   report loading cpu time: 31ms
[Reporting,DEBUG ,7/30 11:39:04,#00004]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/30 11:39:04,#00004]   main execution cpu time: 31ms
[Reporting,DEBUG ,7/30 11:39:04,#00004]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/30 11:39:04,#00004]   total running  cpu time: 62ms
[Reporting,DEBUG ,7/30 11:39:04,#00004]   report loading system time: 30ms
[Reporting,DEBUG ,7/30 11:39:04,#00004]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/30 11:39:04,#00004]   execution      system time: 42ms
[Reporting,DEBUG ,7/30 11:39:04,#00004]   total running  system time: 72ms
[Reporting,INFO  ,7/30 11:39:22,#00005] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt
[Reporting,DEBUG ,7/30 11:39:22,#00005] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/30 11:39:22,#00005] [DatabaseConfiguration]setUseQuoteLowerCase false true
[Reporting,DEBUG ,7/30 11:39:22,#00005] [DatabaseConfiguration]setUseOrderBy true true
[Reporting,DEBUG ,7/30 11:39:22,#00005] [DatabaseConfiguration]setUseParenthesiseForJoin true true
[Reporting,DEBUG ,7/30 11:39:22,#00005] [DatabaseConfiguration]setIdentifierQuoteString  true
[Reporting,DEBUG ,7/30 11:39:22,#00005] [DatabaseConfiguration]setUseSQL92syntax true false true
[Reporting,DEBUG ,7/30 11:39:22,#00005] [DatabaseConfiguration]setUseWhereClause true true
[Reporting,DEBUG ,7/30 11:39:22,#00005] [DatabaseConfiguration]setUseEscapeEverything false true
[Reporting,DEBUG ,7/30 11:39:22,#00005] [DatabaseConfiguration]setAliasToken  true
[Reporting,DEBUG ,7/30 11:39:22,#00005] Reference counter: Init references took (1ms)
[Reporting,DEBUG ,7/30 11:39:22,#00005] Memory at "afterSetReportFile": max=512 MB	total=508 MB	free=137 MB	used=371 MB
[Reporting,INFO  ,7/30 11:39:22,#00005] Set Prompt[C160M01A.BrNo]: 新店分行
[Reporting,INFO  ,7/30 11:39:22,#00005] Set Prompt[C160M01A.CUSTID]: F159894590  邱F159894590
[Reporting,INFO  ,7/30 11:39:22,#00005] [Engine.setData] with array for id: 0
[Reporting,DEBUG ,7/30 11:39:22,#00005] [Engine.setData] number of Fields: 200
[Reporting,DEBUG ,7/30 11:39:22,#00005] [Engine.setData] data array with 200 columns and 22 rows.
[Reporting,INFO  ,7/30 11:39:22,#00005] Set locale: zh_TW
[Reporting,INFO  ,7/30 11:39:22,#00005] i-net Clear Reports Plus - Plus Trial License for 90 days (will expire on 2025/10/15) with 2 clients
[Reporting,INFO  ,7/30 11:39:22,#00005] engineThreadPool.start(this)
[Reporting,DEBUG ,7/30 11:39:22,#00005] pdf export: user properties not defined
[Reporting,INFO  ,7/30 11:39:22,#00005] Fetch data for mainreport file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt
[Reporting,DEBUG ,7/30 11:39:22,#00005] [Datasource#0] used DataFactory com.inet.report.Database@2711b11f
[Reporting,INFO  ,7/30 11:39:22,#00005] Sort checkpoint reached.
[Reporting,DEBUG ,7/30 11:39:22,#00005] Memory at "beforeSort": max=512 MB	total=508 MB	free=136 MB	used=372 MB
[Reporting,INFO  ,7/30 11:39:22,#00005] Must sort on RowSource ...
[Reporting,INFO  ,7/30 11:39:22,#00005] Sort on RowSource completed.
[Reporting,DEBUG ,7/30 11:39:22,#00005] Memory at "afterSort": max=512 MB	total=508 MB	free=136 MB	used=372 MB
[Reporting,DEBUG ,7/30 11:39:22,#00005] Memory at "beforeRendering": max=512 MB	total=508 MB	free=136 MB	used=372 MB
[Reporting,DEBUG ,7/30 11:39:22,#00005] Memory at "afterRendering": max=512 MB	total=508 MB	free=135 MB	used=373 MB
[Reporting,DEBUG ,7/30 11:39:22,#00005] addPage:2
[Reporting,DEBUG ,7/30 11:39:22,#00005] addPage:3
[Reporting,DEBUG ,7/30 11:39:22,#00005] addPage:4
[Reporting,INFO  ,7/30 11:39:22,#00005] engine isFinish
[Reporting,STATUS,7/30 11:39:22,#00005] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt in 73ms.
[Reporting,DEBUG ,7/30 11:39:22,#00005] Engine Statistics:
[Reporting,DEBUG ,7/30 11:39:22,#00005]   report loading cpu time: 31ms
[Reporting,DEBUG ,7/30 11:39:22,#00005]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/30 11:39:22,#00005]   main execution cpu time: 31ms
[Reporting,DEBUG ,7/30 11:39:22,#00005]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/30 11:39:22,#00005]   total running  cpu time: 62ms
[Reporting,DEBUG ,7/30 11:39:22,#00005]   report loading system time: 28ms
[Reporting,DEBUG ,7/30 11:39:22,#00005]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/30 11:39:22,#00005]   execution      system time: 44ms
[Reporting,DEBUG ,7/30 11:39:22,#00005]   total running  system time: 73ms
[Reporting,INFO  ,7/30 11:39:44,#00006] setReportFile: file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt
[Reporting,DEBUG ,7/30 11:39:44,#00006] Reference counter: Init references took (0ms)
[Reporting,DEBUG ,7/30 11:39:44,#00006] [DatabaseConfiguration]setUseQuoteLowerCase false true
[Reporting,DEBUG ,7/30 11:39:44,#00006] [DatabaseConfiguration]setUseOrderBy true true
[Reporting,DEBUG ,7/30 11:39:44,#00006] [DatabaseConfiguration]setUseParenthesiseForJoin true true
[Reporting,DEBUG ,7/30 11:39:44,#00006] [DatabaseConfiguration]setIdentifierQuoteString  true
[Reporting,DEBUG ,7/30 11:39:44,#00006] [DatabaseConfiguration]setUseSQL92syntax true false true
[Reporting,DEBUG ,7/30 11:39:44,#00006] [DatabaseConfiguration]setUseWhereClause true true
[Reporting,DEBUG ,7/30 11:39:44,#00006] [DatabaseConfiguration]setUseEscapeEverything false true
[Reporting,DEBUG ,7/30 11:39:44,#00006] [DatabaseConfiguration]setAliasToken  true
[Reporting,DEBUG ,7/30 11:39:44,#00006] Reference counter: Init references took (1ms)
[Reporting,DEBUG ,7/30 11:39:44,#00006] Memory at "afterSetReportFile": max=512 MB	total=508 MB	free=163 MB	used=345 MB
[Reporting,INFO  ,7/30 11:39:44,#00006] Set Prompt[C160M01A.BrNo]: 新店分行
[Reporting,INFO  ,7/30 11:39:44,#00006] Set Prompt[C160M01A.CUSTID]: F159894590  邱F159894590
[Reporting,INFO  ,7/30 11:39:44,#00006] [Engine.setData] with array for id: 0
[Reporting,DEBUG ,7/30 11:39:44,#00006] [Engine.setData] number of Fields: 200
[Reporting,DEBUG ,7/30 11:39:44,#00006] [Engine.setData] data array with 200 columns and 22 rows.
[Reporting,INFO  ,7/30 11:39:44,#00006] Set locale: zh_TW
[Reporting,INFO  ,7/30 11:39:44,#00006] i-net Clear Reports Plus - Plus Trial License for 90 days (will expire on 2025/10/15) with 2 clients
[Reporting,INFO  ,7/30 11:39:44,#00006] engineThreadPool.start(this)
[Reporting,DEBUG ,7/30 11:39:44,#00006] pdf export: user properties not defined
[Reporting,INFO  ,7/30 11:39:44,#00006] Fetch data for mainreport file:/C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt
[Reporting,DEBUG ,7/30 11:39:44,#00006] [Datasource#0] used DataFactory com.inet.report.Database@55a26a48
[Reporting,INFO  ,7/30 11:39:44,#00006] Sort checkpoint reached.
[Reporting,DEBUG ,7/30 11:39:44,#00006] Memory at "beforeSort": max=512 MB	total=508 MB	free=162 MB	used=346 MB
[Reporting,INFO  ,7/30 11:39:44,#00006] Must sort on RowSource ...
[Reporting,INFO  ,7/30 11:39:44,#00006] Sort on RowSource completed.
[Reporting,DEBUG ,7/30 11:39:44,#00006] Memory at "afterSort": max=512 MB	total=508 MB	free=162 MB	used=346 MB
[Reporting,DEBUG ,7/30 11:39:44,#00006] Memory at "beforeRendering": max=512 MB	total=508 MB	free=162 MB	used=346 MB
[Reporting,DEBUG ,7/30 11:39:44,#00006] Memory at "afterRendering": max=512 MB	total=508 MB	free=161 MB	used=347 MB
[Reporting,DEBUG ,7/30 11:39:44,#00006] addPage:2
[Reporting,DEBUG ,7/30 11:39:44,#00006] addPage:3
[Reporting,DEBUG ,7/30 11:39:44,#00006] addPage:4
[Reporting,INFO  ,7/30 11:39:44,#00006] engine isFinish
[Reporting,STATUS,7/30 11:39:44,#00006] Rendered report /C:/Project/Eloan/LMS/workspace/MEGA-LMS-UPGRADE/lms/lms-config/target/classes/report/cls/CLS1161R02_zh_TW.rpt in 68ms.
[Reporting,DEBUG ,7/30 11:39:44,#00006] Engine Statistics:
[Reporting,DEBUG ,7/30 11:39:44,#00006]   report loading cpu time: 15ms
[Reporting,DEBUG ,7/30 11:39:44,#00006]   data fetching  cpu time: 0ms
[Reporting,DEBUG ,7/30 11:39:44,#00006]   main execution cpu time: 46ms
[Reporting,DEBUG ,7/30 11:39:44,#00006]   sub execution  cpu time: 0ms
[Reporting,DEBUG ,7/30 11:39:44,#00006]   total running  cpu time: 62ms
[Reporting,DEBUG ,7/30 11:39:44,#00006]   report loading system time: 26ms
[Reporting,DEBUG ,7/30 11:39:44,#00006]   data fetching  system time: 0ms
[Reporting,DEBUG ,7/30 11:39:44,#00006]   execution      system time: 42ms
[Reporting,DEBUG ,7/30 11:39:44,#00006]   total running  system time: 68ms
