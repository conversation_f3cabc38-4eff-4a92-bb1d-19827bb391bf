package com.mega.eloan.lms.cls.handler.form;

import java.io.File;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.TreeSet;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.iisigroup.cap.component.impl.CapMvcParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.gwclient.EJCICGwClient;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.BeanValidator;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.ClsUtility;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.constants.URLConstant;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CLSDocStatusEnum;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.service.AMLRelateService;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.NumberService;
import com.mega.eloan.lms.base.service.ProdService;
import com.mega.eloan.lms.base.service.ProdService.ProdKindEnum;
import com.mega.eloan.lms.base.service.RPAProcessService;
import com.mega.eloan.lms.cls.common.ClsUtil;
import com.mega.eloan.lms.cls.constants.ClsConstants;
import com.mega.eloan.lms.cls.pages.CLS1151S01Page;
import com.mega.eloan.lms.cls.pages.CLS1161M01Page;
import com.mega.eloan.lms.cls.pages.CLS1161S02APage;
import com.mega.eloan.lms.cls.pages.CLS1161V03Page;
import com.mega.eloan.lms.cls.panels.CLS1161S01Panel;
import com.mega.eloan.lms.cls.panels.CLS1161S02Panel;
import com.mega.eloan.lms.cls.panels.CLS1161S04Panel;
import com.mega.eloan.lms.cls.panels.CLS1201S20Panel;
import com.mega.eloan.lms.cls.report.CLS1131R04RptService;
import com.mega.eloan.lms.cls.service.CLS1131Service;
import com.mega.eloan.lms.cls.service.CLS1141Service;
import com.mega.eloan.lms.cls.service.CLS1161Service;
import com.mega.eloan.lms.cls.service.CLS3401Service;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.ejcic.service.EjcicService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.mfaloan.bean.ELF600;
import com.mega.eloan.lms.mfaloan.bean.MISLN20;
import com.mega.eloan.lms.mfaloan.bean.MISLN30;
import com.mega.eloan.lms.mfaloan.bean.PTEAMAPP;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisELF600Service;
import com.mega.eloan.lms.mfaloan.service.MisLNF030Service;
import com.mega.eloan.lms.mfaloan.service.MisMISLN20Service;
import com.mega.eloan.lms.mfaloan.service.MisPTEAMAPPService;
import com.mega.eloan.lms.mfaloan.service.MisStoredProcService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C101M01A;
import com.mega.eloan.lms.model.C101S01J;
import com.mega.eloan.lms.model.C101S04W;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C120S01E;
import com.mega.eloan.lms.model.C120S01Q;
import com.mega.eloan.lms.model.C120S02A;
import com.mega.eloan.lms.model.C122M01A;
import com.mega.eloan.lms.model.C160A01A;
import com.mega.eloan.lms.model.C160M01A;
import com.mega.eloan.lms.model.C160M01B;
import com.mega.eloan.lms.model.C160M01C;
import com.mega.eloan.lms.model.C160M01D;
import com.mega.eloan.lms.model.C160M01E;
import com.mega.eloan.lms.model.C160M01F;
import com.mega.eloan.lms.model.C160S01A;
import com.mega.eloan.lms.model.C160S01B;
import com.mega.eloan.lms.model.C160S01C;
import com.mega.eloan.lms.model.C160S01D;
import com.mega.eloan.lms.model.C160S01E;
import com.mega.eloan.lms.model.C160S01F;
import com.mega.eloan.lms.model.C160S01G;
import com.mega.eloan.lms.model.C160S02A;
import com.mega.eloan.lms.model.C340M01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01C;
import com.mega.eloan.lms.model.L120S09A;
import com.mega.eloan.lms.model.L120S09B;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01R;
import com.mega.eloan.lms.model.L140M04A;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.eloan.lms.model.L140S02C;
import com.mega.eloan.lms.model.L140S02E;
import com.mega.eloan.lms.model.L140S02F;
import com.mega.eloan.lms.model.L250M01A;
import com.mega.eloan.lms.validation.group.Check2;
import com.mega.eloan.lms.validation.group.Check3;
import com.mega.eloan.lms.validation.group.SaveCheck;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

// UPGRADETODO: JXL 改寫為 Apache POI - 更新 import 語句
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.FillPatternType;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.auth.CodeItemService;
import tw.com.jcs.common.Arithmetic;
import tw.com.jcs.common.JsonMapper;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 動用審核表 FormHandler
 * </pre>
 * 
 * @since 2012/12/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/21,Fantasy,new
 *          <li>2013/06/20,Fantasy add dataCheck,checkC160M01B
 *          <li>2013/06/25,Fantasy fix deleteCase to setDeleteTime
 *          <li>2013/07/16,Rex,代償明細中原貸放日期，需判斷原 房貸資訊取得轉貸前後為相同性質之政策性貸款 為Y才需檢查
 *          </ul>
 */
@Scope("request")
@DomainClass(C160M01A.class)
@Controller("cls1161formhandler")
public class CLS1161FormHandler extends AbstractFormHandler {
	private static final int LAST_XLS_KEY = 30; // J-107-0248
	private static final Logger logger = LoggerFactory
			.getLogger(CLS1161FormHandler.class);
	
	@Resource
	UserInfoService uis;

	@Resource
	DocLogService docLogService;

	@Resource
	CLS1161Service service;

	@Resource
	CodeTypeService cts;

	@Resource
	CodeItemService cis;

	@Resource
	BranchService bs;

	@Resource
	MisdbBASEService mis;

	@Resource
	MisPTEAMAPPService mps;

	@Resource
	DocFileService dfs;

	@Resource
	LMSService lmsService;

	@Resource
	CLSService clsService;
	
	@Resource
	MisStoredProcService msps;

	@Resource
	MisCustdataService mcs;

	@Resource
	ProdService prodService;

	@Resource
	NumberService ns;

	@Resource
	CLS1141Service cls1141Service;

	@Resource
	CLS1131Service cls1131Service;

	@Resource
	EloandbBASEService eloandbbaseservice;
	
	@Resource
	SysParameterService sysParameterService;
	
	@Resource
	MisLNF030Service misLNF030Service;
	
	@Resource
	MisELF600Service misELF600Service;
	
	@Resource
	MisMISLN20Service mismisln20Service;
	
	@Resource
	CLS3401Service cls3401Service;
	
	@Resource
	DwdbBASEService dwdbBASEService;
	
	@Resource
	EjcicService ejcicService;
	
	@Resource
	BranchService branchService;
	
	@Resource
	EJCICGwClient ejcicClient;
	
	@Resource
	CodeTypeService codeService;
	
	Properties prop;

	Properties prop_CLS1161S02APage = MessageBundleScriptCreator.getComponentResource(CLS1161S02APage.class);
	Properties prop_CLS1161M01Page = MessageBundleScriptCreator.getComponentResource(CLS1161M01Page.class);
	
	@Resource
	CLS1161Service cls1161Service;
	
	@Resource
	CLS1131R04RptService cls1131R04RptService;
	
	@Resource
	RPAProcessService rpaProcessService;
	
	@Resource
	MisPTEAMAPPService misPTEAMAPPService;
	
	@Resource
	AMLRelateService amlRelateService;
	
	@Autowired
	HttpServletRequest httpServletRequest;

	// UPGRADETODO: JXL 改寫為 Apache POI - 添加輔助方法簡化 POI Excel 操作
	/**
	 * 設定 Excel 儲存格值和格式 (POI 版本) - 注意：JXL 的順序是 (x,y) 對應 POI 的 (colNum, rowNum)
	 * @param sheet Excel 工作表
	 * @param colNum 列號 (對應 JXL 的 x)
	 * @param rowNum 行號 (對應 JXL 的 y)
	 * @param value 值
	 * @param copyStyleFromCell 是否複製格式的參考儲存格
	 */
	private void setPOICellValue(Sheet sheet, int colNum, int rowNum, String value, Cell copyStyleFromCell) {
		// UPGRADETODO: JXL 改寫為 Apache POI - 加入 null 檢查防止 NullPointerException
		if (sheet == null) {
			throw new RuntimeException("Sheet cannot be null");
		}
		
		Row row = sheet.getRow(rowNum);
		if (row == null) {
			row = sheet.createRow(rowNum);
		}
		Cell cell = row.getCell(colNum);
		if (cell == null) {
			cell = row.createCell(colNum);
		}
		
		// 安全地設定儲存格值
		if (value != null) {
			cell.setCellValue(value);
		} else {
			cell.setCellValue("");
		}
		
		// 複製格式
		if (copyStyleFromCell != null && copyStyleFromCell.getCellStyle() != null) {
			cell.setCellStyle(copyStyleFromCell.getCellStyle());
		}
	}
	
	/**
	 * 取得或創建儲存格 (POI 版本) - 注意：JXL 的 getCell(x,y) 對應 POI 的 getRow(y).getCell(x) 
	 * @param sheet Excel 工作表
	 * @param colNum 列號 (對應 JXL 的 x)
	 * @param rowNum 行號 (對應 JXL 的 y)
	 * @return Cell 物件
	 */
	private Cell getPOICell(Sheet sheet, int colNum, int rowNum) {
		// UPGRADETODO: JXL 改寫為 Apache POI - 加入 null 檢查防止 NullPointerException
		if (sheet == null) {
			throw new RuntimeException("Sheet cannot be null");
		}
		
		Row row = sheet.getRow(rowNum);
		if (row == null) {
			row = sheet.createRow(rowNum);
		}
		Cell cell = row.getCell(colNum);
		if (cell == null) {
			cell = row.createCell(colNum);
		}
		return cell;
	}

	/**
	 * 新增案件
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult addCase(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		int txCode = Util.parseInt(params
				.getString(EloanConstants.TRANSACTION_CODE));
		String caseType = Util.trim(params.getString("caseType"));
		if(Util.equals(caseType, UtilConstants.Usedoc.caseType2.一般)
			|| Util.equals(caseType, UtilConstants.Usedoc.caseType2.團貸)
			|| Util.equals(caseType, UtilConstants.Usedoc.caseType2.整批匯入)){
			//ok
		}else{
			throw new CapMessageException("不合理的caseType["+caseType+"]", getClass());
		}
		String mainId = IDGenerator.getUUID();
		String clientIP = httpServletRequest.getRemoteHost();
		
		List<GenericBean> list = new ArrayList<GenericBean>();
		// 動用審核表主檔
		C160M01A c160m01a = new C160M01A();
		c160m01a.setCaseType(caseType);
		c160m01a.setMainId(mainId);
		c160m01a.setUnitType(user.getUnitType());
		c160m01a.setOwnBrId(user.getUnitNo());
		c160m01a.setCaseBrId(user.getUnitNo());
		c160m01a.setTypCd(TypCdEnum.DBU.getCode());
		c160m01a.setDocURL(URLConstant.個金_動用審核表);
		c160m01a.setDocStatus(CLSDocStatusEnum.編製中);
		c160m01a.setRandomCode(IDGenerator.getRandomCode());
		c160m01a.setTxCode(cis.getParentByCode(txCode));
		c160m01a.setIsUseChildren(UtilConstants.DEFAULT.否);
		c160m01a.setDeletedTime(CapDate.getCurrentTimestamp());
		c160m01a.setApprId(user.getUserId());
		c160m01a.setRptId(ClsUtil.C160M01A_RPTID_V20190920);
		list.add(c160m01a);
		// 動用審核表授權檔
		C160A01A c160a01a = new C160A01A();
		c160a01a.setMainId(mainId);
		c160a01a.setOwnUnit(user.getUnitNo());
		c160a01a.setOwner(user.getUserId());
		c160a01a.setAuthUnit(user.getUnitNo());
		c160a01a.setAuthTime(CapDate.getCurrentTimestamp());
		c160a01a.setAuthType(UtilConstants.Casedoc.L120a01aAuthType.編製_移送);
		list.add(c160a01a);
		// 先行動用呈核及控制表檔
		C160M01D c160m01d = new C160M01D();
		c160m01d.setMainId(mainId);
		c160m01d.setAppraiserId(user.getUserId());
		list.add(c160m01d);
		// 儲存
		service.save(list);
		if(true){
			try{
				clsService.inject_AMLRefNo(c160m01a, clientIP, Util.trim(user.getUserId()));
			}catch(CapException e){
				throw new CapMessageException(e.getMessage(), getClass());
			}
		}
		result.set("data", DataParse.toResult(c160m01a));
		return result;
	}// ;

//	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
//	public IResult test_01(PageParameters params, Component parent)
//			throws CapException {
//		CapAjaxFormResult result = new CapAjaxFormResult();
//
//		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
//		String caseType =  UtilConstants.Usedoc.caseType2.一般;
//		String mainId = IDGenerator.getUUID();
//		String clientIP = LMSUtil.getRemoteHost(((WebRequest) RequestCycle.get().getRequest()).getHttpServletRequest());
//		
//		C160M01A c160m01a = new C160M01A();
//		c160m01a.setCaseType(caseType);
//		c160m01a.setMainId(mainId);
//		c160m01a.setUnitType(user.getUnitType());
//		c160m01a.setOwnBrId(user.getUnitNo());
//		c160m01a.setCaseBrId(user.getUnitNo());
//		c160m01a.setTypCd(TypCdEnum.DBU.getCode());
//		c160m01a.setDocURL(URLConstant.個金_動用審核表);
//		c160m01a.setDocStatus(CLSDocStatusEnum.編製中);
//		c160m01a.setRandomCode("");
//		c160m01a.setTxCode("");
//		c160m01a.setIsUseChildren(UtilConstants.DEFAULT.否);
//		c160m01a.setDeletedTime(CapDate.getCurrentTimestamp());
//		c160m01a.setApprId(user.getUserId());
//		clsService.save(c160m01a);//在 CLS1160Service 會起 flowService.start(
//		
//		if(true){
//			try{
//				clsService.inject_AMLRefNo(c160m01a, clientIP, Util.trim(user.getUserId()));
//			}catch(CapException e){
//				throw new CapMessageException(e.getMessage(), getClass());
//			}
//		}
//		return result;
//	}
	
	/**
	 * 刪除案件
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult deleteCase(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String oid = Util.trim(params.getString(EloanConstants.OID));
		C160M01A c160m01a = service.findModelByOid(C160M01A.class, oid);
		if (c160m01a != null) {
			service.setDeleteTime(c160m01a);
			// c160m01a.setDeletedTime(CapDate.getCurrentTimestamp());
			// service.save(c160m01a);
		}

		return result;
	}// ;

	/**
	 * 讀取案件
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	@DomainAuth(AuthType.Query)
	public IResult load(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		int page = Util.parseInt(params.getString("page"));
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		// base form info
		C160M01A c160m01a = service.findModelByOid(C160M01A.class, oid);
		if (c160m01a != null) {
			CapAjaxFormResult baseForm = DataParse.toResult(c160m01a);
			baseForm.set("typCd", "DBU");
			CodeType ct = cts.findByCodeTypeAndCodeValue(
					ClsConstants.codeType.動審表種類, c160m01a.getCaseType());
			if(ct==null){
				throw new CapMessageException("caseType["+Util.trim(c160m01a.getCaseType())+"]非合理值"
						, getClass());
			}
			baseForm.set("caseTypeDesc", Util.trim(ct.getCodeDesc()));
			StringBuilder sb = new StringBuilder();
			sb.append(Util.trim(c160m01a.getCustId())).append(" ");
			sb.append(Util.trim(c160m01a.getDupNo())).append(" ");
			sb.append(Util.trim(c160m01a.getCustName()));
			baseForm.set("custInfo", sb.toString());
			result.set("baseForm", baseForm);
		}
		result.set("useType", Util.trim(c160m01a.getUseType()));

		switch (page) {
		case 1: // 文件資訊
		case 6: //IVR語音檔
			if (true) {
				StringBuilder cntrNo = new StringBuilder();
				List<C160M01B> list = clsService.findC160M01B_mainId(mainId);
				for (C160M01B c160m01b : list) {
					cntrNo.append(cntrNo.length() > 0 ? "、" : "");
					cntrNo.append(Util.trim(c160m01b.getCntrNo()));
				}

				if (c160m01a != null) {
					CapAjaxFormResult C160M01AForm = DataParse
							.toResult(c160m01a);
					C160M01AForm.set("ownBrIdName",
							Util.trim(bs.getBranchName(c160m01a.getOwnBrId())));
					C160M01AForm.set("docStatus", CLSDocStatusEnum
							.getMessage(c160m01a.getDocStatus()));
					C160M01AForm.set("cntrNo", cntrNo.toString());

					CodeType ct = cts.findByCodeTypeAndCodeValue(
							ClsConstants.codeType.授權等級,
							Util.trim(c160m01a.getCaseLvl()));
					if (ct != null)
						C160M01AForm.set("caseLvlCn", ct.getCodeDesc());

					List<C160M01E> c160m01eList = (List<C160M01E>) service
							.findListByMainId(C160M01E.class, mainId);
					StringBuilder sb = new StringBuilder();
					for (C160M01E c160m01e : c160m01eList) {
						if (UtilConstants.STAFFJOB.授信主管L3.equals(c160m01e
								.getStaffJob())) {
							sb.append(sb.length() > 0 ? "<br/>" : "");
							String staffNo = Util.trim(c160m01e.getStaffNo());
							sb.append(staffNo).append(" ");
							sb.append(uis.getUserName(staffNo));
						}
					}
					C160M01AForm.set("bossId", sb.toString());
					C160M01AForm.set("managerNm",
							uis.getUserName(c160m01a.getManagerId()));
					C160M01AForm.set("reCheckNm",
							uis.getUserName(c160m01a.getReCheckId()));
					C160M01AForm.set("apprNm",
							uis.getUserName(c160m01a.getApprId()));
					C160M01AForm.set("updater",
							uis.getUserName(c160m01a.getUpdater()));
					C160M01AForm.set("creator",
							uis.getUserName(c160m01a.getCreator()));
					result.set("C160M01AForm", C160M01AForm);
					result.set("CaseType3Form", C160M01AForm);
				}
			}
			break;
		case 2: // 額度明細
			if (true) {
				CapAjaxFormResult totalForm = new CapAjaxFormResult();

				this._calLoanTotalData(totalForm, mainId);

				result.set("totalForm", totalForm);
			}
			break;
		case 3: // 動用審核表
			if (true) {
				List<C160M01C> list = clsService.findC160M01C_mainId(mainId);
				String jsonStr = JsonMapper.toJSON(list);
				result.set("detials", jsonStr);
				result.set("C160M01AForm", DataParse.toResult(c160m01a));
			}
			break;
		case 4: // 先行動用呈核及控制表
			C160M01D c160m01d = service.findModelByMainId(C160M01D.class,
					mainId);
			if (c160m01d != null) {
				CapAjaxFormResult C160M01DForm = DataParse.toResult(c160m01d);
				C160M01DForm.set("appraiserNm",
						uis.getUserName(c160m01d.getAppraiserId()));
				C160M01DForm.set("bossNm",
						uis.getUserName(c160m01d.getBossId()));
				if (Util.isEmpty(c160m01d.getManagerNm())) {
					c160m01d.setManagerNm(uis.getUserName(c160m01d
							.getManagerId()));
				}
				result.set("C160M01DForm", C160M01DForm);
			}
			break;
		case 5: // AML
			List<L120S09A> l120s09a_list = clsService.findL120S09A(mainId);
			boolean active_SAS_AML = clsService.active_SAS_AML(c160m01a);
			//=============
			String refNo =  Util.trim(c160m01a.getAmlRefNo()); //抓 c160m01a
			Date blackListQDate = null;			
			String ncResult = "";			
			String uniqueKey = "";
			String ncCaseId = "";
			
			if(active_SAS_AML){
				L120S09B l120s09b = clsService.findL120S09B_mainId_latestOne(mainId);
				if(l120s09b!=null){
					blackListQDate = l120s09b.getQueryDateS();			
					ncResult = Util.trim(l120s09b.getNcResult());					
					uniqueKey = Util.trim(l120s09b.getUniqueKey());
					ncCaseId = Util.trim(l120s09b.getNcCaseId());
					refNo = Util.trim(l120s09b.getRefNo()); // P-108-0046 可能會在原 reNo 後加上 -Util.getRightStr(l120s09b_uniqueKey, 6);
				}else{
					//舊版
					if(LMSUtil.lackBlackCode(l120s09a_list)){
						// keep null
					}else{
						blackListQDate = LMSUtil.maxQueryDateS(l120s09a_list);	
					}	
				}
			}else{
				//舊版
				if(LMSUtil.lackBlackCode(l120s09a_list)){
					// keep null
				}else{
					blackListQDate = LMSUtil.maxQueryDateS(l120s09a_list);	
				}
			}
			
			
			if(true){
				CapAjaxFormResult CLS1201S20Form = new CapAjaxFormResult();
				//~~~~~~
				CLS1201S20Form.set("blackListQDate", Util.trim(TWNDate.toAD(blackListQDate)));	
				ClsUtil.inject_SAS_AML(CLS1201S20Form, ncResult, refNo, uniqueKey, ncCaseId,
						"N", "N", "", "");
				//~~~~~~
				result.set("CLS1201S20Form", CLS1201S20Form);
			}
			break;
		}

		result.set("useProd69Fmt", service.useProd69Fmt(mainId));
		return result;
	}// ;

	/**
	 * 儲存
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	@DomainAuth(AuthType.Modify)
	public IResult save(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		List<GenericBean> saveList = new ArrayList<GenericBean>();
		int page = Util.parseInt(params.getString("page"));
		switch (page) {
		case 2: // 額度明細
			try {
				if (!params.containsKey("prompt")) {
					params.add("prompt", UtilConstants.DEFAULT.是);
					sendBeforeCheck(params);
				}
			} catch (CapMessageException e) {
				result.set("message", e.getMessage());
			}

		case 3: // 動用審核表
			String useType = UtilConstants.DEFAULT.否;
			StringBuilder sb = new StringBuilder();
			String detials = Util.trim(params.getString("detials"));
			JSONObject datas = DataParse.toJSON(detials);
			List<C160M01C> list = (List<C160M01C>) service.findListByMainId(
					C160M01C.class, mainId);
			for (C160M01C c160m01c : list) {
				JSONObject json = (JSONObject) datas.get(c160m01c.getOid());
				if (json != null) {
					DataParse.toBean(json, c160m01c);
					c160m01c.setMainId(mainId);
					Object obj = json.get("itemFormat");
					if (obj instanceof JSONArray)
						c160m01c.setItemFormat(((JSONArray) obj).toString());

					if (Util.isNotEmpty(c160m01c.getItemContent())) {
						if (UtilConstants.Usedoc.checkItem.未收.equals(Util
								.trim(c160m01c.getItemCheck()))) {
							sb.append(sb.length() > 0 ? "、" : "").append(
									Util.trim(c160m01c.getItemDscr()));
						}
					}
				}
				// 0免附 1已收到 2未收到
				if (Util.trim(c160m01c.getItemCheck()).matches("[2]"))
					useType = UtilConstants.DEFAULT.是;

			}
			saveList.addAll(list); // add save list

			if (true) {
				C160M01D c160m01d = service.findModelByMainId(C160M01D.class,
						mainId);
				if (c160m01d == null) {
					c160m01d = new C160M01D();
					c160m01d.setMainId(mainId);
					c160m01d.setAppraiserId(MegaSSOSecurityContext.getUserId());
				}
				if (sb.length() > 0)
					sb.append(getI18nMsg("C160M01C.warning"));
				if (Util.isEmpty(c160m01d.getWaitingItem()))
					c160m01d.setWaitingItem(sb.toString());
				saveList.add(c160m01d); // add save list
			}

			String form = Util.trim(params.getString("C160M01AForm"));
			JSONObject json = DataParse.toJSON(form);
			if (!json.isEmpty()) {
				String oid = Util.trim(json.get(EloanConstants.OID));
				C160M01A c160m01a = service.findModelByOid(C160M01A.class, oid);
				if (c160m01a != null) {
					DataParse.toBean(json, c160m01a);
					c160m01a.setUseType(useType); // 是否先行動用
					c160m01a.setRandomCode(IDGenerator.getRandomCode());
					saveList.add(c160m01a); // add save list
				}
			}
			result.set("useType", useType);
			break;
		case 4:
			if (true) {
				C160M01D c160m01d = service.findModelByMainId(C160M01D.class,
						mainId);
				if (c160m01d != null) {
					String C160M01DForm = Util.trim(params
							.getString("C160M01DForm"));
					JSONObject C160M01DJson = DataParse.toJSON(C160M01DForm);
					DataParse.toBean(C160M01DJson, c160m01d);
					saveList.add(c160m01d);
				}
			}
		default:
			break;
		}
		C160M01E c160m01e = clsService.findC160M01E(mainId, null, "L1");
		if (!Util.isEmpty(c160m01e)) {
			c160m01e.setStaffNo(MegaSSOSecurityContext.getUserId());
		}
		C160M01A c160m01a = clsService.findC160M01A_mainId(mainId);
		c160m01a.setApprId(MegaSSOSecurityContext.getUserId());
		c160m01a.setUpdater(MegaSSOSecurityContext.getUserId());
		c160m01a.setUpdateTime(CapDate.getCurrentTimestamp());
		
		//J-109-0150_10702_B1001 IVR頁籤由模擬動審移至動審表
		if(!"tempSave".equals(Util.trim(params.getString("formAction")))){
			List<C160M01B> c160m01bs = (List<C160M01B>) service
			.findListByMainId(C160M01B.class, c160m01a.getMainId());
			for(C160M01B c160m01b:c160m01bs){
				String reMainId=c160m01b.getRefmainId();
				L140M01A l140m01a = service.findByMainId(reMainId);
				String custName = l140m01a.getCustName();
				String projClass = l140m01a.getProjClass();
				if(Util.isEmpty(c160m01b.getIVRFlag())){
					if (Util.equals(projClass, LMSUtil.特定金錢信託受益權自行設質擔保授信)) {
						throw new CapMessageException(
								RespMsgHelper.getMessage("EFD0005",
								MessageFormat.format(
										prop_CLS1161M01Page.getProperty("cls3301v00.error.01"),
										new StringBuffer().append(l140m01a.getCustName()).toString(),
										new StringBuffer().append(l140m01a.getCntrNo()).toString())),
								getClass());
					}
//					if(LMSUtil.isContainValue(Util.trim(l140m01a.getProPerty()),
//							UtilConstants.Cntrdoc.Property.變更條件)
//							|| LMSUtil.isContainValue(Util.trim(l140m01a.getProPerty()),
//							UtilConstants.Cntrdoc.Property.增額)){
//						Map<String, Object> map = eloandbbaseservice
//								.findLastTimeCaseByCutIdAndCntrNoOrdByUpdateTime(
//										l140m01a.getCustId(), l140m01a.getDupNo(), Util.trim(l140m01a.getCntrNo()), UtilConstants.Casedoc.DocType.個金, Util.trim(l140m01a.getMainId()));
//						L140M01A l140m01a_old = null;
//						String oldL140M01AmainId = "";
//						if (map != null) {
//							oldL140M01AmainId = Util.trim(map.get("MAINID"));
//							l140m01a_old = service.findModelByMainId(L140M01A.class,oldL140M01AmainId);
//						}
//						if(Util.isNotEmpty(l140m01a_old)){
//							if(!Util.equals(l140m01a.getCurrentApplyCurr(),l140m01a_old.getCurrentApplyCurr())){
//								throw new CapMessageException(RespMsgHelper.getMessage(parent,
//										"EFD0005", MessageFormat.format(
//												prop_CLS1161M01Page.getProperty("cls3301v00.error.02"),
//												new StringBuffer().append(l140m01a.getCustName()).toString(),
//												new StringBuffer().append(l140m01a.getCntrNo()).toString())),
//										getClass());
//							}
//						}
//					}
				}
			}
		}
		
		service.save(c160m01a);
		service.save(params, saveList);
		return result;
	}// ;

	/**
	 * 暫存
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult tempSave(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				UtilConstants.DEFAULT.是);
		save(params);

		return result;
	}// ;

	/**
	 * 引進案件
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult importCase(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		// 刪除相關之所有資資料
		List<GenericBean> delList = new ArrayList<GenericBean>();
		for (Class<?> clazz : ClsUtil.C160SClass) {
			delList.addAll(service.findListByMainId(clazz, mainId));
		}
		delList.addAll(service.findListByMainId(C160M01B.class, mainId));
		service.delete(delList);

		//J-110-0233_10702_B1002 Web e-Loan檢核勞工紓困未提權網銀帳號不得線上對保
		String caseMainid =Util.trim(params.getString("caseMainId"));
		L120M01A l120m01a = clsService.findL120M01A_mainId(caseMainid);
		if(l120m01a!=null){
			if(Util.equals(l120m01a.getSimplifyFlag(), "C")){
				C160M01A c160m01a = clsService.findC160M01A_mainId(mainId);
				if(Util.equals(c160m01a.getCaseType(),UtilConstants.Usedoc.caseType2.一般)){
					String AmlRefNo = c160m01a.getAmlRefNo();
					if(!AmlRefNo.endsWith("-P")){
						c160m01a.setAmlRefNo(c160m01a.getAmlRefNo() + "-P");
						clsService.save(c160m01a);
					}
				}
				// 取得帳戶中額度序號
				List<MISLN20> Cntrnos = mismisln20Service
						.findByCustId(l120m01a.getCustId(),l120m01a.getDupNo());
				//J-110-0233_10702_B1006 Web e-Loan增加動審表增加家事查詢結果檢核，照會檢核排除已有提權、實體存戶
				List<Map<String, Object>> accounts = dwdbBASEService.getAccount_laborContract(l120m01a.getCustId(),l120m01a.getDupNo());
				int isNonAuthBankAcc=0;
				if (accounts != null && accounts.size() > 0) {
					Map<String, String> exclude_dpCate = clsService.get_codeTypeWithOrder("c340_ctrTypeA_exclude_dpCate");
					for (Map<String, Object> dataRow : accounts) {
						String accoutNo = MapUtils.getString(dataRow, "MR_PB_ACT_NO", "");
						String flag = MapUtils.getString(dataRow, "MR_PB_MSAC_FLAG", "");
						String dpCate = StringUtils.substring(accoutNo, 3, 5); //存款帳號第4~5碼是存款種類
						/*
						  例如：09-公司戶, 66-黃金存摺
						*/
						if(exclude_dpCate.containsKey(dpCate)){
							isNonAuthBankAcc++;
							continue;
						}
						//未提權網銀存戶
						if(Util.equals(flag, "1") || Util.equals(flag, "2") || Util.equals(flag, "3")){
							isNonAuthBankAcc++;
						}
					}
				}
				//查無貸款戶
				if(Cntrnos.size()==0){
					//無實體帳號或數存提權帳號
					if(isNonAuthBankAcc==accounts.size()){
						if(!Util.equals(c160m01a.getNewCustFlag(), UtilConstants.DEFAULT.是)){
							c160m01a.setNewCustFlag(UtilConstants.DEFAULT.是);
							clsService.save(c160m01a);
							throw new CapMessageException("此ID為全新客戶，請經辦照會客戶後，再送呈主管覆核!", getClass());
						}
					}
				}
			}
		}
		
		// 匯入
		service.importCase(params);
		// 匯入檢附資訊檔明細檔
		importC160M01C(params);

		getL140M01RByC160M01A(params);

		if(true){
			service.sync_C160M01A_totAmt(mainId);
		}
		
		if(service.is_only_apply_prod69(mainId)){
			C160M01A c160m01a = clsService.findC160M01A_mainId(mainId);
			c160m01a.setComm("貸放手續齊全，擬准予動用。");
			clsService.daoSave(c160m01a);
			//~~~~~~~~~~~~~~~~~~~~~~~~
		}
		
		if(clsService.is_function_on_codetype("auto_imp_joinMarkDesc")){ //=> 允許切換Active(萬一 MIS 回應緩慢，才不會造成 e-Loan 新增動審表 被卡住)
			//自0024引入「共用行銷」
			for(C160M01B c160m01b : clsService.findC160M01B_mainId(mainId)){
				LinkedHashMap<String, JSONObject> map = new LinkedHashMap<String, JSONObject>(); 
				if (true) {
					JSONObject borrower_M = new JSONObject();
					String custId = Util.trim(c160m01b.getCustId());
					String dupNo = Util.trim(c160m01b.getDupNo());
					String custName = Util.trim(c160m01b.getCustName());
					borrower_M.put("custId", custId);
					borrower_M.put("dupNo", dupNo);
					borrower_M.put("custName", custName);
					map.put(LMSUtil.getCustKey_len10custId(custId, dupNo), borrower_M);
				}
				for(C160S01B c160s01b : clsService.findC160S01B(mainId, c160m01b.getRefmainId())){
					JSONObject borrower_oth = new JSONObject();
					String custId = Util.trim(c160s01b.getRId());
					String dupNo = Util.trim(c160s01b.getRDupNo());
					String custName = Util.trim(c160s01b.getRName());
					borrower_oth.put("custId", custId);
					borrower_oth.put("dupNo", dupNo);
					borrower_oth.put("custName", custName);
					map.put(LMSUtil.getCustKey_len10custId(custId, dupNo), borrower_oth);
				}
				JSONObject custJson = new JSONObject();
				if(true){
					for(String idDup: map.keySet()){
						custJson.put(idDup, map.get(idDup));
					}
				}
				c160m01b.setJoinMarketingDate(new Date());
				c160m01b.setJoinMarkDesc(build_joinMarkDesc(custJson));
				clsService.daoSave(c160m01b);
			}
		}
		return result;
	}// ;

	/**
	 * 重新引進額度明細表
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult importC160M01B(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		
		// 驗證是否有可引進的額度明細資料
		List<C160M01B> c160m01bList = (List<C160M01B>) service.findListByMainId(C160M01B.class, mainId);
		if (c160m01bList.isEmpty()) {
			throw new CapMessageException("沒有找到可引進的額度明細資料", getClass());
		}
		
		// 驗證額度明細是否有對應的L140M01A資料
		int validRecordCount = 0;
		for (C160M01B c160m01b : c160m01bList) {
			String refMainId = Util.trim(c160m01b.getRefmainId());
			if (!Util.isEmpty(refMainId)) {
				L140M01A l140m01a = service.findModelByMainId(L140M01A.class, refMainId);
				if (l140m01a != null) {
					validRecordCount++;
				}
			}
		}
		
		if (validRecordCount == 0) {
			throw new CapMessageException("額度明細資料中沒有有效的參照資料，無法執行引進作業", getClass());
		}
		
		// 刪除額度明細表相關之資料
		// 包含[C160S01A, C160S01B, C160S01C, C160S01E.class, C160S01F]
		// 沒有 C160M01B(=L140M01A)
		List<GenericBean> delList = new ArrayList<GenericBean>();
		for (Class<?> clazz : ClsUtil.C160SClass) {
			delList.addAll(service.findListByMainId(clazz, mainId));
		}
		service.delete(delList);

		//在 動審表>額度明細 頁籤, 執行「重新引進」
		/*
		 * 當1份簽報書, 包含2個額度
		 * (1)第一次引入全部的2個額度
		 * (2)在 動審表>額度明細 頁籤, 執行「重新引進」右邊的「刪除」, 刪掉其中一個額度
		 * (3)執行「重新引進」時, 只會重引 "剩下未刪除的額度"
		 */
		service.importCase(mainId);

		if(true){
			service.sync_C160M01A_totAmt(mainId);
		}
		
		// 設定成功訊息
		result.set("importedCount", validRecordCount);
		result.set("message", "成功引進 " + validRecordCount + " 筆額度明細資料");
		
		return result;
	}// ;

	/**
	 * 重新引進借保人
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult importC160S01B(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String refmainId = Util.trim(params.getString("refmainId"));
		service.save(service.findC160S01BList(mainId, refmainId));

		return result;
	}// ;

	/**
	 * 重新引進檢附資訊檔明細檔
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult importC160M01C(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		List<C160M01C> list = prodService.getC160M01C(mainId);
		if(true){ 
			
			Map<String, C160M01C> map_c160s01c = convert_C160M01C_itemCode_obj(list);
			if(service.is_only_apply_prod69(mainId)){
				/*1	借款契約書
				2	增補契約條款
				3	借支書
				4	授權書、本票
				5	他項權利證明書及設定契約書
				6	建物、土地權狀影本
				7	建物、土地謄本
				8	擔保借款抵押權設定種類約款
				9	保單正本及收據副本 				
				10	借款人撥款委託書
				11	「轉貸款約定書」
				12	撥款約定書
				13	承諾書
				14	同意書
				15	租賃合約書(自住則免)
				16	委任書
				17	切結書
				18	銀行法第12條之1之自用住宅放款－「房屋財產查核清單」或「財產歸屬資料清單」
				19	非屬自用住宅放款之借款人(或擔保物提供人)相關佐證資料				
				20	一般保證人宣告書
				21	連帶保證人宣告書
				22	「本行員工購屋貸款、房屋修繕貸款及理財型房屋貸款」員工及配偶合計額度不逾新台幣1千5百萬元同意書
				23	兆豐金控集團現職員工購屋貸款、房屋修繕貸款及理財型房屋貸款」現職員工本人及配偶貸款額度未逾新台幣1千5百萬元之同意書
				24	銀行法及金控法利害關係人查詢紀錄，若為利害關係人授信案件，應填寫「辦理關係人授信交易檢核表」【房貸或其他十足擔保之個人放款，其條件不優於其他同類授信對象，且個人歸戶後 (含本案)之授信金額(循環信用額度應以額度計算)在新台幣一億元(不含)以下之案件，於簽約時方可免再做查詢。】
				25	司法院網站借款人及保證人是否為受監護或受輔助宣告資料				
				38	「歡喜理財家或綜合理財房屋貸款」宣告書
				39	「循環動用之理財型貸款或循環動用之消費性貸款」宣告書				
				40	個人購屋貸款日後增新貸同意書
				41	個人授信戶授信總額度達新台幣叁仟萬元者或其擔任董監事、經理人之企業於本行授信額度達新台幣壹億元(含)以上或為集團企業者，應請客戶提供「同一關係人資料表」，並據以建入「e-Loan系統→資料建檔維護系統」及確認該戶之「同一關係人」在本行授信情形有無符合規定。
				43	不動產使用狀況暨擔保借款抵押權設定種類聲明書
				44	簽案或動審時是否至少擇一填寫「銀行公會疑似洗錢或資恐交易態樣檢核表-授信」				
				*/
				for(C160M01C c160m01c : list){
					c160m01c.setItemCheck(UtilConstants.Usedoc.checkItem.免收);//default 免收
				}
				
				//消金處明澤說借支及本票可以免收
				String[] arr_1 = new String[]{"1", "25", "44", "47", "48", "49", "50"};
				for(String itemCode : arr_1){
					C160M01C c160m01c = map_c160s01c.get(itemCode);
					if(c160m01c!=null){
						c160m01c.setItemCheck(UtilConstants.Usedoc.checkItem.已收);
					}
				}
				if(true){
					boolean is_item24_V = true;
					/*if(clsService.is_function_on_codetype("c160s01c_item24_prod69_alwaysY")){
						is_item24_V = true;
					}else{						
						for(String idDup: prod69_idDup){
							String custId = StringUtils.substring(idDup, 0, 10);
							String dupNo = StringUtils.substring(idDup, 10);
							PageParameters q1_params = new PageParameters();
							q1_params.put("custId", custId);
							q1_params.put("dupNo", dupNo);
							q1_params.put("type", ClsConstants.C101S01E.本行利害關係人);
							
							PageParameters q2_params = new PageParameters();
							q2_params.put("custId", custId);
							q2_params.put("dupNo", dupNo);
							q2_params.put("type", ClsConstants.C101S01E.金控利害關係人44條);
							
							C101S01J c101s01j = new C101S01J();
							List<GenericBean> dataList = new ArrayList<GenericBean>();
							JSONObject r1 = cls1131Service.queryData(dataList, null, c101s01j, q1_params);
							JSONObject r2 = cls1131Service.queryData(dataList, null, c101s01j, q2_params);
							
							if(Util.equals(c101s01j.getQresult(), "Y")
									|| Util.equals(c101s01j.getXQResult(), "Y")){
								is_item24_V = true;
								break;
							}
						}			
					}*/
					//~~~~~~~~~~~~~~~~~~~~~~
					if(is_item24_V){
						C160M01C c160m01c = map_c160s01c.get(UtilConstants.C900S01B_ITEMCODE.第24項);
						if(c160m01c!=null){
							c160m01c.setItemCheck(UtilConstants.Usedoc.checkItem.已收);
						}
					}					
				}
				
				
				if(true){
				C160M01C c160m01c = map_c160s01c.get("1");
				if(c160m01c!=null){
					JSONObject itemFmtJSON_1 = new JSONObject();
					itemFmtJSON_1.put("value", "1");
					itemFmtJSON_1.put("titleName", "式");
					
					JSONObject itemFmtJSON_2 = new JSONObject();
					itemFmtJSON_2.put("value", "2");
					itemFmtJSON_2.put("titleName", "份");
					
					JSONArray itemArr = new JSONArray();
					itemArr.add(itemFmtJSON_1);
					itemArr.add(itemFmtJSON_2);
					c160m01c.setItemFormat(itemArr.toString());
					c160m01c.setItemDscr(Util.trim(c160m01c.getItemContent())+"1式2份");
				}
					
				}
				
				if(map_c160s01c.get("47")!=null){ //只呈現 勞工紓困 的必要選項
					if(true){
						C160M01C c160m01c = map_c160s01c.get("4");
						if(c160m01c!=null){
							c160m01c.setItemContent("本票及授權書"); //與 default 字串不同
							c160m01c.setItemDscr(c160m01c.getItemContent());
						}
					}
					
					if(true){
						C160M01C c160m01c = map_c160s01c.get(UtilConstants.C900S01B_ITEMCODE.第24項);
						if(c160m01c!=null){
							c160m01c.setItemContent("銀行法及金控法利害關係人查詢紀錄(簽約當日)"); //與 default 字串不同
							c160m01c.setItemDscr(c160m01c.getItemContent());
						}
					}
					
					if(true){
						C160M01C c160m01c = map_c160s01c.get("47");
						if(c160m01c!=null){
							//與 default 不同
							//若混合動用[勞工紓困、一般額度]，檢附文件的順序，會跳動
							//當純[勞工紓困]，檢附文件的項目固定 => 這時候, 才適合去加上  (註：.....) 的補充說明
							c160m01c.setItemContent(Util.trim(c160m01c.getItemContent())+"(註：7~10 係確認信保勞動部勞工貸款紓困各要件)"); 
							c160m01c.setItemDscr(c160m01c.getItemContent());
						}
					}
					
					list = new ArrayList<C160M01C>();
					String[] arr_prod69_beed = new String[]{"1", "3", "4", UtilConstants.C900S01B_ITEMCODE.第24項, "25", "44", "47", "48", "49", "50", "53"};
					for(String itemCode : arr_prod69_beed){
						list.add(map_c160s01c.get(itemCode));
					}
					
					int seq = 90001;
					// 自訂項目
					for (int i = 0; i < 4; i++) {
						C160M01C c160m01c = new C160M01C();
						c160m01c.setItemType("3"); //自行輸入項目
						c160m01c.setMainId(mainId);
						c160m01c.setOid(null);
						c160m01c.setItemSeq(seq + i);
						c160m01c.setCreateTime(CapDate.getCurrentTimestamp());
						c160m01c.setCreator(user.getUserId());
						c160m01c.setUpdater(user.getUserId());
						c160m01c.setUpdateTime(CapDate.getCurrentTimestamp());
						list.add(c160m01c);
					}
				}
			}
			else{
				
				//56 -「央行管制」購屋(或購地)案件撥款當日檢核表 檢核項目
				boolean isAddAllocateFundsCheckList = this.service.isAddAllocateFundsCheckList(mainId, false);
				C160M01C c160m01c56 = map_c160s01c.get(UtilConstants.C900S01B_ITEMCODE.第56項);
				
				if(isAddAllocateFundsCheckList){
					c160m01c56.setItemCheck(UtilConstants.Usedoc.checkItem.已收);
					list.add(c160m01c56);
				}
				
				if(!isAddAllocateFundsCheckList){
					list.remove(c160m01c56);
				}
			}
		}
		service.delete(service.findListByMainId(C160M01C.class, mainId));
		service.save(list);

		return result;
	}// ;

	private Map<String, C160M01C> convert_C160M01C_itemCode_obj(List<C160M01C> list){
		Map<String, C160M01C> map = new HashMap<String, C160M01C>();
		for(C160M01C c160m01c : list){
			map.put(c160m01c.getItemCode(), c160m01c);
		}
		return map;
	}
	
	/**
	 * 讀取明細資料
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Query)
	public IResult loadDetial(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String oid = Util.trim(params.getString(EloanConstants.OID));
		// String formName = Util.trim(params.getStringArray("formName"));
		String[] formArray = params.getStringArray("formName");

		for (String formName : formArray) {
			GenericBean model = getModel(formName, oid);
			// output
			if (model != null) {
				CapAjaxFormResult formResult = DataParse.toResult(model);
				if (ClsConstants.FORM.動審表_主檔.equals(formName)) {
					formResult.set("typCd", "DBU");
					C160M01D c160m01d = service.findModelByMainId(
							C160M01D.class,
							Util.trim(model.get(EloanConstants.MAIN_ID)));
					if (c160m01d != null)
						result.set("C160M01DForm", DataParse.toResult(c160m01d));
				} else if (ClsConstants.FORM.動審表_額度明細表.equals(formName)) {
					BigDecimal loanTotAmt = BigDecimal.ZERO;
					// String mainId =
					// Util.trim(model.get(EloanConstants.MAIN_ID));
					C160M01B c160m01b = (C160M01B) model;
					loanTotAmt = c160m01b.getLoanTotAmt();
					add_s_loanTotAmt(formResult, loanTotAmt);
				} else if (ClsConstants.FORM.動審表_擔保品資料.equals(formName)) {
					C160S01A c160s01a = (C160S01A) model;
					formResult.set("buildHtml",
							ClsUtil.getBuildHtml(c160s01a.getBuild()));
					formResult
							.set("areaDetailHtml",
									ClsUtil.getAreaDetailHtml(c160s01a
											.getAreaDetail()));

				} else if (ClsConstants.FORM.動審表_主從債務人資料.equals(formName)) {

				} else if (ClsConstants.FORM.動審表_個金產品種類.equals(formName)) {
					Map<String, String> prodKindNameMap = prodService.getProdKindName();
					Map<String, String> subCodeMap = prodService.getSubCode();
					
					C160S01C c160s01c = (C160S01C) model;
					if(c160s01c.getPenaltyMaxContTm()==null){
						/*
						 	當 penaltyMaxContTm ==null時, 目前寫法
						 	
						 		CapAjaxFormResult formResult = DataParse.toResult(model);
						 		
						 	回傳的 ajax response 不會包含 penaltyMaxContTm
						 	=> 增加判斷這種狀況, 讓 responseJSON 能包含 penaltyMaxContTm, 讓前端 js 在 setValue(...)時能填入 ServerSide的值
						 */
						formResult.set("penaltyMaxContTm", "");	
					}
					
					// start 2013/07/16,Rex,代償明細中原貸放日期，需判斷原房貸資訊取得轉貸前後為相同性質之政策性貸款
					// 為Y才需檢查
					L140S02A l140s02a = clsService.findL140S02A_by_C160S01C(c160s01c);
					
					String prodKindNm = Util.trim(prodKindNameMap.get(Util.trim(formResult.get("prodKind"))));
					if(l140s02a!=null){
						String disasType = Util.trim(l140s02a.getDisasType());
						if(Util.isNotEmpty(disasType)){
							Map<String, String> codeMap = clsService.get_disasType_desc();
							prodKindNm = prodKindNm+"-"+LMSUtil.getDesc(codeMap, disasType);
						}
					}
					formResult.set("prodKindNm", prodKindNm);
					formResult.set("subjCodeNm", Util.trim(subCodeMap.get(Util
							.trim(formResult.get("subjCode")))));
					formResult.set("efctBHName", bs.getBranchName(Util
							.trim(formResult.get("efctBH"))));
					
					
					String L140S02F_FavChgCase = "";
					if (l140s02a != null) {
						L140S02F l140s02f = l140s02a.getL140S02F();
						if (l140s02f != null) {
							L140S02F_FavChgCase = Util.trim(l140s02f
									.getFavChgCase());
						}
					}
					result.set("L140S02F_FavChgCase", L140S02F_FavChgCase);
					// end 2013/07/16,Rex,代償明細中原貸放日期，需判斷原
					// 房貸資訊取得轉貸前後為相同性質之政策性貸款為Y才需檢查
					C160S01E c160s01e = c160s01c.getC160s01e();
					if (c160s01e == null) {
						c160s01e = new C160S01E();
						c160s01e.setMainId(c160s01c.getMainId());
						c160s01e.setRefmainId(c160s01c.getRefmainId());
						c160s01e.setSeq(c160s01c.getSeq());
						service.save(c160s01e);
					}
					result.set(ClsConstants.FORM.動審表_代償轉貸借新還舊主檔,
							DataParse.toResult(c160s01e));
				} else if (ClsConstants.FORM.動審表_代償轉貸借新還舊主檔.equals(formName)) {

				} else if (ClsConstants.FORM.動審表_代償轉貸借新還舊明細.equals(formName)) {

				}
				result.set(formName, formResult);
			}
		}
		return result;
	}// ;

	/**
	 * 儲存明細資料
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult saveDetial(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		List<GenericBean> saveList = new ArrayList<GenericBean>();
		StringBuilder message = new StringBuilder();
		
		// String formName = Util.trim(params.getString("formName"));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String refmainId = Util.trim(params.getString("refmainId"));
		
		String seq = Util.trim(params.getString("seq"));
		String custId = Util.trim(params.getString("custId"));
		String[] formArray = params.getStringArray("formName");

		for (String formName : formArray) {
			JSONObject json = DataParse.toJSON(Util.trim(params
					.getString(formName)));
			json.put(EloanConstants.MAIN_ID, mainId); // for add
			json.put("refmainId", refmainId); // for add
			json.put("custId", custId); // for add
			json.put("seq", seq); // for add
			String oid = Util.trim(json.get(EloanConstants.OID));
			GenericBean model = getModel(formName, oid);

			if (ClsConstants.FORM.動審表_個金產品種類.equals(formName)) {
				C160S01C c160s01c = (C160S01C) model;

				if(c160s01c==null){
					throw new CapMessageException("oid="+oid+" 查無「個金產品種類檔」", getClass());
				}
				String tProdKind = Util.trim(c160s01c.getProdKind());

				if (Util.equals(ProdKindEnum.青年安心成家貸款_第二案_59.getCode(), tProdKind)) {
					// 在FORM中取值
					JSONObject c160s01cJson = JSONObject.fromObject(params
							.getString("C160S01CForm"));
					String tGetDate = Util.trim(c160s01cJson
							.getString("getDate"));
					Date getDay = CapDate.parseDate(tGetDate);

					L140S02A l140s02a = clsService.findL140S02A_by_C160S01C(c160s01c);
					if (l140s02a != null) {
						L140S02F l140s02f = l140s02a.getL140S02F();
						if (l140s02f != null) {
							String errStr = this._checkProdKind59(l140s02f,
									getDay);
							if (Util.isNotEmpty(errStr)) {
								throw new CapMessageException(errStr,
										getClass());
							}
						}
					}
				}

				//J-113-0511 web eLoan國內消金房貸額度檢核新作、購置不動產之產品，超過動用期需填逾期原因
				if(clsService.is_function_on_codetype("cls_overdueRec_to_RPS")){
					L140S02A l140s02a = clsService.findL140S02A_by_C160S01C(c160s01c);
					C160M01A c160m01a = clsService.findC160M01A_mainId(mainId);
					if (UtilConstants.Cntrdoc.Property.新做.equals(c160s01c.getProperty())) {
						if (Util.equals(l140s02a.getLnPurs(),"1")) {
							List<Map<String, Object>> list = eloandbbaseservice
									.findRpsO140m01aByCustIdandCntrNo(c160m01a.getCustId(),c160m01a.getDupNo(),c160s01c.getCntrNo());
							if (list.size()>0) {
								//取最新一筆已核准RPS預約額度
								Map<String, Object> o140s01a = list.get(0);
								String exceptedEmployMon = MapUtils.getString(o140s01a, "EXCEPTEDEMPLOYMON", "");
								if (Util.isNotEmpty(exceptedEmployMon) && exceptedEmployMon.split("-").length==2) {
									Date employMon = CapDate.addMonth(Util.parseDate(exceptedEmployMon.split("-")[0]+"-"+exceptedEmployMon.split("-")[1]+"-01"),1);
									Date lnStartDate = Util.parseDate(Util.trim(json.getString("lnStartDate")));
									if (LMSUtil.cmpDate(lnStartDate, ">=", employMon)) {
										if (Util.isEmpty(CapString.trimFullSpace(Util.trim(json.getString("overdueReason"))))) {
											throw new CapMessageException("此產品為購置不動產案件，授信起日超過預約額度之預計動撥月，請填寫逾期原因", getClass());
										} else {
											//把資料寫回RPS.o140s01a
											for (Map<String, Object> rpsRow : list) {
												String o140s01aMainId = MapUtils.getString(rpsRow, "MAINID", "");
												if (Util.isNotEmpty(o140s01aMainId)) {
													eloandbbaseservice.updateRpsO140S01A("Y", Util.trim(json.getString("overdueReason")), o140s01aMainId);
												}
											}
										}
									} else {
										//把資料寫回RPS.o140s01a
										for (Map<String, Object> rpsRow :list) {
											String o140s01aMainId = MapUtils.getString(rpsRow, "MAINID", "");
											if (Util.isNotEmpty(o140s01aMainId)) {
												eloandbbaseservice.updateRpsO140S01A(null,null,o140s01aMainId);
											}
										}
									}
								}
							}
						}
					}
				}
			}

			if (ClsConstants.FORM.動審表_代償轉貸借新還舊明細.equals(formName)) {
				String bankNo = Util.trim(json.optString("bankNo"));
				String branchNo = Util.trim(json.optString("branchNo"));
				String subACNo = Util.trim(json.optString("subACNo"));
				// ---
				C160S01F c160s01f =service.findByUniqueKey(
						mainId, Integer.parseInt(seq), bankNo, branchNo,
						subACNo, refmainId);
				String errStr = _check_C160S01F(oid, c160s01f);
				errStr += _check_C160S01F_2(c160s01f, json, false);
				if (Util.isEmpty(c160s01f)) {
					c160s01f =service.findModelByOid(C160S01F.class, oid);
				}

				if (Util.isNotEmpty(errStr)) {
					throw new CapMessageException(errStr, getClass());
				}
			}

			// save
			if (model != null) {
				StringBuilder sb = new StringBuilder();

				DataParse.toBean(json, model);
				if(ClsConstants.FORM.動審表_主從債務人資料.equals(formName)) {
					C160S01B c160s01b = (C160S01B)model;
					if(Util.equals(UtilConstants.lngeFlag.擔保品提供人, c160s01b.getRType())){
						c160s01b.setGuaPercent(null);
					}else if(Util.equals(UtilConstants.lngeFlag.共同借款人, c160s01b.getRType())){
						c160s01b.setGuaPercent(new BigDecimal("100"));
					}
				}
				
				// model check
				if (!BeanValidator.isValid(model, SaveCheck.class)) {
					sb.append(EloanConstants.HTML_NEWLINE);
					sb.append(BeanValidator.getValidMsg(model,
							CLS1161S02APage.class, SaveCheck.class));
				}

				// form sp check
				if (ClsConstants.FORM.動審表_主從債務人資料.equals(formName)) {
					// 新增時檢查從債務人是否已存在
					if (Util.isEmpty(oid)) {
						C160S01B c160s01b = (C160S01B) model;
						if (service.checkC160s01bRepeat(c160s01b.getMainId(),
								c160s01b.getRefmainId(), c160s01b.getCustId(),
								c160s01b.getDupNo(), c160s01b.getCntrNo(),
								c160s01b.getRId(), c160s01b.getRDupNo(),
								c160s01b.getRType()))
							sb.append("從債務人[ ").append(c160s01b.getRId())
									.append(" ").append(c160s01b.getRDupNo())
									.append("]已存在。");
					}
				} else if (ClsConstants.FORM.動審表_個金產品種類.equals(formName)) {
					//=================================
					//C160S01C(L140S02A)
					// 扣款帳號
					message.append(service.checkAccount(json, "atpayNo"));
					// 存款帳號/進帳帳號
					message.append(service.checkAccount(json, "accNo"));
					
					//J-111-0227 配合開放入帳及扣款他行帳號調整檢核
					//ACH扣帳-檢核自動扣帳需選擇是
					if (model instanceof C160S01C && 
							Util.equals("01", Util.trim(json.get("payType"))) &&
								Util.notEquals(UtilConstants.DEFAULT.是, Util.trim(json.get("autoPay"))) ){
						sb.append(EloanConstants.HTML_NEWLINE);
						sb.append(getI18nMsg("C160S01C.achPay")+"需為 "+getI18nMsg("C160S01C.autoPay"));//ACH扣帳需為自動扣帳
					}
					
					// 自動扣帳 autoPay check
					if (UtilConstants.DEFAULT.是.equals(Util.trim(json
							.get("autoPay")))) {
						if (model instanceof C160S01C && 
								Util.equals("01", Util.trim(json.get("payType")))){//ACH扣帳
							C160S01C c160s01c = (C160S01C)model;
							//檢核他行帳號扣款帳號
							String errMsg = check_c160s01c_achAcct(c160s01c);
							if(Util.isNotEmpty(errMsg)){
								sb.append(errMsg);
							}else{
								String bankNoFromAchBranchNo = c160s01c.getAchBranchNo().substring(0, 3);
								if(Util.isEmpty(Util.trim(c160s01c.getAchBankNo())) ||
										Util.notEquals(Util.trim(c160s01c.getAchBankNo()), bankNoFromAchBranchNo)){
									//若沒有銀行代碼或銀行代碼不一致則為經辦手KEY，要重新塞值
									String bankNm=findOtherBankNm(bankNoFromAchBranchNo);
									model.set("achBankNo", bankNoFromAchBranchNo);
									model.set("achBankNm", Util.isNotEmpty(bankNm) ? bankNm : c160s01c.getAchBranchNm());
								}
							}
						}else{
							if (!BeanValidator.isValid(model, Check2.class)) {
								sb.append(EloanConstants.HTML_NEWLINE);
								sb.append(BeanValidator.getValidMsg(model,
										CLS1161S02APage.class, Check2.class));
							}
						}
					}
					// 自動進帳 autoRct check
					if (UtilConstants.DEFAULT.是.equals(Util.trim(json
							.get("autoRct")))) {
						if(model instanceof C160S01C && 
								(Util.isNotEmpty(Util.trim(json.get("appOtherAccount"))) ||
								 Util.isNotEmpty(Util.trim(json.get("appOtherBranchNo"))) ||
								 Util.isNotEmpty(Util.trim(json.get("appOtherBranchNm")))) ){
							//撥款他行帳號相關欄位不為空值的時候檢核走這邊
							C160S01C c160s01c = (C160S01C)model;
							String errMsg = check_c160s01c_appOtherBankAcct(c160s01c);
							if(Util.isNotEmpty(errMsg)){
								sb.append(errMsg);
							}else{
								String bankNoFromAppOtherBranchNo = c160s01c.getAppOtherBranchNo().substring(0, 3);
								if(Util.isEmpty(Util.trim(c160s01c.getAppOtherBankNo())) ||
										Util.notEquals(Util.trim(c160s01c.getAppOtherBankNo()), bankNoFromAppOtherBranchNo)){
									//若沒有銀行代碼或銀行代碼不一致則為經辦手KEY，要重新塞值
									String bankNm=findOtherBankNm(bankNoFromAppOtherBranchNo);
									model.set("appOtherBankNo", bankNoFromAppOtherBranchNo);
									model.set("appOtherBankNm", Util.isNotEmpty(bankNm) ? bankNm : c160s01c.getAppOtherBranchNm());
								}
							}
						}else{
							if (!BeanValidator.isValid(model, Check3.class)) {
								sb.append(EloanConstants.HTML_NEWLINE);
								sb.append(BeanValidator.getValidMsg(model,
										CLS1161S02APage.class, Check3.class));
							}
						}
						if(model instanceof C160S01C){
							C160S01C c160s01c = (C160S01C)model;
							String check_msg = check_C160S01C_autoRct(c160s01c);
							if(Util.isNotEmpty(check_msg)){
								sb.append(EloanConstants.HTML_NEWLINE);
								sb.append(check_msg);
							}
						}						
					}
					//J-111-0636 新增私人銀行自動進帳帳號跟自動扣帳帳號檢核
					if(model instanceof C160S01C){
						C160S01C c160s01c = (C160S01C)model;
						sb.append(checkAcctForBranch149(c160s01c));
					}
					
					if(model instanceof C160S01C){
						C160S01C c160s01c = (C160S01C)model;
						List<String> errMsg = new ArrayList<String>();
						if(true){
							List<String> reqColumns = new ArrayList<String>();
							if(needLnStartEndDate(c160s01c)){
								Date lnStartDate = c160s01c.getLnStartDate();
								Date lnEndDate = c160s01c.getLnEndDate();
								
								if(lnStartDate==null){
									reqColumns.add(getI18nMsg("C160S01C.lnStartDate"));
								}
								if(lnEndDate==null){
									reqColumns.add(getI18nMsg("C160S01C.lnEndDate"));
								}
							}
							if(Util.isEmpty(Util.trim(c160s01c.getCtrType()))){
								reqColumns.add(getI18nMsg("C160S01C.ctrType"));
							}
							if(reqColumns.size()>0){
								errMsg.add("未指定 "+StringUtils.join(reqColumns, "、"));
							}
						}
						//-----------------
						List<String> err_check_c160s01c = check_c160s01c(c160s01c);
						if(err_check_c160s01c.size()>0){
							errMsg.addAll(err_check_c160s01c);
						}
						//-----------------
						if(errMsg.size()>0){
							sb.append(EloanConstants.HTML_NEWLINE);
							sb.append(StringUtils.join(errMsg, EloanConstants.HTML_NEWLINE));
						}
					}
				} else if (ClsConstants.FORM.動審表_代償轉貸借新還舊主檔.equals(formName)) {
					
					C160S01E c160s01e = (C160S01E)model;
					// model check
					if (UtilConstants.DEFAULT.是.equals(Util.trim(json
							.get("chgOther")))) {
						if (!BeanValidator.isValid(model, Check2.class)) {
							sb.append(EloanConstants.HTML_NEWLINE);
							sb.append(BeanValidator.getValidMsg(model,
									CLS1161S02APage.class, Check2.class));
						}
						List<C160S01F> c160s01fs = clsService.findC160S01F_byMainIdSeqRefMainid(mainId, Integer.parseInt(seq), refmainId);
						if (c160s01fs.size()>0) {
							for (C160S01F c160s01f:c160s01fs) {
								String errMsg = _check_C160S01F_2(c160s01f, json,true);
								if (Util.isNotEmpty(errMsg)) {
									sb.append(EloanConstants.HTML_NEWLINE);
									sb.append(errMsg);
									break;
								}
							}
						}
					}
					// check 手續費
					if (UtilConstants.DEFAULT.是.equals(Util.trim(json
							.get("chargeFlag")))) {
						if (!BeanValidator.isValid(model, Check3.class)) {
							sb.append(EloanConstants.HTML_NEWLINE);
							sb.append(BeanValidator.getValidMsg(model,
									CLS1161S02APage.class, Check3.class));
						}
					}
					
					//check J-109-0435 產品種類56:「97年2000億優惠購屋專案」之貸款，額度明細表之轉貸資訊「原貸放日期」、「原貸款到期日」為必建欄位
					C160S01C c160s01c = clsService.findC160S01C_mainId_refMainid_seq(c160s01e.getMainId(), c160s01e.getRefmainId(), c160s01e.getSeq());
					if(CrsUtil.is_56(c160s01c.getProdKind())){
						
						List<C160S01F> c160s01fList = this.clsService.findC160S01F_byMainIdSeqRefMainid(c160s01e.getMainId(), c160s01e.getSeq(), c160s01e.getRefmainId());
						for(C160S01F c160s01f : c160s01fList){
							
							if(null == c160s01f.getOLNAppDate() || null == c160s01f.getOLNEndDate()){
								sb.append(EloanConstants.HTML_NEWLINE);
								sb.append(getI18nMsg("C160S01F.error004"));
							}
						}
					}
				} else if (ClsConstants.FORM.動審表_代償轉貸借新還舊明細.equals(formName)) {
					// 移至上方,檢查變更後的資料,uniqueIndex是否重複,要在DataParse.toBean(json,
					// model); 之前
				}

				// out check message
				if (sb.length() > 0) {
					throw new CapMessageException(sb.toString(), getClass());
				}
				// add save list
				saveList.add(model);
			}
		}
		// save
		service.save(saveList);
		// output
		for (GenericBean model : saveList) {
			String className = Util.trim(DataParse.getEntityClass(model)
					.getSimpleName());
			result.set(className + "Form", DataParse.toResult(model));
		}
		// 顯示訊息
		result.set("message", message.toString());

		return result;
	}// ;
	
	//J-111-0636 149 私人銀行檢核 當自動進帳帳號跟自動扣帳帳號都有填寫時，兩個帳號要一致 
	//※149分行登入填寫動審表才檢核，且有寫帳號一定要是149開頭的帳號
	private String checkAcctForBranch149(C160S01C c160s01c){
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		StringBuilder errMsg = new StringBuilder();
		String checkBranchNo = "149";//私人銀行
		
		String LMS_CHK_149_CLS_ACCT = Util.trim(lmsService
				.getSysParamDataValue("LMS_CHK_149_CLS_ACCT"));
		if(Util.notEquals("Y", LMS_CHK_149_CLS_ACCT)){
			return errMsg.toString();
		}
		
		if(Util.equals(checkBranchNo, user.getUnitNo())){//149(私人銀行)才檢核
			String atpayNo = Util.trim(c160s01c.getAtpayNo());//扣款帳號
			String accNo = Util.trim(c160s01c.getAccNo());//存款帳號/進帳帳號
			//帳號若有填，一定要是149開頭
			if(Util.isNotEmpty(atpayNo) && Util.notEquals(Util.getLeftStr(atpayNo, 3), checkBranchNo)){
				//扣款帳號需為149開頭
				errMsg.append(EloanConstants.HTML_NEWLINE);
				errMsg.append(getI18nMsg("C160S01C.atpayNo")+"需為"+checkBranchNo+"開頭之帳號");
			}
			if(Util.isNotEmpty(accNo) && Util.notEquals(Util.getLeftStr(accNo, 3), checkBranchNo)){
				//進帳帳號需為149開頭
				errMsg.append(EloanConstants.HTML_NEWLINE);
				errMsg.append(getI18nMsg("C160S01C.accNo")+"需為"+checkBranchNo+"開頭之帳號");
			}
			if(Util.isNotEmpty(atpayNo) && Util.isNotEmpty(accNo)){
				//自動進帳帳號跟自動扣帳帳號都有填寫時才要檢查有沒有一致
				if(Util.notEquals(atpayNo, accNo)){
					errMsg.append(EloanConstants.HTML_NEWLINE);
					errMsg.append(getI18nMsg("C160S01C.atpayNo")+"與"+getI18nMsg("C160S01C.accNo")+"需一致");
				}
			}
		}
		return errMsg.toString();
	}

	private String get_rctAmt_descKey(String prodKind){
		if(CrsUtil.is_67(prodKind)){
			return "C160S01C.rctAMT";	
		}else if(CrsUtil.is_70(prodKind)){
			return "label.rmRctAmt.prodKind70"; //每期撥款額度	
		}else{
			return "C160S01C.rctAMT";
		}		
	}

	//J-111-0227 配合開放入帳及扣款他行帳號調整檢核-檢核扣帳他行帳號相關欄位
	private String check_c160s01c_achAcct(C160S01C c160s01c){
		StringBuilder errMsg = new StringBuilder();
		Properties prop_cls1161s02a = MessageBundleScriptCreator.getComponentResource(CLS1161S02APage.class);
		
		//檢核他行帳號扣款帳號
		if ( Util.isEmpty(Util.trim(c160s01c.getAchBranchNo())) ||
				Util.isEmpty(Util.trim(c160s01c.getAchBranchNm())) ||
				Util.isEmpty(Util.trim(c160s01c.getAchAccount())) ){
			errMsg.append(EloanConstants.HTML_NEWLINE);
			errMsg.append("扣款"+prop_cls1161s02a.getProperty("C160S01C.otherBankAccount")+" 必填欄位");//扣款他行帳號 必填欄位
		} else {
			if(Util.trim(c160s01c.getAchBranchNo()).length()!=7){
				//開放經辦自行輸入，要檢查
				errMsg.append(EloanConstants.HTML_NEWLINE);
				errMsg.append("扣款"+prop_cls1161s02a.getProperty("C160S01C.error.BranchNo"));//他行分行代號須為七碼
			}else{
				//檢核他行分行代號
				if(!checkBranchCode(Util.trim(c160s01c.getAchBranchNo()))){
					errMsg.append(EloanConstants.HTML_NEWLINE);
					errMsg.append("扣款"+prop_cls1161s02a.getProperty("C160S01C.error2.BranchNo"));//他行分行代號有誤
				}
			}
		}
		return errMsg.toString();
	}
	
	//J-111-0227 配合開放入帳及扣款他行帳號調整檢核-檢核進帳他行帳號相關欄位
	private String check_c160s01c_appOtherBankAcct(C160S01C c160s01c){
		StringBuilder errMsg = new StringBuilder();
		Properties prop_cls1161s02a = MessageBundleScriptCreator.getComponentResource(CLS1161S02APage.class);
		
		//檢核他行帳號進帳帳號
		if(Util.isEmpty(Util.trim(c160s01c.getAppOtherAccount())) ||
				Util.isEmpty(Util.trim(c160s01c.getAppOtherBranchNo())) ||
				Util.isEmpty(Util.trim(c160s01c.getAppOtherBranchNm())) ){
			errMsg.append(EloanConstants.HTML_NEWLINE);
			errMsg.append("進帳"+prop_cls1161s02a.getProperty("C160S01C.otherBankAccount")+" 必填欄位");//進帳他行帳號 必填欄位
		} else {
			if(Util.trim(c160s01c.getAppOtherBranchNo()).length()!=7){
				//經辦自行輸入，要檢查
				errMsg.append(EloanConstants.HTML_NEWLINE);
				errMsg.append("進帳"+prop_cls1161s02a.getProperty("C160S01C.error.BranchNo"));//他行分行代號須為七碼
			}else{
				//檢核他行分行代號
				if(!checkBranchCode(Util.trim(c160s01c.getAppOtherBranchNo()))){
					errMsg.append(EloanConstants.HTML_NEWLINE);
					errMsg.append("進帳"+prop_cls1161s02a.getProperty("C160S01C.error2.BranchNo"));//他行分行代號有誤
				}
			}
		}
		if(Util.isEmpty(Util.trim(c160s01c.getRctAMT()))){
			errMsg.append(EloanConstants.HTML_NEWLINE);
			errMsg.append(prop_cls1161s02a.getProperty("C160S01C.rctAMT")+" 必填欄位");//進帳金額 必填欄位
		}
		if(Util.isEmpty(Util.trim(c160s01c.getRctDate()))){			
			errMsg.append(EloanConstants.HTML_NEWLINE);
			errMsg.append(prop_cls1161s02a.getProperty("C160S01C.rctDate")+" 必填欄位");//進帳日期 必填欄位
		}
		
		return errMsg.toString();
	}
	
	//J-111-0227 配合開放入帳及扣款他行帳號調整檢核-檢核他行分行代號
	private boolean checkBranchCode(String bankBranchCode){
		boolean result=false;
		String bankCode = bankBranchCode.substring(0,3);
		List<Map<String, Object>> branchList=mis.findMISSynBank(bankCode);
		
		if(branchList!=null && branchList.size()>0){
			for(Map<String, Object> branchNm : branchList){
				if(Util.equals(branchNm.get("CODE"),bankBranchCode)){
					result = true;
					break;
				}
			}
		}
		if(!result && Util.equals("0000", bankBranchCode.substring(3)) &&
				Util.isNotEmpty(findOtherBankNm(bankCode))){
			result=true;
		}
		return result;
	}
	
	//J-111-0227 配合開放入帳及扣款他行帳號調整檢核-取得他行銀行名稱
	private String findOtherBankNm(String bankCode){
		String otherBankNm="";
		Map<String, String> bankType = codeService.findByCodeType("lms1605s03_slBankType","zh_TW");
		for (Iterator<Map.Entry<String, String>> entries = bankType.entrySet().iterator(); entries.hasNext(); ){
			Map.Entry<String, String> entry = entries.next();
			String BankCodeType="BankCode"+entry.getKey();
			CodeType bankInfo=codeService.findByCodeTypeAndCodeValue(BankCodeType,bankCode,"zh_TW");
			if(bankInfo != null){
				otherBankNm=bankInfo.getCodeDesc();
				break;
			}
		}
		return otherBankNm;
	}
	
	private List<String> check_c160s01c(C160S01C c160s01c){
		List<String> errMsg = new ArrayList<String>();

		Properties prop_cls1151s01 = MessageBundleScriptCreator.getComponentResource(CLS1151S01Page.class);
		Properties prop_cls1161s02a = MessageBundleScriptCreator.getComponentResource(CLS1161S02APage.class);
		
		if(CrsUtil.is_49(c160s01c.getProdKind())){
			L140S02A l140s02a = clsService.findL140S02A_by_C160S01C(c160s01c);
			String befSeqCntrNo = Util.trim(l140s02a.getBefSeqCntrNo());
			//J-109-0244 消金處表示，可以讓客戶在同1份簽報書，就申請第1順位的額度、次順位的額度
			//在次順位的額度動審時，檢核「次順位動撥日須晚於前順位抵押權借款之動撥日」
			List<MISLN30> befSeqLNF030_list = misLNF030Service.selByCntrNo(befSeqCntrNo);
			Date minLoanDate = null;
			for(MISLN30 lnf030 : befSeqLNF030_list){
				if(lnf030.getLnf030_loan_date()==null){
					continue;
				}
				if(minLoanDate==null ||(minLoanDate!=null && LMSUtil.cmpDate(lnf030.getLnf030_loan_date(), "<", minLoanDate))){
					minLoanDate = lnf030.getLnf030_loan_date();
				}
				
			}
			if(minLoanDate==null){
				errMsg.add(prop_cls1151s01.getProperty("L140S02A.befSeqCntrNo")+befSeqCntrNo+"尚未撥款");
			}else{
				if(LMSUtil.cmpDate(minLoanDate, "<", CapDate.getCurrentTimestamp())){
					//ok
				}else{
					if(clsService.is_function_on_codetype("prod49_cls1161_befSeqCntrNo_loan")){
						errMsg.add(prop_cls1151s01.getProperty("L140S02A.befSeqCntrNo")+befSeqCntrNo+"首撥日為"+TWNDate.toAD(minLoanDate)+"，次順位動撥日須晚於前順位抵押權借款之動撥日");		
					}	
				}
				
			}
		}
		if(CrsUtil.is_67(c160s01c.getProdKind())){ //以房養老-傳統型 ｛自動進帳、自動扣帳｝，必須為Y
			if(!Util.equals("Y", c160s01c.getAutoPay())){
				errMsg.add(CrsUtil.PROD_67_DESC+"需為 "+getI18nMsg("C160S01C.autoPay"));	
			}
			if(!Util.equals("Y", c160s01c.getAutoRct())){
				errMsg.add(CrsUtil.PROD_67_DESC+"需為 "+getI18nMsg("C160S01C.autoRct"));
			}
			//扣款帳號、進帳帳號 需相同 
			if(!Util.equals(c160s01c.getAtpayNo(), c160s01c.getAccNo())){
				errMsg.add(CrsUtil.PROD_67_DESC+"["
						+getI18nMsg("C160S01C.atpayNo")+"、"
						+getI18nMsg("C160S01C.accNo")
						+"]需相同");	
			}
			
			if(c160s01c.getRctAMT()==null||c160s01c.getRctAMT().compareTo(BigDecimal.ZERO)<=0){
				errMsg.add(getI18nMsg(get_rctAmt_descKey(c160s01c.getProdKind()))+" 需 > 0");
			}else{
				BigDecimal rctAmt = c160s01c.getRctAMT();
				int threshold_rmRctAmt = 1;
				if(clsService.is_function_on_codetype("C160S01C_rmRctAmt_threshold")){
					threshold_rmRctAmt = 3000;
				}
				
				if(rctAmt.compareTo(new BigDecimal(threshold_rmRctAmt))<0){		
					//errorMsg.014={0}至少需{1}元以上
					errMsg.add(CrsUtil.PROD_67_DESC+MessageFormat.format(getI18nMsg("errorMsg.014"), getI18nMsg(get_rctAmt_descKey(c160s01c.getProdKind())), String.valueOf(threshold_rmRctAmt)));
				}else{
					/*
						在 html 裡 label 是 L140S02A.loanAmt
					 	但 input 卻是  approvedAmt
					*/
					BigDecimal prodAmt = c160s01c.getApprovedAmt();
					//重算 每期利息上限
					c160s01c.setRmIntMax(LMSUtil.prod_rmIntMax(rctAmt));
													
					//檢核 每期撥款金額 的 倍數
					//比照 CLS1151M01FormHandler ::checkl140s02a(...) 搜尋    "page5.225"
					if(prodAmt.remainder(rctAmt).compareTo(BigDecimal.ZERO)!=0){
						
						//page5.225={0}需為{1}的倍數
						String p1 = "("+getI18nMsg("C160S01C.loanAmt")+")"+" "
									+LMSUtil.pretty_numStr(prodAmt)+" "; 
						String p2 = "("+getI18nMsg(get_rctAmt_descKey(c160s01c.getProdKind()))+")"+" "
									+LMSUtil.pretty_numStr(rctAmt)+" ";
						errMsg.add(MessageFormat.format(prop_cls1151s01.getProperty("page5.225")
								, p1 , p2));
					}
					
					L140S02A l140s02a = clsService.findL140S02A_by_C160S01C(c160s01c);
					if(l140s02a!=null && UtilConstants.Cntrdoc.Property.新做.equals(l140s02a.getProperty())){
						BigDecimal should_val = Arithmetic.mul(rctAmt, new BigDecimal(ClsUtil.prod_totalPeriod(l140s02a)));
						
						if(prodAmt.compareTo(should_val)!=0){
							errMsg.add(CrsUtil.PROD_67_DESC+" "
									+getI18nMsg("C160S01C.loanAmt")+"應為"
									+LMSUtil.pretty_numStr(rctAmt)+" * "
									+ClsUtil.prod_totalPeriod(l140s02a)+" = "
									+LMSUtil.pretty_numStr(should_val));
						}		
					}
				}								
			}
		}else if(CrsUtil.is_70(c160s01c.getProdKind())){ //以房養老-累積型 ｛自動進帳｝必須為Y
			if(!Util.equals("Y", c160s01c.getAutoPay())){
				errMsg.add(CrsUtil.PROD_70_DESC+"需為 "+prop_CLS1161S02APage.getProperty("C160S01C.autoPay"));	
			}
			if(!Util.equals("Y", c160s01c.getAutoRct())){
				errMsg.add(CrsUtil.PROD_70_DESC+"需為 "+prop_CLS1161S02APage.getProperty("C160S01C.autoRct"));
			}
			
			//扣款帳號、進帳帳號 需相同 
			if(!Util.equals(c160s01c.getAtpayNo(), c160s01c.getAccNo())){
				errMsg.add(CrsUtil.PROD_70_DESC+"["
						+getI18nMsg("C160S01C.atpayNo")+"、"
						+getI18nMsg("C160S01C.accNo")
						+"]需相同");	
			}
			
			if(c160s01c.getRctAMT()==null||c160s01c.getRctAMT().compareTo(BigDecimal.ZERO)<=0){
				errMsg.add(getI18nMsg(get_rctAmt_descKey(c160s01c.getProdKind()))+" 需 > 0");
			}else{
				BigDecimal rctAmt = c160s01c.getRctAMT();
				if(true){
					/*
						在 html 裡 label 是 L140S02A.loanAmt
					 	但 input 卻是  approvedAmt
					*/
					BigDecimal prodAmt = c160s01c.getApprovedAmt();
					//重算 每期利息上限
					c160s01c.setRmIntMax(LMSUtil.prod_rmIntMax(rctAmt));
													
					//檢核 每期撥款金額 的 倍數
					//比照 CLS1151M01FormHandler ::checkl140s02a(...) 搜尋    "page5.225"
					if(prodAmt.remainder(rctAmt).compareTo(BigDecimal.ZERO)!=0){
						
						//page5.225={0}需為{1}的倍數
						String p1 = "("+getI18nMsg("C160S01C.loanAmt")+")"+" "
									+LMSUtil.pretty_numStr(prodAmt)+" "; 
						String p2 = "("+getI18nMsg(get_rctAmt_descKey(c160s01c.getProdKind()))+")"+" "
									+LMSUtil.pretty_numStr(rctAmt)+" ";
						errMsg.add(MessageFormat.format(prop_cls1151s01.getProperty("page5.225")
								, p1 , p2));
					}
					
					L140S02A l140s02a = clsService.findL140S02A_by_C160S01C(c160s01c);
					if(l140s02a!=null && UtilConstants.Cntrdoc.Property.新做.equals(l140s02a.getProperty())){
						BigDecimal should_val = Arithmetic.mul(rctAmt, new BigDecimal(ClsUtil.prod_totalPeriod(l140s02a)));
						
						if(prodAmt.compareTo(should_val)!=0){
							errMsg.add(CrsUtil.PROD_70_DESC+" "
									+getI18nMsg("C160S01C.loanAmt")+"應為"
									+LMSUtil.pretty_numStr(rctAmt)+" * "
									+ClsUtil.prod_totalPeriod(l140s02a)+" = "
									+LMSUtil.pretty_numStr(should_val));
						}		
					}
				}								
			}			
		}
		
		if(CrsUtil.is_67(c160s01c.getProdKind()) || CrsUtil.is_70(c160s01c.getProdKind())){ // ｛以房養老-傳統型 ｝與｛以房養老-累積型 ｝共用的檢核			
			if(true){ //檢核［起迄日］與［總期數］的關連
				//J-108-0227 雖在畫面自動以［起日+總期數］帶出［迄日］，但因分行在動審時縮短期間，導致［起日+總期數!=迄日］
				if(c160s01c.getLnStartDate()!=null && c160s01c.getLnEndDate()!=null && clsService.is_function_on_codetype("J-108-0227")){
					L140S02A l140s02a = clsService.findL140S02A_by_C160S01C(c160s01c);
					if(l140s02a!=null && UtilConstants.Cntrdoc.Property.新做.equals(l140s02a.getProperty())){
						int addMonths = ClsUtil.prod_totalPeriod(l140s02a);
						Date shouldLnEndDt = CapDate.addMonth(c160s01c.getLnStartDate(), addMonths);
						int diffDays = Math.abs(CapDate.calculateDays(shouldLnEndDt, c160s01c.getLnEndDate()));						
						if(diffDays>3){
							errMsg.add(CrsUtil.PROD_67_DESC
									+prop_cls1161s02a.getProperty("C160S01C.lnStartDate")+"("+TWNDate.toAD(c160s01c.getLnStartDate())+")"
									+"~"
									+prop_cls1161s02a.getProperty("C160S01C.lnEndDate")+"("+TWNDate.toAD(c160s01c.getLnEndDate())+")"
									+"應間隔"+addMonths+"個月。<br/>因貸款期間會影響［撥款期數、每期進帳金額］，若與客戶簽訂契約的貸款期間＜簽案時的貸款期間，請先修改簽案內容。");
						}
					}
				}
			}
		}
		
		if(CrsUtil.is_68(c160s01c.getProdKind())){
			String lnStartDate = Util.trim(TWNDate.toAD(c160s01c.getLnStartDate()));
			String lnEndDate = Util.trim(TWNDate.toAD(c160s01c.getLnEndDate()));
			
			String useStartDate = Util.trim(TWNDate.toAD(c160s01c.getUseStartDate()));
			String useEndDate = Util.trim(TWNDate.toAD(c160s01c.getUseEndDate()));
			
			if(Util.equals(lnStartDate, useStartDate) && Util.equals(lnEndDate, useEndDate)){
				//eq
				if(clsService.is_function_on_codetype("J-107-0008")
						&& Util.equals(StringUtils.substring(lnStartDate, 5, 10), StringUtils.substring(lnEndDate, 5, 10))){
					Map<String, String> prodKindNameMap = prodService.getProdKindName();								
					errMsg.add(prop_cls1161s02a.getProperty("C160S01C.prodKind") +"「"+LMSUtil.getDesc(prodKindNameMap, c160s01c.getProdKind())+"」"
							+"之"+prop_cls1161s02a.getProperty("C160S01C.lnEndDate")+"應等於[起日+期間-1天]");
				}
			}else{
				Map<String, String> prodKindNameMap = prodService.getProdKindName();								
				errMsg.add(prop_cls1161s02a.getProperty("C160S01C.prodKind") +"「"+LMSUtil.getDesc(prodKindNameMap, c160s01c.getProdKind())+"」"
						+"之"+prop_cls1161s02a.getProperty("C160S01C.useLine")+"("+useStartDate+" ~ "+useEndDate+")"
						+"與"+prop_cls1161s02a.getProperty("C160S01C.lnSelect")+"("+lnStartDate+" ~ "+lnEndDate+")需相同");						
			}							
		}
		
		if(true){
			String check_pmt_1st_rt_dt = check_pmt_1st_rt_dt(c160s01c, prop_cls1161s02a);
			if(Util.isNotEmpty(check_pmt_1st_rt_dt)){
				errMsg.add(check_pmt_1st_rt_dt);
			}
		}
		
		//若為了修正動審表資料，可能會重做新動審表 => 先不執行這一段檢核
//		if(clsService.is_function_on_codetype("J-108-0195")){
//			if(c160s01c.getDRateAdd()!=null && c160s01c.getDRateAdd().compareTo(BigDecimal.ZERO)!=0){
//				errMsg.add("依消金業務處108/09/16兆銀消金字第1080000164號函 ，新版契約書自108/09/20生效。計收延遲利息加碼應為０%。");				
//			}
//		}
		return errMsg;
	}
	
	
	private String check_pmt_1st_rt_dt(C160S01C c160s01c, Properties prop_cls1161s02a){
		if(c160s01c.getPmt_1st_rt_dt()!=null){		
			if(CrsUtil.is_71(c160s01c.getProdKind())
				||CrsUtil.is_03(c160s01c.getProdKind())
				||CrsUtil.is_31(c160s01c.getProdKind())){
				
			}else{
				return "「"+prop_cls1161s02a.getProperty("C160S01C.pmt_1st_rt_dt") +"」"+prop_cls1161s02a.getProperty("C160S01C.pmt_1st_rt_dt.memo_msg");
			}
			
			//檢核「期付金」，才需輸入「首次還款日」
			//透支 or 按月計息 不用 ===> 參考 ELF501_GINTTYPE
			L140S02A l140s02a = clsService.findL140S02A_by_C160S01C(c160s01c);
			boolean cannot_input_1st_rt_dt = false;
			if(l140s02a!=null){
				L140S02C l140s02c = clsService.findL140S02C(l140s02a.getMainId(), l140s02a.getSeq());					
				if(l140s02c!=null){
					if (Util.equals(l140s02c.getRIntWay(), "6")) {
						cannot_input_1st_rt_dt = false; // 期付金
					}else{
						if (Util.equals(l140s02a.getProdKind(), ProdKindEnum.企金科目.getCode())) {
							cannot_input_1st_rt_dt = true;
						} else {
							L140S02E l140s02e = clsService.findL140S02E(l140s02a.getMainId(), l140s02a.getSeq());
							if(l140s02e!=null){
								if (Util.equals(l140s02e.getPayWay(), "7")) {
									cannot_input_1st_rt_dt = true; // 按月計息
								} else {
									cannot_input_1st_rt_dt = false;// 期付金 or 其它FreeText輸入欄位 (消金戶 default 是 期付金)
								}
							}
						}
					}	
				}
			}
			if(cannot_input_1st_rt_dt){
				return ("此 產品種類 非「期付金」，不需輸入「"+prop_cls1161s02a.getProperty("C160S01C.pmt_1st_rt_dt") +"」");
			}else{

				if(c160s01c.getLnStartDate()!=null){
					//第1版：原邏輯{撥款日+1個月}前後15天
//					Date defaultDate = CapDate.addMonth(c160s01c.getLnStartDate(), 1);
//					Date d1 = CapDate.shiftDays(defaultDate, -15);
//					Date d2 = CapDate.shiftDays(defaultDate, 15);
					
					//第2版：改為 撥款日+16~45天
//					Date d1 = CapDate.shiftDays(c160s01c.getLnStartDate(), 16);
//					Date d2 = CapDate.shiftDays(c160s01c.getLnStartDate(), 45);
//					if(LMSUtil.cmpDate(c160s01c.getPmt_1st_rt_dt(), ">=", d1) && LMSUtil.cmpDate(c160s01c.getPmt_1st_rt_dt(), "<=", d2)){
//						
//					}else{
//						return (prop_cls1161s02a.getProperty("C160S01C.pmt_1st_rt_dt") +"("+Util.trim(TWNDate.toAD(c160s01c.getPmt_1st_rt_dt()))+")"
//								+" 應介於 "
//								+Util.trim(TWNDate.toAD(d1))+" ~ "+Util.trim(TWNDate.toAD(d2)));
//						
//					}
					
					//第3版：當有輸入值(1)線下對保案件：不可早於撥款日期(2)線上對保案件：檢核需和 {PLOAN 傳回的首次還款日}相同
					if(l140s02a!=null){
						L140M01A l140m01a = clsService.findL140M01A_mainId(l140s02a.getMainId());
						if(l140m01a!=null){
							C340M01A c340m01a = service.find_c340m01a_CtrTypeA_ploanCtrStatus9_orderBy_ploanCtrBegDateDesc(l140m01a);
							if(c340m01a==null){
								//線下對保
								if( c160s01c.getLnStartDate()!=null
										&& LMSUtil.cmpDate(c160s01c.getPmt_1st_rt_dt(), "<", c160s01c.getLnStartDate())){
										return prop_cls1161s02a.getProperty("C160S01C.pmt_1st_rt_dt")+"("+TWNDate.toAD(c160s01c.getPmt_1st_rt_dt())+")"
											+"不可早於"+prop_cls1161s02a.getProperty("C160S01C.lnStartDate")+"("+TWNDate.toAD(c160s01c.getLnStartDate())+")";
									
								}
							}else{
								//線上對保
								if( c340m01a.getPloanCtr1stRtDt()!=null 
										&& ! LMSUtil.cmpDate(c160s01c.getPmt_1st_rt_dt(), "==", c340m01a.getPloanCtr1stRtDt())){
										return prop_cls1161s02a.getProperty("C160S01C.pmt_1st_rt_dt")+"("+TWNDate.toAD(c160s01c.getPmt_1st_rt_dt())+")"
											+"與線上對保契約的首次還款日("+TWNDate.toAD(c340m01a.getPloanCtr1stRtDt())+")不一致";									
								}
							}							
						}
					}
				}
			}			
			
		}
		return "";
	}
	private String _checkProdKind59(L140S02F l140s02f, Date getDay) {
		String _overDay = ("2010-12-01");
		Date overDay = CapDate.parseDate(_overDay);
		Calendar oDay = Calendar.getInstance();
		oDay.setTime(overDay);

		// Date homeRegisterDate = l140s02f.getHomeRegisterDate();
		if (getDay == null) {
			// homeRegisterDate = new Date();
			return Util.trim(this.getI18nMsg("L140M01A.msg127")); // L140M01A.msg127=房貸相關頁籤中優惠房貸的
																	// 產權登記日期不可空白。
		}
		// **因原程式的月份計數不足月也會加一個月***
		Calendar homeRegisterCal = Calendar.getInstance();
		homeRegisterCal.setTime(getDay);

		if (oDay.compareTo(homeRegisterCal) >= 0) {
			return Util.trim(this.getI18nMsg("L140M01A.msg128")); // L140M01A.msg128=所有權取得日必需大於2010年12月1日。
		}
		// -----------------以上是原本的2010-12-01的檢查-------------------

		Boolean tRule = false;

		String _ruleDay = ("2015-01-01"); // 新規則啟用日期
		Date ruleDay = CapDate.parseDate(_ruleDay);
		Calendar rDay = Calendar.getInstance();
		rDay.setTime(ruleDay);

		String _nowDay = CapDate.formatDate(new Date(), "yyyy-MM-dd");
		Date nowDate = CapDate.parseDate(_nowDay);
		Calendar nDate = Calendar.getInstance();
		nDate.setTime(nowDate);

		String _appDay = null;
		Date appDate = null;
		Calendar appDay = null;

		if (nDate.compareTo(rDay) >= 0) { // 系統日大於啟用日,開始要輸入申請日期

			if (l140s02f.getAppDate() == null) {
				return null;
			}

			_appDay = CapDate.formatDate(
					l140s02f.getAppDate() == null ? new Date() : l140s02f
							.getAppDate(), "yyyy-MM-dd");
			appDate = CapDate.parseDate(_appDay);
			appDay = Calendar.getInstance();
			appDay.setTime(appDate);

			if (appDay.compareTo(rDay) >= 0) {// 申請日大於啟用日要檢查6個月
				tRule = true;
			}

		}

		if (tRule) {
			_appDay = CapDate.formatDate(
					l140s02f.getAppDate() == null ? new Date() : l140s02f
							.getAppDate(), "yyyy-MM-dd");
			appDate = CapDate.parseDate(_appDay);
			appDay = Calendar.getInstance();
			appDay.setTime(appDate);

			int totalmon = -1;
			while (homeRegisterCal.compareTo(appDay) <= 0) {
				totalmon = totalmon + 1;
				homeRegisterCal.add(Calendar.MONTH, 1);
			}
			if (totalmon > 5) {
				// L140M01A.msg125=房地之取得日期係以建物謄本上登記日期為準，<br/>登記日期為申請日前6個月起者，便符合規定。<br/>舉例：借款人於104年1月5日申請貸款，<br/>所購置之自有住宅建物謄本登記日期須在103年7月5日以後。
				return MessageFormat.format(this.getI18nMsg("L140M01A.msg125"),
						_appDay); // Util.trim(this.getI18nMsg("L140M01A.msg125"));
			}
		}
		return null;
	}

	private String _check_C160S01F(String org_oid, C160S01F c160s01f) {
		boolean error = true;
		if (c160s01f == null) {
			error = false;
		} else {
			if (Util.isEmpty(org_oid)) {
				// 新增的資料, 和既有的衝突
			} else {
				if (Util.equals(org_oid, c160s01f.getOid())) {
					// 原資料修改
					error = false;
				} else {
					/*
					 * 由簽報書帶入 branchNo=AAAAAAA
					 * 
					 * 已有一筆資料branchNo=AAAAAAA 新增另一筆 branchNo=BBBBBBB 又在 UI 把
					 * branchNo 由 BBBBBBB 改回 AAAAAAA → oid 不一致
					 */
				}

				//判斷信貸代償不檢查
                Boolean active_repayment = false;
                MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
                String unitNo = user.getUnitNo();
                Map<String, String> map= clsService.get_codeType_codeDesc2WithOrder("LMS_FUNC_ON_FLAG","zh_TW");
                if(clsService.is_function_on_codetype("cls_repayment_pilot")){
                    if (map.containsKey("cls_repayment_pilot")) {
                        String val = Util.trim(map.get("cls_repayment_pilot"));
                        if(val.contains(unitNo)){
                            active_repayment = true;
                        }
                    }
                }
                if(clsService.is_function_on_codetype("cls_repayment_all")){
                    active_repayment = true;
                }
                if (active_repayment) {
                    C160S01C c160s01c = clsService.findC160S01C_mainId_refMainid_seq(c160s01f.getMainId(), c160s01f.getRefmainId(), c160s01f.getSeq());
                    if (Util.isNotEmpty(c160s01c)) {
                        if (Util.equals(c160s01c.getProdKind(), "71")) {
                            error = false;
                        }
                    }
                }
			}
		}

		if (error) {
			StringBuffer sb = new StringBuffer();
			sb.append(getI18nMsg("C160S01F.bankNo")).append("+");
			sb.append(getI18nMsg("C160S01F.branchNo")).append("+");
			sb.append(getI18nMsg("C160S01F.subACNo"));
			sb.append(EloanConstants.HTML_NEWLINE);
			sb.append(getI18nMsg("C160S01F.error"));
			return sb.toString();
		} else {
			return "";
		}
	}
	private String _check_C160S01F_2(C160S01F c160s01f, JSONObject json, Boolean mainTableCheck){
		if (Util.isEmpty(c160s01f)) {
			return "";
		}
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String ssoUnitNo = user.getSsoUnitNo();
		String unitNo = user.getUnitNo();
		Boolean active_repayment = false;
		String errorMsg = "";
		Map<String, String> map= clsService.get_codeType_codeDesc2WithOrder("LMS_FUNC_ON_FLAG","zh_TW");
		if(clsService.is_function_on_codetype("cls_repayment_pilot")){
			if (map.containsKey("cls_repayment_pilot")) {
				String val = Util.trim(map.get("cls_repayment_pilot"));
				if(val.contains(unitNo)){
					active_repayment = true;
				}
			}
		}
		if(clsService.is_function_on_codetype("cls_repayment_all")){
			active_repayment = true;
		}
		if (active_repayment) {
			C160S01C c160s01c = clsService.findC160S01C_mainId_refMainid_seq(c160s01f.getMainId(), c160s01f.getRefmainId(), c160s01f.getSeq());
			if (Util.isNotEmpty(c160s01c)) {
				if (Util.equals(c160s01c.getProdKind(), "71")) {
					String repaymentProduct = Util.trim(json.optString("repaymentProduct"));
					String custName = Util.trim(json.optString("custName"));
					String accNo = Util.trim(json.optString("accNo"));
					//如果是主檔儲存檢核就直接撈DB檢查
					if (mainTableCheck) {
						repaymentProduct = c160s01f.getRepaymentProduct();
						custName = c160s01f.getCustName();
						accNo = c160s01f.getAccNo();
					}
					if (Util.isEmpty(repaymentProduct) || Util.isEmpty(custName) || Util.isEmpty(accNo)) {
						errorMsg = getI18nMsg("C160S01F.error005");
					}
				}
			}
		}
		return errorMsg;
	}

	/**
	 * 刪除明細
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult deleteDetial(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String oid = Util.trim(params.getString(EloanConstants.OID));
		String formName = Util.trim(params.getString("formName"));
		GenericBean model = getModel(formName, oid);
		if (model != null) {
			boolean proc_C160M01A_totAmt = false;
			String c160m01a_mainId = ""; 
			if (ClsConstants.FORM.動審表_個金產品種類.equals(formName)) {
				service.deleteC160S01C((C160S01C) model);
			} else {
				if ("C160M01BForm".equals(formName)) {
					String mainId = Util.trim(model.get("mainId"));
					String refmainId = Util.trim(model.get("refmainId"));
					List<C160S01C> c160s01cList = clsService.findC160S01C_mainId_refMainid(mainId, refmainId);
					if (c160s01cList != null) {
						service.delete(c160s01cList);
					}
					proc_C160M01A_totAmt = true;
					c160m01a_mainId = mainId;
				}
				service.delete(model);
			}
		
			if(proc_C160M01A_totAmt){
				service.sync_C160M01A_totAmt(c160m01a_mainId);
			}
		}

		return result;
	}// ;

	/**
	 * 複製明細
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult copyDetial(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String formName = Util.trim(params.getString("formName"));
		String oid = Util.trim(params.getString(EloanConstants.OID));
		GenericBean model = getModel(formName, oid);
		if (model != null) {
			if (ClsConstants.FORM.動審表_個金產品種類.equals(formName)) {
				String editOid = Util.trim(params.getString("editOid"));
				if (!oid.equals(editOid)) {
					C160S01C editC160s01c = service.findModelByOid(
							C160S01C.class, editOid);
					if (editC160s01c != null) {
						L140S02C l140s02c = clsService.findL140S02C(
								editC160s01c.getRefmainId(),
								editC160s01c.getSeq());
						C160S01C c160s01c = (C160S01C) model;
						// 自動扣帳
						editC160s01c.setAutoPay(c160s01c.getAutoPay());
						editC160s01c.setAtpayNo(c160s01c.getAtpayNo());
						// 自動進帳
						editC160s01c.setAutoRct(c160s01c.getAutoRct());
						// 進帳金額

						if ("Y".equals(Util.trim(c160s01c.getAutoRct()))) {
							editC160s01c.setRctAMT(editC160s01c
									.getApprovedAmt());
						}
						editC160s01c.setAccNo(c160s01c.getAccNo());

						// 進帳日期
						editC160s01c.setRctDate(c160s01c.getRctDate());
						// 行銷行
						editC160s01c.setEfctBH(c160s01c.getEfctBH());
						// 扣帳方式
						editC160s01c.setPayType(c160s01c.getPayType());
						// 期間(動用起迄)
						editC160s01c.setUseLine(c160s01c.getUseLine());
						editC160s01c
								.setUseStartDate(c160s01c.getUseStartDate());
						editC160s01c.setUseEndDate(c160s01c.getUseEndDate());
						editC160s01c.setUseMonth(c160s01c.getUseMonth());
						editC160s01c.setUseOther(c160s01c.getUseOther());
						// 延遲息/違約金
						editC160s01c.setDRateAdd(c160s01c.getDRateAdd());
						editC160s01c.setDMonth1(c160s01c.getDMonth1());
						editC160s01c.setDRate1(c160s01c.getDRate1());
						editC160s01c.setDMonth2(c160s01c.getDMonth2());
						editC160s01c.setDRate2(c160s01c.getDRate2());
						// (1)授信期間

						editC160s01c.setLnStartDate(c160s01c.getLnStartDate());
						// 授信期間-結束日期(開始日期加上期間年月)
						if(editC160s01c.getLnStartDate()!=null){
							Date Lnenddate = CapDate.addMonth(
									editC160s01c.getLnStartDate(),
									editC160s01c.getYear() * 12
											+ editC160s01c.getMonth());
							if (check_intway_subjCode(
									Util.trim(l140s02c.getIntWay()),
									Util.trim(c160s01c.getSubjCode()))) {
								Lnenddate = CapDate.shiftDays(Lnenddate, -1);
							}
							editC160s01c.setLnEndDate(Lnenddate);
						}
						// (2)所有權取得。
						editC160s01c.setGetDate(c160s01c.getGetDate());

						service.save(editC160s01c);
					}
				}
				// service.copyC160S01C((C160S01C) model);
			}
		}

		return result;
	}// ;

	/**
	 * getModel
	 * 
	 * @param formName
	 * @param oid
	 * @return
	 */
	private GenericBean getModel(String formName, String oid) {
		GenericBean result = null;
		if (ClsConstants.FORM.動審表_主檔.equals(formName)) {
			result = service.findModelByOid(C160M01A.class, oid);
		} else if (ClsConstants.FORM.動審表_額度明細表.equals(formName)) {
			result = service.findModelByOid(C160M01B.class, oid);
		} else if (ClsConstants.FORM.動審表_擔保品資料.equals(formName)) {
			result = service.findModelByOid(C160S01A.class, oid);
		} else if (ClsConstants.FORM.動審表_主從債務人資料.equals(formName)) {
			result = service.findModelByOid(C160S01B.class, oid, true);
		} else if (ClsConstants.FORM.動審表_個金產品種類.equals(formName)) {
			result = service.findModelByOid(C160S01C.class, oid);
		} else if (ClsConstants.FORM.動審表_代償轉貸借新還舊主檔.equals(formName)) {
			result = service.findModelByOid(C160S01E.class, oid);
		} else if (ClsConstants.FORM.動審表_代償轉貸借新還舊明細.equals(formName)) {
			result = service.findModelByOid(C160S01F.class, oid, true);
		}
		return result;
	}

	private Set<String> excel_validIdSet() {
		Set<String> r = new HashSet<String>();
		for (CodeType obj : cts.findByCodeTypeList("cls1161_xlsId")) {
			String target = Util.trim(obj.getCodeValue());
			if (Util.isNotEmpty(target)) {
				r.add(target);
			}
		}
		return r;
	}

	/**
	 * 匯入EXCEL作業 step1
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult importExcelStep1(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		JSONObject formJson = DataParse.toJSON(params
				.getString("CaseType3Form"));
		String excelId = Util.trim(formJson.get("excelId"));
		DocFile docFile = dfs.read(excelId);
		if (docFile != null) {
			Properties prop = PropUtil
					.getProperties("cls/excelFields.properties");

			Workbook workbook = null;
			String filePath = dfs.getFilePath(docFile);
			Set<String> validIdSet = excel_validIdSet();
			try {
				List<GenericBean> list = new ArrayList<GenericBean>();

				workbook = WorkbookFactory.create(new File(filePath));
				Sheet sheet = workbook.getSheetAt(0);

				// C160M01A．動用審核表主檔
				String oid = Util.trim(formJson.get(EloanConstants.OID));
				C160M01A c160m01a = service.findModelByOid(C160M01A.class, oid);
				if (c160m01a != null) {
					// C160S01D．個金動審表匯入明細檔
					service.delete(service.getC160S01DList(mainId, true));
					// 取得EXCEL資料
					JSONObject data = ClsUtil.getC160m01fData(sheet, prop);
					String custId = Util.trim(data.get("custId"));
					String dupNo = Util.trim(data.get("dupNo"));
					if (!validIdSet.contains(custId)) {
						/*
						 * XXX 暫先略過
						throw new CapMessageException(
								"統編：" + custId + "不可整批匯入", getClass());
						*/
					}
					// 檢查統編和重覆序號(若不同則刪除個金動審表匯入明細檔)
					if (!Util.trim(c160m01a.getCustId()).equals(custId)
							|| !Util.trim(c160m01a.getDupNo()).equals(dupNo)) {
						// C160S01D．個金動審表匯入明細檔
						// service.delete(service.findListByMainId(C160S01D.class,
						// mainId));
						c160m01a.setCaseNo(null);
						c160m01a.setChildrenSeq(null);
						c160m01a.setChildrenId(null);
						c160m01a.setChildrenName(null);
						c160m01a.setIsUseChildren(UtilConstants.DEFAULT.否);
					}
					// add all data
					formJson.putAll(data);
					DataParse.toBean(formJson, c160m01a);
					c160m01a.setFinCount(0);
					c160m01a.setTotCount(0);
					list.add(c160m01a);

					// C160M01F．個金動審表匯入主檔
					C160M01F c160m01f = service.findModelByMainId(
							C160M01F.class, mainId);
					DataParse.toBean(data, c160m01f);
					c160m01f.setMainId(mainId);
					list.add(c160m01f);

					// C160S01G．個金動審表匯入利率檔
					service.delete(service.findListByMainId(C160S01G.class,
							mainId));
					list.addAll(ClsUtil.getC160s01gList(mainId, sheet, prop));
					// 刪除現有C120S01B_C120S01E_C120S01Q
					service.deleteC120S01B_C120S01E_C120S01QByMainId(mainId);
				}
				service.save(list);
				result.set("CaseType3Form", DataParse.toResult(c160m01a));

				workbook.close();
			} catch (CapMessageException e) {
				throw e;
			} catch (Exception e) {
				logger.error("parseExcel EXCEPTION!!", e);
				throw new CapMessageException(e, getClass());
			} finally {
				try {
					if (workbook != null) {
						workbook.close();
						workbook = null;
					}
				} catch (Exception e) {
					logger.debug("Workbook close EXCEPTION!", getClass());
				}
			}
		} else {
			throw new CapMessageException("請先上傳EXCEL", getClass());
		}

		return result;
	}// ;

	final String DATAFORMAT = "yyyy/MM/dd";

	/**
	 * 匯入EXCEL作業 step2
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult importExcelStep2(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		JSONObject formJson = DataParse.toJSON(params
				.getString("CaseType3Form"));
		String excelId = Util.trim(formJson.get("excelId"));
		DocFile docFile = dfs.read(excelId);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// C160M01A．動用審核表主檔
		String oid = Util.trim(formJson.get(EloanConstants.OID));
		C160M01A c160m01a = service.findModelByOid(C160M01A.class, oid);

		if (docFile != null && c160m01a != null) {
			Properties prop = PropUtil
					.getProperties("cls/excelFields.properties");

			Workbook workbook = null;
			String filePath = dfs.getFilePath(docFile);
			try {
				List<GenericBean> list = new ArrayList<GenericBean>();

				workbook = WorkbookFactory.create(new File(filePath));
				Sheet sheet = workbook.getSheetAt(0);

				// 處理明細
				int totCount = c160m01a.getTotCount();
				int start = params.getInt("start", ClsConstants.動審表明細起始數);
				/*
				 * 在前端 CLS1161S01Panel.js 裡, 有分批去處理  panelAction.importExcelStep(2);
				 * 若 excel 總共300筆, 會依每批100筆去處理
				 * 前端看到的變化
				 * (1)完成筆數   	  0/100
				 * (2)完成筆數   	  0/200
				 * (3)完成筆數   	300/300
				 * 下面的 int end = start + 100; 和前端有關
				 */
				int end = start + 100;
				// UPGRADETODO: JXL 改寫為 Apache POI - 修改取得行數的方式
				int rows = sheet.getLastRowNum() + 1;
				
				if (end > rows){
					end = rows;
				}
				
				if(true){
					int max_limit_for_AML = 150;
					if(rows>ClsConstants.動審表明細起始數 + max_limit_for_AML){
						throw new CapMessageException("配合AML系統名單掃描，上傳筆數請勿超過"+max_limit_for_AML
								+"筆", getClass());
					}	
				}
				
				if(clsService.is_function_on_codetype("J-108-0195")){
					C160M01F c160m01f = service.findModelByMainId(C160M01F.class, mainId);
					if(c160m01f!=null && c160m01f.getDRateAdd()!=null && c160m01f.getDRateAdd().compareTo(BigDecimal.ZERO)!=0){
						throw new CapMessageException("依消金業務處108/09/16兆銀消金字第1080000164號函 ，新版契約書自108/09/20生效。計收延遲利息加碼應為０%。", getClass());
					}
				}
				
				// 取得整批滙入的評等結果
				List<C120S01Q> s01qList = service
						.findC120S01QByC160M02AJcicEtchIn1Month(user.getUnitNo());
				
				Map<String, String> ntCode_map = clsService.get_codeTypeWithOrder("CountryCode");
				
				String[] c122m01a_applyKindArr = new String[]{UtilConstants.C122_ApplyKind.P};
				Date c122m01a_sinceDate = CapDate.addMonth(CapDate.getCurrentTimestamp(), -5);
				
				for (int row = start; row < end; row++) {
					JSONObject json = ClsUtil.getC160s01dData(sheet, prop, row); //透過 excelFields.properties 對應到 c160s01d

					if (!json.isEmpty()) {
						totCount++; // 計算總數
						// if (!ClsConstants.SUCCESS.equals(Util.trim(json
						// .get("result"))) && !json.isEmpty()) {
						// UPGRADETODO: JXL 改寫為 Apache POI - 修改儲存格格式處理
						// cell format
						Row sheetRow = sheet.getRow(row) != null ? sheet.getRow(row) : sheet.createRow(row);
						Cell sourceCell = sheetRow.getCell(LAST_XLS_KEY-2) != null ? sheetRow.getCell(LAST_XLS_KEY-2) : sheetRow.createCell(LAST_XLS_KEY-2);
						CellStyle format = sourceCell.getCellStyle() != null ? sourceCell.getCellStyle() : sheet.getWorkbook().createCellStyle();
						Font font = sheet.getWorkbook().createFont();
						font.setFontName("Arial");
						font.setFontHeightInPoints((short) 12);
						font.setBold(true);
						// check
						C160S01D c160s01d = new C160S01D();
						// C160S01D c160s01d = service.findByUniqueKey(mainId,
						// Util.trim(json.get("staffNo")),
						// Util.trim(json.get("custId")));
						DataParse.toBean(json, c160s01d);
						String checkMsg = ClsUtil.checkData(c160s01d, ntCode_map);
						boolean isFinalRatingFinish = false; // 當 if (Util.isEmpty(checkMsg)) { 才會去更新
						if (Util.isEmpty(checkMsg)) {
							c160s01d.setMainId(mainId);
							checkMsg = service.setCntrNo(c160s01d);
							C120S01Q duplicateS01Q = null;

							for (C120S01Q s01q : s01qList) {
								if (c160s01d.getCustId().equals(
										s01q.getCustId() + s01q.getDupNo())) {
									// 複製C120S01Q for 評等
									duplicateS01Q = new C120S01Q();
									DataParse.copy(s01q, duplicateS01Q);
									duplicateS01Q.setMainId(mainId);
									//XXX 刪除評等時依SRCMAINID為KEY檢查,當已引進不得刪除該評等
									duplicateS01Q.setSrcMainId(s01q.getMainId());
									list.add(duplicateS01Q);
																										
									if(true){
										// UPGRADETODO: JXL 改寫為 Apache POI - 回填非房貸模型初始評等
										Row currentRow = sheet.getRow(row) != null ? sheet.getRow(row) : sheet.createRow(row);
										Cell labelCell = currentRow.getCell(LAST_XLS_KEY+5) != null ? currentRow.getCell(LAST_XLS_KEY+5) : currentRow.createCell(LAST_XLS_KEY+5);
										labelCell.setCellValue(Util.trim(s01q.getGrade1()));
										// 保持原有的儲存格格式
										if (ClsUtil.getCellFormat(labelCell) != null) {
											labelCell.setCellStyle(ClsUtil.getCellFormat(labelCell));
										}
									}
									if(true){
										// UPGRADETODO: JXL 改寫為 Apache POI - 回填升降等
										String grade2 = "";
										if (Util.equals(s01q.getAdjustStatus(), "1")) {
											grade2 = Util.trim(s01q.getGrade2());
										} else if (Util.equals(s01q.getAdjustStatus(), "2")) {
											grade2 = "-"+ Util.trim(s01q.getGrade2());
										}	
										Row currentRow = sheet.getRow(row) != null ? sheet.getRow(row) : sheet.createRow(row);
										Cell gradeCell = currentRow.getCell(LAST_XLS_KEY+6) != null ? currentRow.getCell(LAST_XLS_KEY+6) : currentRow.createCell(LAST_XLS_KEY+6);
										gradeCell.setCellValue(grade2);
										if (ClsUtil.getCellFormat(gradeCell) != null) {
											gradeCell.setCellStyle(ClsUtil.getCellFormat(gradeCell));
										}
									}
									// UPGRADETODO: JXL 改寫為 Apache POI - 回填非房貸模型最終評等
									isFinalRatingFinish = !Util.isEmpty(s01q.getGrade3());
									Cell referenceCell = getPOICell(sheet, LAST_XLS_KEY+7, row);
									setPOICellValue(sheet, LAST_XLS_KEY+7, row, Util.trim(s01q.getGrade3()), referenceCell);

									// 票交所與聯徵負面資訊									
									// UPGRADETODO: JXL 改寫為 Apache POI - 票交所與聯徵負面資訊
									Cell negativeInfoRefCell = getPOICell(sheet, LAST_XLS_KEY+8, row);
									setPOICellValue(sheet, LAST_XLS_KEY+8, row, Util.trim(ClsUtil.c120s01q_negativeInfo(s01q)), negativeInfoRefCell);

									// 聯徵查詢日期
									String jcicDate = CapDate.formatDate(
											s01q.getJcicQDate(), DATAFORMAT);
									// UPGRADETODO: JXL 改寫為 Apache POI - 聯徵查詢日期
									Cell jcicDateRefCell = getPOICell(sheet, LAST_XLS_KEY+9, row);
									setPOICellValue(sheet, LAST_XLS_KEY+9, row, jcicDate, jcicDateRefCell);

									// 票信查詢日期
									String etchDate = CapDate.formatDate(
											s01q.getEtchQDate(), DATAFORMAT);
									// UPGRADETODO: JXL 改寫為 Apache POI - 票信查詢日期
									Cell etchDateRefCell = getPOICell(sheet, LAST_XLS_KEY+10, row);
									setPOICellValue(sheet, LAST_XLS_KEY+10, row, etchDate.toString(), etchDateRefCell);

									// 複製C120S01E for ejcic/etch
									C120S01E s01e = cls1141Service
											.findC120S01EByUniqueKey(
													s01q.getMainId(),
													s01q.getCustId(),
													s01q.getDupNo());
									if (s01e != null) {
										C120S01E duplicateS01E = new C120S01E();
										DataParse.copy(s01e, duplicateS01E);
										duplicateS01E.setMainId(mainId);
										list.add(duplicateS01E);
										
										// 回填退票、拒往、票信查詢日期(自行輸入)
										if(duplicateS01E!=null){
											boolean replaceXLS_ETCH_column = false;
											if(CrsUtil.isNull_or_ZeroDate(duplicateS01E.getEChkDDate())
													&& CrsUtil.isNOT_null_and_NOTZeroDate(duplicateS01E.getEChkQDate())){
												/*
												  大多數的狀況：{ECHKDDATE=0001-01-01},{ECHKQDATE=yyyy-MM-dd}
												 */
												replaceXLS_ETCH_column = true;
											}
											if(CrsUtil.isNOT_null_and_NOTZeroDate(duplicateS01E.getEChkDDate())
													&& CrsUtil.isNOT_null_and_NOTZeroDate(duplicateS01E.getEChkQDate())){
												/*
												   客戶在向 002BR 申請中鋼整批消貸的同時，還有 某個分行 有去查詢此客戶的EJCIC/ETCH
												   {ECHKDDATE=yyyy-M1-d1},{ECHKQDATE=yyyy-M2-d2}
												   => EChkDDate 非空值
												 */
												replaceXLS_ETCH_column = true;
											}
											if(replaceXLS_ETCH_column){
												if(true){
													// UPGRADETODO: JXL 改寫為 Apache POI - chkItem1a
													Cell refCell1a = getPOICell(sheet, LAST_XLS_KEY+2, row);
													setPOICellValue(sheet, LAST_XLS_KEY+2, row, convert_chkItem(Util.trim(s01q.getChkItem1a())), refCell1a);
												}
												if(true){
													// UPGRADETODO: JXL 改寫為 Apache POI - chkItem1b
													Cell refCell1b = getPOICell(sheet, LAST_XLS_KEY+3, row);
													setPOICellValue(sheet, LAST_XLS_KEY+3, row, convert_chkItem(Util.trim(s01q.getChkItem1b())), refCell1b);
												}
												if(true){													
													// UPGRADETODO: JXL 改寫為 Apache POI - EChkQDate
													Cell refCellDate = getPOICell(sheet, LAST_XLS_KEY+4, row);
													setPOICellValue(sheet, LAST_XLS_KEY+4, row, Util.trim(CapDate.formatDate(duplicateS01E.getEChkQDate(), DATAFORMAT)), refCellDate);
												}
											}
										}	
									}
									
									//===================================
									// 複製C120M01A for 信用卡張數
									C120M01A m01a = cls1131Service
											.findModelByKey(C120M01A.class,
													s01q.getMainId(),
													s01q.getCustId(),
													s01q.getDupNo());
									if (m01a != null) {
										C120M01A duplicateM01A = new C120M01A();
										DataParse.copy(m01a, duplicateM01A);
										duplicateM01A.setMainId(mainId);
										list.add(duplicateM01A);										
									}
									
									//===================================
									// 複製C120S01B for 年資
									C120S01B s01b = cls1131Service
											.findModelByKey(C120S01B.class,
													s01q.getMainId(),
													s01q.getCustId(),
													s01q.getDupNo());
									if (s01b != null) {
										C120S01B duplicateS01B = new C120S01B();
										DataParse.copy(s01b, duplicateS01B);
										duplicateS01B.setMainId(mainId);
										list.add(duplicateS01B);

										// UPGRADETODO: JXL 改寫為 Apache POI - 回填年薪
										Cell payAmtRefCell = getPOICell(sheet, LAST_XLS_KEY-1, row);
										setPOICellValue(sheet, LAST_XLS_KEY-1, row, Util.trim(s01b.getPayAmt()), payAmtRefCell);

										// UPGRADETODO: JXL 改寫為 Apache POI - 回填其他所得
										Cell othAmtRefCell = getPOICell(sheet, LAST_XLS_KEY, row);
										setPOICellValue(sheet, LAST_XLS_KEY, row, Util.trim(s01b.getOthAmt()), othAmtRefCell);

										// UPGRADETODO: JXL 改寫為 Apache POI - 回填年資
										Cell seniorityRefCell = getPOICell(sheet, LAST_XLS_KEY+1, row);
										setPOICellValue(sheet, LAST_XLS_KEY+1, row, LMSUtil.pretty_numStr(s01b.getSeniority()), seniorityRefCell);
										if(true){
											if(s01b.getPayAmt()!=null){
												c160s01d.setAnnuity(s01b.getPayAmt().intValue());
											}
											if(s01b.getOthAmt()!=null){
												c160s01d.setOthincome(s01b.getOthAmt().intValue());
											}
											if(true){
												String jobClass = "";
												if(true){ //自非房貸3.0後，多出 職業大類_小類_職稱 的欄位
													String raw_jobClass = Util.trim(
																  Util.trim(s01b.getJobType1())
																+ Util.trim(s01b.getJobType2())
																+ Util.trim(s01b.getJobTitle()));
													if(Util.isNotEmpty(raw_jobClass) && raw_jobClass.length()==4){
														jobClass = 	raw_jobClass;
													}													
												}
												c160s01d.setJobClass(jobClass);
											}
										}
									}
									break;
								}
							}
							if (duplicateS01Q == null) {
								checkMsg += "查無聯徵/票信在1個月內的評等資料，請先至個金徵信作業->整批滙入上傳。";
							}
							list.add(c160s01d);
						} // end-if (Util.isEmpty(checkMsg)) {

						// UPGRADETODO: JXL 改寫為 Apache POI - 回填額度序號
						Cell cntrNoRefCell = getPOICell(sheet, LAST_XLS_KEY-15, row);
						setPOICellValue(sheet, LAST_XLS_KEY-15, row, Util.trim(c160s01d.getCntrNo()), cntrNoRefCell);

						String res = Util.isEmpty(checkMsg) ? ClsConstants.SUCCESS
								: checkMsg;
						// 將結果回存c160s01d
						c160s01d.setResult(res);
						if(true){
							String c122m01a_mainId = "";
							C122M01A c122m01a = clsService.findLatestC122M01A_for_C160S01D(c160m01a.getOwnBrId()
									, StringUtils.substring(c160s01d.getCustId(), 0, 10)
									, c122m01a_applyKindArr
									, c122m01a_sinceDate
									, ClsConstants.C122M01A_StatFlag.已作廢);
							if(c122m01a!=null){
								c122m01a_mainId = c122m01a.getMainId();	
							}
							c160s01d.setC122m01a_mainId(c122m01a_mainId);//若為線上申貸且同時線上對保
						}
						// UPGRADETODO: JXL 改寫為 Apache POI - 結果回填Excel
						// 設定字型顏色：成功為藍色，失敗為紅色
						font.setColor(Util.isEmpty(checkMsg) ? IndexedColors.BLUE.getIndex() : IndexedColors.RED.getIndex());
						format.setFont(font);
						Cell resultCell = getPOICell(sheet, LAST_XLS_KEY-2, row);
						resultCell.setCellValue(res); // 若都正常, 系統填入 "Success"
						resultCell.setCellStyle(format);
						if(true){
							int col = LAST_XLS_KEY+11;
							// UPGRADETODO: JXL 改寫為 Apache POI - 創建 CellStyle
							Cell sourceCell2 = getPOICell(sheet, col, row);
							CellStyle format2 = sheet.getWorkbook().createCellStyle();
							if (sourceCell2.getCellStyle() != null) {
								format2.cloneStyleFrom(sourceCell2.getCellStyle());
							}
							// UPGRADETODO: JXL 改寫為 Apache POI - 創建 Font
							Font font2 = sheet.getWorkbook().createFont();
							font2.setFontName("Arial");
							font2.setFontHeightInPoints((short) 12);
							font2.setBold(true);
							// 回填是否辦妥評等
							// UPGRADETODO: JXL 改寫為 Apache POI - 設定字型顏色
							font2.setColor(isFinalRatingFinish ? IndexedColors.BLUE.getIndex() : IndexedColors.RED.getIndex());
							format2.setFont(font2);
							// UPGRADETODO: JXL 改寫為 Apache POI - 設定儲存格值和格式
							Cell ratingCell = getPOICell(sheet, col, row);
							ratingCell.setCellValue(isFinalRatingFinish ? ClsConstants.SUCCESS : ClsConstants.ERROR);
							ratingCell.setCellStyle(format2);
						}
					}
				}
				service.save(list);

				// C160M01A．動用審核表主檔
				c160m01a.setTotCount(totCount);
				service.save(c160m01a);
				result.set("CaseType3Form", DataParse.toResult(c160m01a));

				result.set("start", start);
				result.set("end", end);
				result.set("rows", rows);

				workbook.close();
			} catch (Exception e) {
				logger.error("parseExcel EXCEPTION!!", e);
				throw new CapMessageException(e, getClass());
			} finally {
				try {
					if (workbook != null) {
						workbook.close();
						workbook = null;
					}
				} catch (Exception e) {
					logger.debug("Workbook close EXCEPTION!", getClass());
				}
			}
		} else {
			throw new CapMessageException("請先上傳EXCEL", getClass());
		}

		return result;
	}// ;
	
	private String convert_chkItem(String s){
		if(Util.equals("1", s)){
			return "Y";
		}else if(Util.equals("2", s)){
			return "N";
		}
		return s;
	}
	
	/**
	 * 匯入EXCEL作業 step3
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult importExcelStep3(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		JSONObject formJson = DataParse.toJSON(params
				.getString("CaseType3Form"));
		// C160M01A．動用審核表主檔
		String oid = Util.trim(formJson.get(EloanConstants.OID));
		C160M01A c160m01a = service.findModelByOid(C160M01A.class, oid);
		if (c160m01a != null) {
//			int finCount = service.getC160S01DCount(mainId);
			// ========================================
			// 可能XLS的資料都完整，但JCIC的日期超過			
			int finCount = service.getC160S01D_Success_Count(mainId, ClsConstants.SUCCESS);
			c160m01a.setFinCount(finCount);
			service.save(c160m01a);
			result.set("CaseType3Form", DataParse.toResult(c160m01a));
		}

		return result;
	}// ;

	/**
	 * 處理上傳之檔案
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult importExcelCallback(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		// String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		JSONObject formJson = DataParse.toJSON(params
				.getString("CaseType3Form"));
		// C160M01A．動用審核表主檔
		String oid = Util.trim(formJson.get(EloanConstants.OID));
		C160M01A c160m01a = service.findModelByOid(C160M01A.class, oid);
		if (c160m01a != null) {
			DataParse.toBean(formJson, c160m01a);
			if (Util.isEmpty(c160m01a.getApprovedNo()))
				throw new CapMessageException("請選擇本案核准之編號", getClass());

			// 清除子戶相關資料
			if (UtilConstants.DEFAULT.否.equals(Util.trim(c160m01a
					.getIsUseChildren()))) {
				c160m01a.setChildrenId(null);
				c160m01a.setChildrenName(null);
				c160m01a.setChildrenSeq(null);
			} else {
				if (Util.isEmpty(c160m01a.getChildrenId())
						|| Util.isEmpty(c160m01a.getChildrenName())) {
					throw new CapMessageException("請輸入或引入子戶統編及名稱", getClass());
				}

				String childrenSeq = service.getChildrenSeq(formJson);
				c160m01a.setChildrenSeq(childrenSeq);
			}
			// 案件號碼
			lmsService.setCaseNo(c160m01a);

			// 自動取得批號
			if (Util.isEmpty(c160m01a.getPackNo())) {
				String packNo = mps
						.getMaxAmtappno(c160m01a.getCustId(),
								c160m01a.getDupNo(),
								MegaSSOSecurityContext.getUnitNo());
				c160m01a.setPackNo(packNo);
			}
			service.save(c160m01a);
		}

		return result;
	}// ;

	/**
	 * 給號
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult getNumber(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String form = Util.trim(params.getString("C160M01AForm"));
		String approvedNo = Util.trim(params.getString("approvedNo"));
		JSONObject json = DataParse.toJSON(form);
		String oid = Util.trim(json.get("oid"));

		C160M01A c160m01a = service.findModelByOid(C160M01A.class, oid);
		String loanNo = "";
		if (Util.equals(c160m01a.getCaseType(), "2")) {
			loanNo = Util.trim(c160m01a.getLoanMasterNo());
		} else if (Util.equals(c160m01a.getCaseType(), "3")) {
			loanNo = Util.trim(approvedNo);
		}
		if (c160m01a != null) {
			String packNo = mps.getMaxAmtappno2(loanNo);
			c160m01a.setPackNo(packNo);
			service.save(c160m01a);
			result.set("number", packNo);
		}

		return result;
	}// ;

	/**
	 * 給號
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult setNumber(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String form = Util.trim(params.getString("C160M01AForm"));
		JSONObject json = DataParse.toJSON(form);
		String oid = Util.trim(json.get("oid"));
		C160M01A c160m01a = service.findModelByOid(C160M01A.class, oid);
		if (c160m01a != null) {
			String packNo = Util.trim(json.get("packNo"));
			c160m01a.setPackNo(packNo);
			service.save(c160m01a);
			result.set("number", packNo);
		}

		return result;
	}// ;

	/**
	 * 重新上傳
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public IResult ReUpDate(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oidArr = Util.trim(params.getString("oid"));
		// JSONObject json = DataParse.toJSON(form);
		// String oid = Util.trim(json.get("oid"));
		String[] dataSplit = oidArr.split("\\|");
		for(String oid: dataSplit){
			C160M01A c160m01a = service.findModelByOid(C160M01A.class, oid);
			service.uploadMIS_DW(c160m01a);
		}		
		return result;
	}// ;

	public IResult complement_ELF504(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oidArr = Util.trim(params.getString("oid"));
		if(Util.isEmpty(oidArr)){
			String brArr = Util.trim(params.getString("br"));
			String[] dataSplit = brArr.split("\\|");
			for(String brNo : dataSplit){
				ISearch iSearch = clsService.getMetaSearch();
				iSearch.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", brNo);
				iSearch.addSearchModeParameters(SearchMode.GREATER_THAN, "approveTime", "2010-01-01 00:00:00");
				iSearch.addOrderBy("approveTime");
				iSearch.setMaxResults(Integer.MAX_VALUE);
				
				Page<? extends GenericBean> c160page = clsService.findPage(C160M01A.class, iSearch);
				for(GenericBean bean : c160page.getContent()){
					C160M01A c160m01a = (C160M01A)bean;
					service.complement_ELF504(c160m01a);
				}
			}
			
		}else{
			String[] dataSplit = oidArr.split("\\|");
			for(String oid: dataSplit){
				C160M01A c160m01a = service.findModelByOid(C160M01A.class, oid);
				service.complement_ELF504(c160m01a);
			}			
		}
		
		return result;
	}
	
	@SuppressWarnings("unchecked")
	public IResult ReUpDateDW(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString("oid"));
		
		C160M01A c160m01a = service.findModelByOid(C160M01A.class, oid);
		if (Util.equals(c160m01a.getCaseType(), "3")) {// 整批匯入上傳
			List<C160S01D> c160s01dList = (List<C160S01D>)service.findListByMainId(C160S01D.class, c160m01a.getMainId());
			for (C160S01D c160s01d : c160s01dList) {
				service.uploadDW(c160m01a, c160s01d);
			}
		}
		return result;
	}
	
	/**
	 * 共同行銷查詢
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult queryJoinMark(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String form = Util.trim(params.getString("C160M01BForm"));
		JSONObject json = DataParse.toJSON(form);
		String oid = Util.trim(json.get("oid"));
		C160M01B c160m01b = service.findModelByOid(C160M01B.class, oid);
		if (c160m01b != null) {
			String custId = Util.trim(c160m01b.getCustId());
			String dupNo = Util.trim(c160m01b.getDupNo());
			String value = service.getJoinMarkDesc(custId, dupNo);
			c160m01b.setJoinMarketingDate(new Date());
			c160m01b.setJoinMarkDesc(value);
			service.save(c160m01b);
			result.set("message", value);
			result.set("joinMarketingDate", c160m01b.getJoinMarketingDate());
		}

		return result;
	}// ;

	/**
	 * 共同行銷查詢2(多筆)
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	@DomainAuth(AuthType.Modify)
	public IResult queryJoinMark2(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String cust = Util.trim(params.getString("cust"));
		JSONObject custJson = Util.isEmpty(cust) ? new JSONObject()
				: JSONObject.fromObject(cust);

		result.set("message", build_joinMarkDesc(custJson));

		return result;
	}// ;

	private String build_joinMarkDesc(JSONObject custJson){
		StringBuffer message = new StringBuffer();
		
		Set<String> set = (Set<String>) custJson.keySet();
		for (String key : set) {
			JSONObject json = custJson.getJSONObject(key);
			String custId = Util.trim(json.get("custId"));
			String dupNo = Util.trim(json.get("dupNo"));
			String custName = Util.trim(json.get("custName"));
			String value = service.getJoinMarkDesc(custId, dupNo);
			message.append(message.length() > 0 ? EloanConstants.HTML_NEWLINE
					: "");
			message.append(custId).append(" ");
			message.append(dupNo).append(" ");
			message.append(custName).append(" ");
			message.append(value);
		}
		return message.toString();
	}
	
	/**
	 * 流程
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult flow(PageParameters params) throws CapException {
		// 先儲存再走流程
		SimpleContextHolder.put("reloadSaveLog", UtilConstants.DEFAULT.否);
		save(params);
		return flowAction(params);
	}// ;

	/**
	 * 流程
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult flowAction(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String oid = Util.trim(params.getString(EloanConstants.OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		C160M01A c160m01a = service.findModelByOid(C160M01A.class, oid);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		if (c160m01a != null) {
			String activity = Util.trim(params.getString("activity"));
			String docStatus = Util.trim(c160m01a.getDocStatus());
			String target = null;
			if ("send".equals(activity)) {
				if (CLSDocStatusEnum.編製中.getCode().equals(docStatus)) {
					// 儲存主管清單
					List<GenericBean> saveList = new ArrayList<GenericBean>();
					service.delete(service.findListByMainId(C160M01E.class,
							mainId));
					String form = Util.trim(params.getString("C160M01EForm"));
					JSONObject json = DataParse.toJSON(form);
					int numPerson = Util.parseInt(json.get("numPerson"));
					for (int i = 1; i <= numPerson; i++) {
						C160M01E c160m01e = new C160M01E();
						c160m01e.setMainId(mainId);
						c160m01e.setStaffJob(UtilConstants.STAFFJOB.授信主管L3);
						String staffNo = "boss" + String.valueOf(i);
						c160m01e.setStaffNo(Util.trim(json.get(staffNo)));
						saveList.add(c160m01e);
					}
					C160M01E c160m01e = new C160M01E();
					c160m01e.setMainId(mainId);
					c160m01e.setStaffJob(UtilConstants.STAFFJOB.單位授權主管L5);
					c160m01e.setStaffNo(Util.trim(json.get("manager")));
					saveList.add(c160m01e);
					service.save(saveList);
					// save main
					c160m01a.setManagerId(c160m01e.getStaffNo());
					service.save(c160m01a);
					// check
				} else if (CLSDocStatusEnum.先行動用_已覆核.getCode()
						.equals(docStatus)) {
					// String managerId =
					// Util.trim(params.getString("managerId"));
					// c160m01a.setManagerId(managerId);
				}
				// service.save(c160m01a);
			} else if ("accept".equals(activity)) {
				if (user.getUserId().equals(c160m01a.getApprId())) {
					// EFD0053=WARN|覆核人員不可與「經辦人員或其它覆核人員」為同一人|
					throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
				}
				String approveDate = Util.trim(params.getString("approveDate"));
				if (Util.isNotEmpty(approveDate)) {
					Date approveTime = Util.parseDate(approveDate);

					//J-111-0548 Web eloan 個金授信-動用審核表(一般)及動用審核表(團貸)之核准日期，請增加檢核機制「洗錢防制與打擊資助恐怖主義(AML/CFT)的資料查詢日期<=覆核日期<=覆核當日日期」
					//by 008831 112/01/06
					List<L120S09A> l120s09a_list = clsService.findL120S09A(mainId);
					Date blackListQDate = LMSUtil.maxQueryDateS(l120s09a_list);	
					
					if (blackListQDate != null && approveTime.compareTo(blackListQDate) < 0) {
						throw new CapMessageException("核定日需大於等於洗錢防制與打擊資助恐怖主義(AML/CFT)的資料查詢日期", getClass());
					}
					
					if (approveTime.compareTo(CapDate.getCurrentTimestamp()) > 0) {
						throw new CapMessageException("核定日需小於等於本日", getClass());
					}
				  
					if (CLSDocStatusEnum.先行動用_待覆核.getCode().equals(docStatus)) {
						logger.trace("先行動用_待覆核,not update ApproveTime:"
								+ c160m01a.getApproveTime());
					} else {
						c160m01a.setApproveTime(new Timestamp(approveTime
								.getTime()));
						if (c160m01a.getOrgApproveTime() == null) {
							c160m01a.setOrgApproveTime(c160m01a.getApproveTime());
						}
					}

					service.save(c160m01a);
				} else {
					throw new CapMessageException("請輸入核定日", getClass());
				}

				if (CLSDocStatusEnum.待覆核.getCode().equals(docStatus)) {
					if (UtilConstants.DEFAULT.是.equals(c160m01a.getUseType())) {
						target = "先行動用";
					} else {
						target = "核准";
					}
				} else if (CLSDocStatusEnum.先行動用_待覆核.getCode()
						.equals(docStatus)) {
					target = "先行動用核准";
					// 先行動用呈核及控制表檔
					C160M01D c160m01d = service.findModelByMainId(
							C160M01D.class, mainId);
					if (c160m01d != null) {
						c160m01d.setBfReCheckDate(new Date());
						service.save(c160m01d);
					}
				}
				/* J-110-0081_10702_B1001 Web e-Loan調整PLOAN報表、自動更新案件狀態
				 * J-111-0586_11565_B1001 修改為值接抓C160M01B,因為SrcMainId只有一般案會有填值
				 * 一張動審表C160M01A，可能同時對應到多個額度明細表C160M01B
				 * */
//				L120M01A l120m01a = clsService.findL120M01A_mainId(c160m01a.getSrcMainId());
				List<C160M01B> c160m01b_list = clsService.findC160M01B_mainId(mainId);
				List<L140M01A> listL140m01a = new ArrayList<L140M01A>();
				if(c160m01b_list != null && c160m01b_list.size()>0){
					for (C160M01B c160m01b : c160m01b_list) {
						if(Util.isNotEmpty(c160m01b)){
							L140M01A l140m01a = clsService.findL140M01A_mainId(c160m01b.getRefmainId());
							if(Util.isNotEmpty(l140m01a)){
								listL140m01a.add(l140m01a);
							}
						}
					}
				}
				if(listL140m01a.size()>0){
					clsService.sync_c122m01a_applyKindPorE_relateColumn(ClsConstants.C122M01A_StatFlag.動審表已覆核, listL140m01a);
				}
			} else if ("back".equals(activity)) {
				if (user.getUserId().equals(c160m01a.getApprId())) {
					// EFD0053=WARN|覆核人員不可與「經辦人員或其它覆核人員」為同一人|
					throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
				}
				if (CLSDocStatusEnum.待覆核.getCode().equals(docStatus)) {
					target = "退回";
				} else if (CLSDocStatusEnum.先行動用_待覆核.getCode()
						.equals(docStatus)) {
					target = "先行動用退回";
				}
			}

			service.flowAction(oid, target);
		}

		return result;
	}// ; 

	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult flowActionBatch(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		// 儲存and檢核
		String[] oids = params.getStringArray("oids");
		
		StringBuilder errorMsg = new StringBuilder();
		
		for (String oid : oids) {
			C160M01A c160m01a = service.findModelByOid(C160M01A.class, oid);
			if (c160m01a != null) {
				if (!clsService.is_only_apply_prod69(c160m01a.getSrcMainId())) {
					errorMsg.append(c160m01a.getCustId() + UtilConstants.Mark.MARKDAN);
				}
				if(true){
					L120M01A l120m01a = clsService.findL120M01A_mainId(c160m01a.getSrcMainId());
					List<String> labor_abnormalMsg_list = clsService.check69C160M01Err(l120m01a.getMainId(),c160m01a.getMainId());
					if(labor_abnormalMsg_list.size()>0){
						errorMsg.append("客戶："+l120m01a.getCustId()+"-"+l120m01a.getDupNo()+" "+l120m01a.getCustName()
								+"("+"案號："+Util.toSemiCharString(l120m01a.getCaseNo())+")"
								+ "<br/>"
								+ "有勞工紓困特定檢核訊息，不可「整批覆核」。"
								+ "<br/>"
								+"請「單筆覆核」，查看檢核訊息。");
					}
					//J-110-0233_10702_B1009 Web e-Loan檢核申請書不承作案件
					String laborContractIsRejectMsg=clsService.findLaborContractIsReject(l120m01a);
					if(Util.isNotEmpty(laborContractIsRejectMsg)){
						errorMsg.append(laborContractIsRejectMsg);
					}
				}
			}
		}
		
		
		if (errorMsg.length() > 0) {
			errorMsg.deleteCharAt(errorMsg.length() - 1).append(
					this.getMessage("CLS1141.NoneProd69error"));
			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.注意, errorMsg.toString()),
					getClass());
		}
		
		for (String oid : oids) {
			C160M01A c160m01a = service.findModelByOid(C160M01A.class, oid);
			if (c160m01a != null) {
				String mainId = c160m01a.getMainId();
				
				String activity = Util.trim(params.getString("activity"));
				String docStatus = Util.trim(c160m01a.getDocStatus());
				String target = null;
				if ("send".equals(activity)) {
					if (CLSDocStatusEnum.編製中.getCode().equals(docStatus)) {
						// 儲存主管清單
						List<GenericBean> saveList = new ArrayList<GenericBean>();
						service.delete(service.findListByMainId(C160M01E.class,
								mainId));
						String form = Util.trim(params.getString("C160M01EForm"));
						JSONObject json = DataParse.toJSON(form);
						int numPerson = Util.parseInt(json.get("numPerson"));
						for (int i = 1; i <= numPerson; i++) {
							C160M01E c160m01e = new C160M01E();
							c160m01e.setMainId(mainId);
							c160m01e.setStaffJob(UtilConstants.STAFFJOB.授信主管L3);
							String staffNo = "boss" + String.valueOf(i);
							c160m01e.setStaffNo(Util.trim(json.get(staffNo)));
							saveList.add(c160m01e);
						}
						C160M01E c160m01e = new C160M01E();
						c160m01e.setMainId(mainId);
						c160m01e.setStaffJob(UtilConstants.STAFFJOB.單位授權主管L5);
						c160m01e.setStaffNo(Util.trim(json.get("manager")));
						saveList.add(c160m01e);
						service.save(saveList);
						// save main
						c160m01a.setManagerId(c160m01e.getStaffNo());
						service.save(c160m01a);
						// check
					} else if (CLSDocStatusEnum.先行動用_已覆核.getCode()
							.equals(docStatus)) {
						// String managerId =
						// Util.trim(params.getString("managerId"));
						// c160m01a.setManagerId(managerId);
					}
					// service.save(c160m01a);
				} else if ("accept".equals(activity)) {
					if (user.getUserId().equals(c160m01a.getApprId())) {
						// EFD0053=WARN|覆核人員不可與「經辦人員或其它覆核人員」為同一人|
						throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
					}
					String approveDate = Util.trim(params.getString("approveDate"));
					if (Util.isNotEmpty(approveDate)) {
						Date approveTime = Util.parseDate(approveDate);

						//J-111-0548 Web eloan 個金授信-動用審核表(一般)及動用審核表(團貸)之核准日期，請增加檢核機制「洗錢防制與打擊資助恐怖主義(AML/CFT)的資料查詢日期<=覆核日期<=覆核當日日期」
						//by 008831 112/01/06
						List<L120S09A> l120s09a_list = clsService.findL120S09A(mainId);
						Date blackListQDate = LMSUtil.maxQueryDateS(l120s09a_list);	
						
						if (blackListQDate != null && approveTime.compareTo(blackListQDate) < 0) {
							throw new CapMessageException("核准日需大於等於洗錢防制與打擊資助恐怖主義(AML/CFT)的資料查詢日期", getClass());
						}
						
						if (approveTime.compareTo(CapDate.getCurrentTimestamp()) > 0) {
							throw new CapMessageException("核准日需小於等於系統日", getClass());
						}
						
						if (CLSDocStatusEnum.先行動用_待覆核.getCode().equals(docStatus)) {
							logger.trace("先行動用_待覆核,not update ApproveTime:"
									+ c160m01a.getApproveTime());
						} else {
							c160m01a.setApproveTime(new Timestamp(approveTime
									.getTime()));
							if (c160m01a.getOrgApproveTime() == null) {
								c160m01a.setOrgApproveTime(c160m01a.getApproveTime());
							}
						}

						service.save(c160m01a);
					} else {
						throw new CapMessageException("請輸入核准日", getClass());
					}

					if (CLSDocStatusEnum.待覆核.getCode().equals(docStatus)) {
						if (UtilConstants.DEFAULT.是.equals(c160m01a.getUseType())) {
							target = "先行動用";
						} else {
							target = "核准";
						}
					} else if (CLSDocStatusEnum.先行動用_待覆核.getCode()
							.equals(docStatus)) {
						target = "先行動用核准";
						// 先行動用呈核及控制表檔
						C160M01D c160m01d = service.findModelByMainId(
								C160M01D.class, mainId);
						if (c160m01d != null) {
							c160m01d.setBfReCheckDate(new Date());
							service.save(c160m01d);
						}
					}
				} else if ("back".equals(activity)) {
					if (user.getUserId().equals(c160m01a.getApprId())) {
						// EFD0053=WARN|覆核人員不可與「經辦人員或其它覆核人員」為同一人|
						throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
					}
					if (CLSDocStatusEnum.待覆核.getCode().equals(docStatus)) {
						target = "退回";
					} else if (CLSDocStatusEnum.先行動用_待覆核.getCode()
							.equals(docStatus)) {
						target = "先行動用退回";
					}
				}
				
				service.flowAction(oid, target);
			}
		}

		return result;
	}// ;
	
	/**
	 * 於已覆核，登錄「先行動用呈核及控制表檔」的內容
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult srue(PageParameters params) throws CapException {
		JSONObject json = DataParse.toJSON(Util.trim(params
				.getString("C160M01DForm")));
		String oid = Util.trim(json.get(EloanConstants.OID));
		C160M01D c160m01d = service.findModelByOid(C160M01D.class, oid);
		if (c160m01d == null) {
			c160m01d = new C160M01D();
			c160m01d.setMainId(params.getString(EloanConstants.MAIN_ID, ""));
		}
		DataParse.toBean(json, c160m01d);
		if(true){
			Properties prop_view = MessageBundleScriptCreator.getComponentResource(CLS1161V03Page.class);
			
			List<String> errList = new ArrayList<String>();
			if(c160m01d.getFinishDate()==null){
				errList.add(prop_view.getProperty("C160M01D.finishDate"));
			}
			if(Util.isEmpty(Util.trim(c160m01d.getItemTrace()))){
				errList.add(prop_view.getProperty("C160M01D.itemTrace"));
			}
			if(Util.isEmpty(Util.trim(c160m01d.getManagerId()))){
				errList.add(prop_view.getProperty("C160M01D.managerId"));
			}
			if(errList.size()>0){
				throw new CapMessageException("未輸入："+StringUtils.join(errList, "、"), getClass());
			}
		}
		c160m01d.setAppraiserId(MegaSSOSecurityContext.getUserId());
		service.save(c160m01d);
		// flow
		return flowAction(params);
	}// ;

	/**
	 * EXCEL DATA 上傳 MIS
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Accept)
	public IResult xlsToMis(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		JSONObject formJson = DataParse.toJSON(params
				.getString("CaseType3Form"));
		// C160M01A．動用審核表主檔
		String oid = Util.trim(formJson.get(EloanConstants.OID));
		C160M01A c160m01a = service.findModelByOid(C160M01A.class, oid);
		if (c160m01a != null) {
			//因 testing 環境較慢，上傳時，放寬 timeout
			// IRequestCycleSettings iRequestCycleSettings =
			// parent.getApplication().getRequestCycleSettings();
			// Duration def_timeout = iRequestCycleSettings.getTimeout();
			// iRequestCycleSettings.setTimeout(Duration.minutes(29));
			
			int start = params.getInt("start", 0);
			int tot = c160m01a.getFinCount();
			List<C160S01D> list = service.getC160S01DList(mainId);
			for (C160S01D c160s01d : list) {
				service.uploadMIS(c160m01a, c160s01d);
				service.uploadDW(c160m01a, c160s01d);
			}
			int end = start + list.size();
			if (end >= tot || list.size() == 0) {
				end = tot;
				result.set("finish", true);

				service.uploadMISPTEAMAPP(c160m01a, null); // 動審表上傳MIS.PTEAMAPP(團貸年度總額度檔)
			}
			result.set("end", end);
			result.set("tot", tot);
			//-----------
			// iRequestCycleSettings.setTimeout(def_timeout);
		}
		// flow
		return result;
	}// ;

	/**
	 * 儲存再檢核
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult sendCheck(PageParameters params) throws CapException {
		// 先儲存再檢核
		SimpleContextHolder.put("reloadSaveLog", UtilConstants.DEFAULT.否);
		params.add("prompt", UtilConstants.DEFAULT.否);
		save(params);
		return sendBeforeCheck(params);
	}// ;

	/**
	 * 呈主管前檢核
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	@DomainAuth(AuthType.Modify)
	public IResult sendBeforeCheck(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		/* TODO	前端JS 未呼叫此程式
		 	在 server 端的 save(PageParameters params, Component parent) 		 指定 prompt=Y
		 	在 server 端的 sendCheck(PageParameters params, Component parent) 指定 prompt=N 
		 */
		String prompt = Util.trim(params.getString("prompt"));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		// 呈案前設定各項費用資料之SEQ
		service.setL140M01RSeq(mainId);

		Properties prop_cls1161s01Panel = MessageBundleScriptCreator.getComponentResource(CLS1161S01Panel.class);
		Properties prop_cls1161s02Panel = MessageBundleScriptCreator.getComponentResource(CLS1161S02Panel.class);
		C160M01A c160m01a = clsService.findC160M01A_mainId(mainId);
		
		//當caseType= 一般/團貸，會抓 c160m01b
		List<C160M01B> c160m01b_list = clsService.findC160M01B_mainId(mainId);
		//==============================================
		//用 prompt==Y 或  prompt!=Y 來判斷[儲存]或[呈主管] 
		//因為在[呈主管]之前，會先做[儲存] => 可以控制哪些 msg 要在[儲存]看到? 哪些 msg 要在[呈主管]看到?  
		
		
		// prompt is not 'Y' to check
		if (!prompt.equals(UtilConstants.DEFAULT.是)) { // prompt!=Y 代表 [呈主管] 的檢核
			// 動用審核表主檔
			
			if (Util.isEmpty(c160m01a.getCustId())) {
				StringBuilder errorMessage = new StringBuilder();
				errorMessage.append(EloanConstants.HTML_NEWLINE);
				errorMessage.append("統一編號 [必填欄位]");
				throw new CapMessageException(errorMessage.toString(),
						getClass());
			}
			if (Util.equals(c160m01a.getCaseType(),
					UtilConstants.Usedoc.caseType2.團貸)
					&& Util.isEmpty(c160m01a.getPackNo())) {
				StringBuilder errorMessage = new StringBuilder();
				errorMessage.append(EloanConstants.HTML_NEWLINE);
				errorMessage.append(getI18nMsg("errorMsg.008"));
				throw new CapMessageException(errorMessage.toString(),
						getClass());
			}

			// 20150902,EL07623,整批匯入案件檢查結果，有一筆不是成功則整批拒絕
			String caseType = Util.trim(c160m01a.getCaseType());
			if (UtilConstants.Usedoc.caseType2.整批匯入.equals(caseType)) {
				if(Util.isEmpty(Util.trim(c160m01a.getApprovedNo()))){
					throw new CapMessageException("尚未輸入「"
							+ Util.trim(prop_cls1161s01Panel.getProperty("C160M01A.caseNo1"))
							+ "」",
							getClass());
				}
				List<C160S01D> list = service.getC160S01DList(mainId, true);
				for (C160S01D s01d : list) {
					if (!ClsConstants.SUCCESS.equals(s01d.getResult())) {
						StringBuilder errorMessage = new StringBuilder();
						errorMessage.append(EloanConstants.HTML_NEWLINE);
						errorMessage.append(s01d.getCustId()
								+ s01d.getCustName() + " 案件結果不為成功，不可呈主管覆核!");
						throw new CapMessageException(errorMessage.toString(),
								getClass());
					}
				}
				valid_aml(c160m01a, true);
				return result;
			}

			// 一般,團貸檢核(審核意見必填)
			if (!BeanValidator.isValid(c160m01a, Check3.class)) {
				StringBuilder errorMessage = new StringBuilder();
				errorMessage.append(BeanValidator.getValidMsg(c160m01a,
						CLS1161S01Panel.class, Check3.class));
				throw new CapMessageException(errorMessage.toString(),
						getClass());
			}

			if( Util.equals(c160m01a.getCaseType(),UtilConstants.Usedoc.caseType2.一般)
					|| Util.equals(c160m01a.getCaseType(),UtilConstants.Usedoc.caseType2.團貸)){
				boolean need_check_c160m01c_24_rel = false;
				boolean need_check_c160m01c_45_buyInsurance = false;
				boolean need_check_c160m01c_46_buyInsurance = false;
				String c160m01c_45_itemContent = "";
				String c160m01c_46_itemContent = "";
				String c160m01c_56_AllocateFunsCheckListMsg = "";
				
				//C160M01C 檢附資訊檔明細檔(來源lms.c900s01b)
				List<C160M01C> c160m01c_list = (List<C160M01C>) service.findListByMainId(C160M01C.class, mainId);				
				for(C160M01C c160m01c: c160m01c_list){
					if( (Util.isEmpty(Util.trim(c160m01c.getItemCheck())) || (Util.equals(c160m01c.getItemCheck(), UtilConstants.Usedoc.checkItem.免收)))
							 && Util.equals(c160m01c.getItemType(), UtilConstants.C900S01B_ITEMTYPE.共用項目)
							 && Util.equals(c160m01c.getItemCode(),  UtilConstants.C900S01B_ITEMCODE.第24項)){
						need_check_c160m01c_24_rel = true;
					}
					
					if( (Util.isEmpty(Util.trim(c160m01c.getItemCheck())) || (Util.equals(c160m01c.getItemCheck(), UtilConstants.Usedoc.checkItem.免收)))
							 && (Util.equals(c160m01c.getItemType(), UtilConstants.C900S01B_ITEMTYPE.共用項目) || Util.equals(c160m01c.getItemType(), UtilConstants.C900S01B_ITEMTYPE.依產品自訂))
							 && Util.equals(c160m01c.getItemCode(),  UtilConstants.C900S01B_ITEMCODE.第45項)){
						need_check_c160m01c_45_buyInsurance = true;
						c160m01c_45_itemContent = Util.trim(c160m01c.getItemContent());
					}
					if( (Util.isEmpty(Util.trim(c160m01c.getItemCheck())) || (Util.equals(c160m01c.getItemCheck(), UtilConstants.Usedoc.checkItem.免收)))
							 && (Util.equals(c160m01c.getItemType(), UtilConstants.C900S01B_ITEMTYPE.共用項目) || Util.equals(c160m01c.getItemType(), UtilConstants.C900S01B_ITEMTYPE.依產品自訂))
							 && Util.equals(c160m01c.getItemCode(),  UtilConstants.C900S01B_ITEMCODE.第46項)){
						need_check_c160m01c_46_buyInsurance = true;
						c160m01c_46_itemContent = Util.trim(c160m01c.getItemContent());
					}
					
					if(UtilConstants.C900S01B_ITEMCODE.第56項.equals(c160m01c.getItemCode())){
						if(!UtilConstants.Usedoc.checkItem.已收.equals(c160m01c.getItemCheck())){
							c160m01c_56_AllocateFunsCheckListMsg += this.clsService.getCheckAllocateFundsCheckListItemMessage() + "<br/><br/>";
						}
					}
				}
				
				c160m01c_56_AllocateFunsCheckListMsg += this.clsService.checkIsEjcicB29AndB33InquiryLogForCls(c160m01a.getMainId());
				
				if(need_check_c160m01c_24_rel){
					Map<String, String> prodIdDup_cntrNoMap = new HashMap<String, String>();
					//主借人及共同借款人-C 要檢核「辦理關係人授信交易檢核表」
					for(C160M01B c160m01b:c160m01b_list){
						String idDup = LMSUtil.getCustKey_len10custId(c160m01b.getCustId(), c160m01b.getDupNo());
						//主借人
						prodIdDup_cntrNoMap.put(idDup, c160m01b.getCntrNo());
						
						//共同借款人-C
						List<C160S01B> c160s01b_list = (List<C160S01B>) service.findModelByMainIdAndRefMainId(
								C160S01B.class, c160m01b.getMainId(), c160m01b.getRefmainId(), null);
						for(C160S01B c160s01b: c160s01b_list){
							if(Util.equals(c160s01b.getRType(), UtilConstants.lngeFlag.共同借款人)){
								prodIdDup_cntrNoMap.put(LMSUtil.getCustKey_len10custId(
										c160s01b.getRId(), c160s01b.getRDupNo()), c160m01b.getCntrNo());
							}							
						}
					}
					
					for(String idDup : prodIdDup_cntrNoMap.keySet()){
						String custId = Util.trim(StringUtils.substring(idDup, 0, 10));
						String dupNo = Util.trim(StringUtils.substring(idDup, 10));
						
						// UPGRADETODO: 使用當前params的副本，避免CapMvcParameters的request為null問題
						PageParameters q1_params = new CapMvcParameters();
						q1_params.setRequestObject(params.getServletRequest()); // 複製request物件
						q1_params.put(EloanConstants.MAIN_ID, mainId);
						q1_params.put("custId", custId);  
						q1_params.put("dupNo", dupNo);
						q1_params.put("type", ClsConstants.C101S01E.本行利害關係人);
						q1_params.put(ClsConstants.C101S01E.聯徵查詢日期, "");
						
						PageParameters q2_params = new CapMvcParameters();
						q2_params.setRequestObject(params.getServletRequest()); // 複製request物件
						q2_params.put(EloanConstants.MAIN_ID, mainId);
						q2_params.put("custId", custId);
						q2_params.put("dupNo", dupNo);
						q2_params.put("type", ClsConstants.C101S01E.金控利害關係人44條);
						q2_params.put(ClsConstants.C101S01E.聯徵查詢日期, "");
						
						C101S01J c101s01j = new C101S01J();
						List<GenericBean> dataList = new ArrayList<GenericBean>();
						JSONObject r1 = cls1131Service.queryData(dataList, null, c101s01j, q1_params);
						JSONObject r2 = cls1131Service.queryData(dataList, null, c101s01j, q2_params);
						if(Util.equals(c101s01j.getQresult(), "Y")
								|| Util.equals(c101s01j.getXQResult(), "Y")){
							throw new CapMessageException( custId+"-"+dupNo
									+" 為利害關係人，應檢核/附銀行法及金控法利害關係人查詢紀錄，該項檢附文件不可為「免附」。",
									getClass());
						}
					}
				}
			
				if(need_check_c160m01c_45_buyInsurance && clsService.is_function_on_codetype("J-108-0351_chk_c160_itemCode_45")){
					//團貸子戶，如其中有部分是買「類保險商品」
					check_J_108_0351(mainId, c160m01c_45_itemContent);
				}
				if(need_check_c160m01c_46_buyInsurance && clsService.is_function_on_codetype("J-108-0351_chk_c160_itemCode_46")){
					//團貸子戶，如其中有部分是買「類保險商品」
					check_J_108_0351(mainId, c160m01c_46_itemContent);
				}
			
				if(clsService.is_function_on_codetype("cls1161_c160m01b_dataCheck")){				
					for (C160M01B c160m01b : c160m01b_list) {
						if (!Util.equals("Y", c160m01b.getDataCheck())) {
							throw new CapMessageException(prop_cls1161s02Panel.getProperty("C160M01B.cntrNo") +" "
									+ c160m01b.getCntrNo()+" "
									+"編輯未完成", getClass());
						}
					}
				}
				
				if(clsService.is_function_on_codetype("cls1161_c160m01b_cnt")){
					//因為在「額度明細表」 頁籤, 有[重新引進 : importC160M01B] [刪除 : deleteDetial] 的功能 ===> 若額度全刪掉，需要檢核
					if(c160m01b_list.size() == 0) {
						//errorMsg.009=本案沒有任何額度明細表。
						throw new CapMessageException(getI18nMsg("errorMsg.009"), getClass());
					}
				}
				
				if(!"".equals(c160m01c_56_AllocateFunsCheckListMsg)){
					throw new CapMessageException(c160m01c_56_AllocateFunsCheckListMsg, getClass());
				}
				
			} //end-if (一般or團貸)
			
			// 如果檢附文件為X時則要把所有檢附文件的X組在先行動用呈核及控制表中且[預定補全日期]為必填欄位
			int size = service.getC160M01CSize(mainId,
					UtilConstants.Usedoc.checkItem.未收);
			String useType = UtilConstants.DEFAULT.否;
			if (size > 0) {
				useType = UtilConstants.DEFAULT.是;
				C160M01D c160m01d = service.findModelByMainId(C160M01D.class,
						mainId);
				if (!BeanValidator.isValid(c160m01d, Check2.class)) {
					StringBuilder errorMessage = new StringBuilder();
					errorMessage.append(BeanValidator.getValidMsg(c160m01d,
							CLS1161S04Panel.class, Check2.class));
					throw new CapMessageException(errorMessage.toString(),
							getClass());
				}
			} else {
				// 當無未收項目清空預訂補全日
				c160m01a.getC160m01d().setWillFinishDate(null);
				c160m01a.getC160m01d().setWaitingItem("");
			}
			c160m01a.setUseType(useType);//重填入「先行動用」的值
			
			
			// 先使用參數設定來決定是否要執行模擬動審，免得有其它問題
			String checkSimuTrial = sysParameterService
					.getParamValue("SIMU_TRIAL");
			String checkSimuTrialDate = sysParameterService
					.getParamValue("SIMU_TRIAL_DATE");
			String type = c160m01a.getCaseType();

			if ("Y".equals(checkSimuTrial)
					&& (UtilConstants.Usedoc.caseType2.一般.equals(type) || UtilConstants.Usedoc.caseType2.團貸
							.equals(type))) {
				
				
				
				boolean caseType1DoCheck = false;
				boolean caseType2DoCheck = false;
				// 簽報書mainId;
				String theMainId = null;
				
				if (UtilConstants.Usedoc.caseType2.一般.equals(type)) {
					L120M01A l120m01a = clsService.findL120M01A_mainId(c160m01a
							.getSrcMainId());
					theMainId = l120m01a.getMainId();
					Date endDate = l120m01a.getEndDate();

					if (endDate == null
							|| endDate.compareTo(CapDate.parseDate(Util
									.trim(checkSimuTrialDate))) >= 0) {						
						caseType1DoCheck = true;
					}
				} else if (UtilConstants.Usedoc.caseType2.團貸.equals(type)) {
					caseType2DoCheck = true;
				}
				
				if (caseType1DoCheck || caseType2DoCheck) {
					if (c160m01b_list != null && !c160m01b_list.isEmpty()) {
						for (C160M01B c160m01b : c160m01b_list) {
							String refMainId = c160m01b.getRefmainId();
							L140M01A l140m01a = service.findModelByMainId(
									L140M01A.class, refMainId);

							String proPerty = l140m01a.getProPerty();
							if (UtilConstants.Cntrdoc.Property.不變
									.equals(proPerty)
									|| UtilConstants.Cntrdoc.Property.取消
											.equals(proPerty)) {
								continue;
							}

							if (caseType2DoCheck) {
								// 團貸再加撈其簽報書要大於9/1
								L120M01C l120m01c = l140m01a.getL120m01c();
								L120M01A l120m01a = clsService
										.findL120M01A_mainId(l120m01c
												.getMainId());
								Date endDate = l120m01a.getEndDate();
								theMainId = l120m01a.getMainId();
								if (endDate == null
										|| endDate
												.compareTo(CapDate.parseDate(Util
														.trim(checkSimuTrialDate))) >= 0) {
								} else {
									continue;
								}

							}

							boolean is57 = false, is59 = false, is66 = false, is31 = false, is02or03or68 = false;

							// 「消金戶-簽約前模擬動審檢核表」（僅限歡喜房貸、青年安心成家貸款及行家理財貸款之案件）
							List<L140S02A> l140s02as = clsService
									.findL140S02A(l140m01a);

							for (L140S02A l140s02a : l140s02as) {
								String prodKind = l140s02a.getProdKind();
								if (ProdService.ProdKindEnum.青年安心成家貸款_57
										.getCode().equals(prodKind)) {
									is57 = true;
								} else if (ProdService.ProdKindEnum.青年安心成家貸款_第二案_59
										.getCode().equals(prodKind)) {
									is59 = true;
								} else if (ProdService.ProdKindEnum.金門青年安心成家_66
										.getCode().equals(prodKind)) {
									is66 = true;
								} else if (ProdService.ProdKindEnum.房貸專案_B案_100億_31
										.getCode().equals(prodKind)) {
									is31 = true;
								} else if (ProdService.ProdKindEnum.行家理財貸款_短期_02
										.getCode().equals(prodKind)
										|| ProdService.ProdKindEnum.行家理財貸款_中長期_03
												.getCode().equals(prodKind)
										|| ProdService.ProdKindEnum.行家理財中期循環_68
												.getCode().equals(prodKind)) {
									// 判斷是否為行家理財
									is02or03or68 = true;
								}
							}

							if (is57 || is59 || is66 || is31 || is02or03or68) {
								List<L250M01A> l250m01as = lmsService
										.getSimuTrialDataBeforePay(theMainId,
												l140m01a.getCntrNo());
								if (l250m01as == null || l250m01as.isEmpty()) {
									throw new CapMessageException("額度序號"
											+ l140m01a.getCntrNo()
											+ "，動審表尚未執行模擬動審", this.getClass());
								}
							}

						}
					}
				}
			} //end-if("Y".equals(checkSimuTrial)
	
			if(true){ // 以房養老-累積型 需檢核，傳統型的額度、累積型的額度應同時動用
				//先判斷動審表裡的產品 C160S01C.prodKind　是否有67或70，若有，再做進一步判斷
				Map<String, List<C160M01B>> idDup_C160M01B_map = CrsUtil.group_by_idDup_C160M01B(c160m01b_list);
				for(String idDup : idDup_C160M01B_map.keySet()){
					//依 idDup 將 List<C160M01B> 分組
					Set<String> idDup_C160M01B_tabMainId_set = new HashSet<String>();
					for (C160M01B c160m01b : idDup_C160M01B_map.get(idDup)) {
						String tabMainId = c160m01b.getRefmainId();
						idDup_C160M01B_tabMainId_set.add(tabMainId);
					}
					//~~~~~~
					for (C160M01B c160m01b : idDup_C160M01B_map.get(idDup)) {
						String tabMainId = c160m01b.getRefmainId();						
						for(C160S01C c160s01c : clsService.findC160S01C_mainId_refMainid(mainId, tabMainId)){
							String prodKind = c160s01c.getProdKind();
							if(Util.equals(UtilConstants.Cntrdoc.Property.新做,  c160s01c.getProperty())
									&& (CrsUtil.is_67(prodKind) || CrsUtil.is_70(prodKind))){
								check_ReverseMortgage_whenProperty1(c160m01b, idDup_C160M01B_tabMainId_set);
							}
						}
					}
					if(true){
						int prodKind70_cnt = 0;
						String prodKind70_cntrNo = "";
						String prodKind70_atPayNo = "";						
						Date prodKind70_rctDate = null;
						for (C160M01B c160m01b : idDup_C160M01B_map.get(idDup)) {
							String tabMainId = c160m01b.getRefmainId();						
							for(C160S01C c160s01c : clsService.findC160S01C_mainId_refMainid(mainId, tabMainId)){
								String prodKind = c160s01c.getProdKind();
								if(Util.equals(UtilConstants.Cntrdoc.Property.新做,  c160s01c.getProperty()) && CrsUtil.is_70(prodKind)){
									++prodKind70_cnt;
									prodKind70_cntrNo = Util.trim(c160m01b.getCntrNo());
									prodKind70_atPayNo = Util.trim(c160s01c.getAtpayNo());
									prodKind70_rctDate = c160s01c.getRctDate();
								}
							}
						}
						if(prodKind70_cnt>0){
							int prodKind67_cnt = 0;
							String prodKind67_atPayNo = "";				
							Date prodKind67_rctDate = null;
							for (C160M01B c160m01b : idDup_C160M01B_map.get(idDup)) {
								String tabMainId = c160m01b.getRefmainId();						
								for(C160S01C c160s01c : clsService.findC160S01C_mainId_refMainid(mainId, tabMainId)){
									String prodKind = c160s01c.getProdKind();
									if(Util.equals(UtilConstants.Cntrdoc.Property.新做,  c160s01c.getProperty()) && CrsUtil.is_67(prodKind)){
										++prodKind67_cnt;
										prodKind67_atPayNo = Util.trim(c160s01c.getAtpayNo());
										prodKind67_rctDate = c160s01c.getRctDate();
									}
								}
							}
							
							if(prodKind67_cnt==0){
								throw new CapMessageException(CrsUtil.PROD_70_DESC+"額度("+prodKind70_cntrNo+")無對應的傳統型額度", getClass());
							}
							//~~~~~~~~~~~~~~~~~~~~~~~~
							//C160S01C.atpayNo=扣款帳號
							if(prodKind67_cnt>0 && Util.isEmpty(prodKind67_atPayNo)){
								throw new CapMessageException(CrsUtil.PROD_70_DESC+"額度("+prodKind70_cntrNo+")對應的傳統型額度未輸入「"+prop_CLS1161S02APage.getProperty("C160S01C.atpayNo")+"」", getClass());
							}
							//C160S01C.rctDate=進帳日期
							if(prodKind67_cnt>0){
								if( prodKind67_rctDate==null){
									throw new CapMessageException(CrsUtil.PROD_70_DESC+"額度("+prodKind70_cntrNo+")對應的傳統型額度未輸入「"+prop_CLS1161S02APage.getProperty("C160S01C.rctDate")+"」", getClass());
								}else{
									if(prodKind70_rctDate==null){
										throw new CapMessageException(CrsUtil.PROD_70_DESC+"額度("+prodKind70_cntrNo+")未輸入「"+prop_CLS1161S02APage.getProperty("C160S01C.rctDate")+"」", getClass());	
									}else{
										if(!LMSUtil.cmpDate(prodKind67_rctDate, "==", prodKind70_rctDate)){
											throw new CapMessageException(build_cmp_prodKind70_67_msg(prodKind70_cntrNo, prop_CLS1161S02APage.getProperty("C160S01C.rctDate")
													, Util.trim(TWNDate.toAD(prodKind70_rctDate)), Util.trim(TWNDate.toAD(prodKind67_rctDate)) ), getClass());
										}	
									}											
								}
							}
							
							//~~~~~~~~~~~~~~~~~~~~~~~~
							//產品 67, 70 的{扣款帳號}需相同
							if(!Util.equals(prodKind70_atPayNo, prodKind67_atPayNo)){
								throw new CapMessageException(build_cmp_prodKind70_67_msg(prodKind70_cntrNo, prop_CLS1161S02APage.getProperty("C160S01C.atpayNo")
										, prodKind70_atPayNo, prodKind67_atPayNo ), getClass());
							}	
						} 
					}
				}
			}
			
		} // end-if(prompt!=Y)

		// 額度明細檔
		StringBuilder message = new StringBuilder();		
		if (true) {
			for (C160M01B c160m01b : c160m01b_list) {
				String msg = checkC160M01B(c160m01b);
				message.append(msg);
			}
		}
		
		//檢核房貸團貸母戶, 總額度有效迄日是否已逾期
		if (Util.equals(c160m01a.getCaseType(), UtilConstants.Usedoc.caseType2.團貸)){
			
			PTEAMAPP pteaMapp = this.misPTEAMAPPService.getGroupLoanBuildCaseMasterAccount(c160m01a.getLoanMasterNo());
			
			if(pteaMapp != null && pteaMapp.getEffend().compareTo(DateUtils.truncate(new Date(), java.util.Calendar.DAY_OF_MONTH)) == -1){
				
				for (C160M01B c160m01b : c160m01b_list) {
					String refMainId = c160m01b.getRefmainId();
					L140M01A l140m01a = service.findModelByMainId(L140M01A.class, refMainId);

					if (UtilConstants.Cntrdoc.Property.新做.equals(l140m01a.getProPerty())) {
						//申請團體貸款總額度或額度迄日已經過期，請申請總額度後再承作此案件！
						throw new CapMessageException(prop_cls1161s01Panel.getProperty("C160M01A.msg.error.0001"), getClass());
					}
				}
			}
		}
		
		// J-110-0527 動審新增聯徵查詢檢核邏輯
		// 檢核簽報書聯徵查詢日期需在動撥日前3個月內，若超出3個月請跳提醒訊息：「本案動撥日距查詢聯徵日期已超過3個月，請再次查詢聯徵」(不阻擋動撥，僅跳提示訊息)。
		List<C120S02A> c120s02as = clsService.findC120S02A(c160m01a.getSrcMainId(), c160m01a.getCustId(), c160m01a.getDupNo());
		List<C160S02A> c160s02as = clsService.findC160S02A(mainId, c160m01a.getCustId(), c160m01a.getDupNo());
		Date c120DataDate = null;
		Date c160DataDate = null;
		if (c120s02as != null && !c120s02as.isEmpty()) {
			c120DataDate = c120s02as.get(0).getDataDate();
		}
		if (c160s02as != null && !c160s02as.isEmpty()) {
			c160DataDate = c160s02as.get(0).getDataDate();
		}
		// 未重新查詢後引入時，用現在日期
		if (c160DataDate == null) {
			c160DataDate = new Date();
		}
		// 簽報書有存檔聯徵資料時才檢核，避免舊案卡住
		if (c120DataDate != null) {
			if (CapDate.addMonth(c120DataDate, 3).before(c160DataDate)) {
				// cls1161.msg1=本案動撥日距簽報書查詢聯徵日期已超過3個月，請再次查詢聯徵
				message.append(prop_CLS1161M01Page.getProperty("cls1161.msg1")).append("<br/><br/>");
			}
		}
		
		//在 server 端的 save(PageParameters params, Component parent) 	      指定 prompt=Y，而且用 try~catch 去包住 CapMessageException => 藉此把 [丟出msg] 弄成 promptMsg
	 	//在 server 端的 sendCheck(PageParameters params, Component parent) 指定 prompt=N 
		// prompt is 'Y' throw message
		if (prompt.equals(UtilConstants.DEFAULT.是) && message.length() > 0) {
			throw new CapMessageException(message.toString(), getClass());
		}
		
		valid_aml(c160m01a, false);
		
		//J-110-0233_10702_B1002 Web e-Loan檢核勞工紓困未提權網銀帳號不得線上對保
		List<String> Msg69=clsService.check69C160M01Err(c160m01a.getSrcMainId(),c160m01a.getMainId());
		if(Msg69.size()>0){
			message.append(StringUtils.join(Msg69, "<br/><br/>"));
		}

		//J-110-0108_10702_B1002 Web e-Loan線上對保新增查詢利害關係人以及受監護/輔助宣告功能
		List<String> Msg71= clsService.check71C160M01Err(c160m01a.getSrcMainId(),c160m01a.getMainId(),c160m01b_list);
		if(Msg71.size()>0){
			message.append(StringUtils.join(Msg71, "<br/><br/>"));
		}

		//J-113-0127 對保、動審檢視本行帳號是否為存摺印鑑遺失或公司帳戶跳提醒
		Map<String, List<C160M01B>> idDup_C160M01B_map = CrsUtil.group_by_idDup_C160M01B(c160m01b_list);
		List<Map<String, Object>> accounts = cls3401Service.find_ploanAcct(c160m01a.getCustId());
		for(String idDup : idDup_C160M01B_map.keySet()){
			for (C160M01B c160m01b : idDup_C160M01B_map.get(idDup)) {
				String tabMainId = c160m01b.getRefmainId();
				for(C160S01C c160s01c : clsService.findC160S01C_mainId_refMainid(mainId, tabMainId)){
					String prodKind = c160s01c.getProdKind();
					//只有71才檢查
					if (accounts != null && accounts.size() > 0 && CrsUtil.is_71(prodKind)) {
						for (Map<String, Object> account : accounts) {
							String accoutNo = MapUtils.getString(account, "MR_PB_ACT_NO", "");
							String taxNo = MapUtils.getString(account, "MR_PB_TAX_NO", "");
							String dpCate = ClsUtility.get_acctNo_APCODE(accoutNo); //存款帳號第4~5碼是存款種類
							String acctStatus = MapUtils.getString(account, "MR_PB_ACT_STATUS", "");
							if (accoutNo.equals(c160s01c.getAccNo())) {
								if(Util.equals("05", acctStatus)){
									message.append(MessageFormat.format("<br><br>"+
											prop_CLS1161M01Page.getProperty("checkBorrowerAcctNo.05"),
											new StringBuffer().append(accoutNo),
											new StringBuffer().append(acctStatus)));
								}
								if(Util.equals("06", acctStatus)){
									message.append(MessageFormat.format("<br><br>"+
											prop_CLS1161M01Page.getProperty("checkBorrowerAcctNo.06"),
											new StringBuffer().append(accoutNo),
											new StringBuffer().append(acctStatus)));
								}
								if(Util.equals("09", dpCate)){
									message.append(MessageFormat.format("<br><br>"+
											prop_CLS1161M01Page.getProperty("checkBorrowerAcctNo.09"),
											new StringBuffer().append(accoutNo)));
								}
								//J-113-0206_10702_B1001 web eLoan對保契約、動審檢核產品種類71之管制戶、警示戶跳提醒以及排除信貸契約書公司戶帳號
								if (accoutNo.length()>3) {
									String brNo = accoutNo.substring(0,3);
									String acct = accoutNo.substring(3,accoutNo.length());
									Map<String, Object> warnMap = cls3401Service.findODS_CMFAUDAC_ByAcc(brNo, acct);
									if (warnMap!=null) {
										message.append(MessageFormat.format("<br><br>"+
												prop_CLS1161M01Page.getProperty("checkBorrowerAcctNo.warn"),
												new StringBuffer().append(accoutNo)));
									}
								}
							}
						}
					}
				}
			}
		}
		result.set("message", message.toString());
		return result;
	}// ;

	
	private String build_cmp_prodKind70_67_msg(String prodKind70_cntrNo, String column, String val70, String val67){
		return CrsUtil.PROD_70_DESC+"額度("+prodKind70_cntrNo+")"+"之"+column+"("+val70+")"
			+"應和傳統型額度"+column+"("+val67+")相同";
	}
	
	private void check_ReverseMortgage_whenProperty1(C160M01B c160m01b, Set<String> idDup_C160M01B_tabMainId_set)
	throws CapMessageException{
		String tabMainId = c160m01b.getRefmainId();
		L120M01C l120m01c = clsService.findL120M01C_refMainId(tabMainId);
		if(l120m01c!=null){
			Map<String, String> shouldContain_ReverseMortgage_mainId_cntrNo = new HashMap<String, String>();
			if(true){
				String target_idDup = LMSUtil.getCustKey_len10custId(c160m01b.getCustId(), c160m01b.getDupNo());
				List<L140M01A> l140m01a_list = CrsUtil.l140m01a_excludeDocStatus060(CrsUtil.l140m01a_in_idDup(clsService.findL140M01A_l120m01aMainId(l120m01c.getMainId()), target_idDup));
				
				for(L140M01A l140m01a: l140m01a_list){
					for (L140S02A l140s02a : clsService.findL140S02A(l140m01a)) {
						String proPerty = Util.trim(l140s02a.getProperty());
						String prodKind = l140s02a.getProdKind();				
						//~~~~~~~~
						if (UtilConstants.Cntrdoc.Property.新做.equals(proPerty) 
								&& (CrsUtil.is_67(prodKind) || CrsUtil.is_70(prodKind))) {
							shouldContain_ReverseMortgage_mainId_cntrNo.put(l140m01a.getMainId(), l140m01a.getCntrNo());
						}
					}
				}	
			}
			if(LMSUtil.elm_onlyLeft(shouldContain_ReverseMortgage_mainId_cntrNo.keySet(), idDup_C160M01B_tabMainId_set).size()>0){
				throw new CapMessageException("同一份簽報書下的「新做」"+CrsUtil.PROD_67_DESC+"額度序號"
						+shouldContain_ReverseMortgage_mainId_cntrNo.values()+"應同時動審", getClass());
			}
		}
		
	}
	
	private boolean has_L140S02A_lnPurs4(L140M01A l140m01a){
		for(L140S02A l140s02a : clsService.findL140S02A(l140m01a)){
			String property = l140s02a.getProperty();
			if (UtilConstants.Cntrdoc.Property.不變.equals(property)
					|| UtilConstants.Cntrdoc.Property.取消.equals(property)) {
				continue;
			}
			if(Util.equals(l140s02a.getLnPurs(), "4")){
				return true;
			}
		}
		return false;
	}
	private void check_J_108_0351(String c160m01a_mainId, String itemContent)
	throws CapMessageException{
		List<C160M01B> c160m01b_list = clsService.findC160M01B_mainId(c160m01a_mainId);
		Map<String, TreeSet<String>> caseMainId_cntrNoSet_map = new LinkedHashMap<String, TreeSet<String>>();
		for(C160M01B c160m01b:c160m01b_list){
			String cntrNo = c160m01b.getCntrNo();
			String tabMainId = c160m01b.getRefmainId();
			L140M01A l140m01a = clsService.findL140M01A_mainId(tabMainId);
			if(l140m01a!=null){
				if(!has_L140S02A_lnPurs4(l140m01a)){
					continue;
				}
				//~~~~~~~~~~~~~~~~~
				L120M01C l120m01c = l140m01a.getL120m01c();
				if(l120m01c!=null){
					String caseMainId = l120m01c.getMainId();
					if(!caseMainId_cntrNoSet_map.containsKey(caseMainId)){
						caseMainId_cntrNoSet_map.put(caseMainId, new TreeSet<String>());
					}
					caseMainId_cntrNoSet_map.get(caseMainId).add(cntrNo);
				}
			}
		}
		for(String caseMainId : caseMainId_cntrNoSet_map.keySet()){
			String cntrNodesc = StringUtils.join(caseMainId_cntrNoSet_map.get(caseMainId), "、");
			List<L140M04A> l140m04a_list = clsService.findL140M04A(caseMainId);
			boolean buyInsurance = false;
			String l140m04a_checkContent = "";
			for(L140M04A l140m04a: l140m04a_list){
				if(Util.equals(UtilConstants.C900S01A_CHECKCODE.第187項, l140m04a.getCheckCode()) && Util.equals("Y", l140m04a.getCheckYN())){
					buyInsurance = true;
					l140m04a_checkContent = Util.trim(l140m04a.getCheckContent());
					break;
				}	
			}			
			if(buyInsurance){
				L120M01A l120m01a = clsService.findL120M01A_mainId(caseMainId);
				throw new CapMessageException("額度序號"+cntrNodesc+" 所屬簽報書 "+Util.toSemiCharString(l120m01a.getCaseNo())
						+" 之查核事項【"+l140m04a_checkContent+"】為「是」"
						+"<br/>"
						+"動審表之檢附文件【"+ itemContent+"】不可為「免附」。", getClass()); 
			}
		}
	}
	
	private void valid_aml(C160M01A c160m01a, boolean mustHaveRecord) 
	throws CapMessageException{
		String mainId = c160m01a.getMainId();
		List<L120S09A> l120s09a_list = clsService.findL120S09A(mainId);
		
		if(mustHaveRecord && l120s09a_list.size()==0){
			Properties prop = MessageBundleScriptCreator.getComponentResource(LMSCommomPage.class);
			// clsL120M01A.error070=AML頁籤有未完成黑名單掃描之名單
			throw new CapMessageException(prop.getProperty("clsL120M01A.error070"), getClass());
		}
		
		if(LMSUtil.lackBlackCode(l120s09a_list)){
			Properties prop = MessageBundleScriptCreator.getComponentResource(LMSCommomPage.class);
			// clsL120M01A.error070=AML頁籤有未完成黑名單掃描之名單
			throw new CapMessageException(prop.getProperty("clsL120M01A.error070"), getClass());				
		}		
	
		if(!clsService.is_aml_case_finish(c160m01a)){
			Properties propCls= MessageBundleScriptCreator.getComponentResource(CLS1201S20Panel.class);
			L120S09B l120s09b = clsService.findL120S09B_mainId_latestOne(mainId);
			String ncResult = "";
			if (l120s09b != null) {
				ncResult = Util.trim(l120s09b.getNcResult());
				if(Util.isNotEmpty(ncResult)){
					Map<String, String> map_ncResult = clsService.get_codeTypeWithOrder("SAS_NC_Result");
					if(map_ncResult.containsKey(ncResult)){
						ncResult = (ncResult+"-"+map_ncResult.get(ncResult));	
					}													
				}
			}
			throw new CapMessageException(MessageFormat.format(propCls.getProperty("clsAmlMsg01"),ncResult),getClass());
		}
	}
	
	/**
	 * 此 method checkSend_C160M01A 會檢核[一般、團貸、中鋼整批]等3類的動審表
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult checkSend_C160M01A(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));		
		C160M01A c160m01a = clsService.findC160M01A_mainId(mainId);
		
		//======================
		// 以下為 error 訊息
		String errMsg = clsService.checkSend_C160M01A(c160m01a);
		if(Util.isNotEmpty(errMsg)){
			throw new CapMessageException(errMsg, getClass());	
		}
		
		//======================
		// 以下為 confirm 訊息
		List<String> cfmList = new ArrayList<String>();
		cfmList.addAll(clsService.cfmMsg_both_EL01_EL02(c160m01a));
		// J-113-0082 配合法務部新規，於AML頁籤新增引入「受告誡處分」資訊
		// 提示有受告誡處分
		String checkCmfwarnpResult = 
				amlRelateService.checkCmfwarnpResult(mainId);
		if(Util.isNotEmpty(checkCmfwarnpResult)){
			cfmList.add(checkCmfwarnpResult);
		}
		
		if(cfmList.size()>0){
			result.set("cfmMsg", StringUtils.join(cfmList, "<br/><br/>"));
		}
		return result;
	}
	
	@DomainAuth(AuthType.Accept)
	public IResult checkApprove_C160M01A(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));		
		C160M01A c160m01a = clsService.findC160M01A_mainId(mainId);
		
		//======================
		// 以下為 error 訊息
		String errMsg = "";
		
		//J-110-0233_10702_B1009 Web e-Loan檢核申請書不承作案件
		L120M01A l120m01a = clsService.findL120M01A_mainId(c160m01a.getSrcMainId());
		String laborContractIsRejectMsg=clsService.findLaborContractIsReject(l120m01a);
		if(Util.isNotEmpty(laborContractIsRejectMsg)){
			errMsg += laborContractIsRejectMsg;
		}
		
		if(Util.isNotEmpty(errMsg)){
			throw new CapMessageException(errMsg, getClass());	
		}
		
		//======================
		// 以下為 confirm 訊息
		List<String> cfmList = new ArrayList<String>();
		cfmList.addAll(clsService.cfmMsg_both_EL01_EL02(c160m01a));
		
		if(OverSeaUtil.isBranchEditing(c160m01a.getDocStatus())) {
			
		}else{
			cfmList.addAll(clsService.check69C160M01Err(c160m01a.getSrcMainId(),c160m01a.getMainId()));
			//J-110-0108_10702_B1002 Web e-Loan線上對保新增查詢利害關係人以及受監護/輔助宣告功能
			List<C160M01B> c160m01b_list = clsService.findC160M01B_mainId(mainId);
			cfmList.addAll(clsService.check71C160M01Err(c160m01a.getSrcMainId(),c160m01a.getMainId(),c160m01b_list));
			// J-113-0082 配合法務部新規，於AML頁籤新增引入「受告誡處分」資訊
			// 提示有受告誡處分
			String checkCmfwarnpResult = 
				amlRelateService.checkCmfwarnpResult(mainId);
			if(Util.isNotEmpty(checkCmfwarnpResult)){
				cfmList.add(checkCmfwarnpResult);
			}
		}
		//J-113-0127 對保、動審檢視本行帳號是否為存摺印鑑遺失或公司帳戶跳提醒
		Map<String, List<C160M01B>> idDup_C160M01B_map = CrsUtil.group_by_idDup_C160M01B(clsService.findC160M01B_mainId(mainId));
		List<Map<String, Object>> accounts = cls3401Service.find_ploanAcct(c160m01a.getCustId());
		for(String idDup : idDup_C160M01B_map.keySet()){
			for (C160M01B c160m01b : idDup_C160M01B_map.get(idDup)) {
				String tabMainId = c160m01b.getRefmainId();
				for(C160S01C c160s01c : clsService.findC160S01C_mainId_refMainid(mainId, tabMainId)){
					String prodKind = c160s01c.getProdKind();
					//只有71才檢查
					if (accounts != null && accounts.size() > 0 && CrsUtil.is_71(prodKind)) {
						for (Map<String, Object> account : accounts) {
							String accoutNo = MapUtils.getString(account, "MR_PB_ACT_NO", "");
							String taxNo = MapUtils.getString(account, "MR_PB_TAX_NO", "");
							String dpCate = ClsUtility.get_acctNo_APCODE(accoutNo); //存款帳號第4~5碼是存款種類
							String acctStatus = MapUtils.getString(account, "MR_PB_ACT_STATUS", "");
							if (accoutNo.equals(c160s01c.getAccNo())) {
								if(Util.equals("05", acctStatus)){
									cfmList.add(MessageFormat.format(
											prop_CLS1161M01Page.getProperty("checkBorrowerAcctNo.05"),
											new StringBuffer().append(accoutNo),
											new StringBuffer().append(acctStatus)));
								}
								if(Util.equals("06", acctStatus)){
									cfmList.add(MessageFormat.format(
											prop_CLS1161M01Page.getProperty("checkBorrowerAcctNo.06"),
											new StringBuffer().append(accoutNo),
											new StringBuffer().append(acctStatus)));
								}
								if(Util.equals("09", dpCate)){
									cfmList.add(MessageFormat.format(
											prop_CLS1161M01Page.getProperty("checkBorrowerAcctNo.09"),
											new StringBuffer().append(accoutNo)));
								}
								//J-113-0206_10702_B1001 web eLoan對保契約、動審檢核產品種類71之管制戶、警示戶跳提醒以及排除信貸契約書公司戶帳號
								if (accoutNo.length()>3) {
									String brNo = accoutNo.substring(0,3);
									String acct = accoutNo.substring(3,accoutNo.length());
									Map<String, Object> warnMap = cls3401Service.findODS_CMFAUDAC_ByAcc(brNo, acct);
									if (warnMap!=null) {
										cfmList.add(MessageFormat.format("<br><br>"+
												prop_CLS1161M01Page.getProperty("checkBorrowerAcctNo.warn"),
												new StringBuffer().append(accoutNo)));
									}
								}
							}
						}
					}
				}
			}
		}
		if(cfmList.size()>0){
			result.set("cfmMsg", StringUtils.join(cfmList, "<br/><br/>"));
		}
		return result;
	}
	/**
	 * 查詢黑名單
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	public IResult queryBlackName(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		return result;
	}

	/**
	 * 查詢主債務人+從債務人名單
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings({ "unchecked" })
	public IResult queryCustList(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		JSONObject json = new JSONObject();
		if (true) {
			JSONObject value = new JSONObject();
			String custId = Util.trim(Util.trim(params.getString("custId")));
			String dupNo = Util.trim(Util.trim(params.getString("dupNo")));
			String custName = Util
					.trim(Util.trim(params.getString("custName")));
			value.put("custId", custId);
			value.put("dupNo", dupNo);
			value.put("custName", custName);
			json.put(custId + dupNo, value);
		}

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String refMainId = Util.trim(params.getString("refmainId"));
		List<C160S01B> list = (List<C160S01B>) service
				.findModelByMainIdAndRefMainId(C160S01B.class, mainId,
						refMainId, null);
		for (C160S01B c160s01b : list) {
			String custId = Util.trim(c160s01b.getRId());
			String dupNo = Util.trim(c160s01b.getRDupNo());
			String custName = Util.trim(c160s01b.getRName());
			String key = custId + dupNo;
			if (!json.containsKey(key) && Util.isNotEmpty(key)) {
				JSONObject value = new JSONObject();
				value.put("custId", custId);
				value.put("dupNo", dupNo);
				value.put("custName", custName);
				json.put(key, value);
			}
		}

		result.set("cust", new CapAjaxFormResult(json));

		return result;
	}

	/**
	 * 查詢計息方式
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	public IResult L140S02CintWay(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String refmainId = Util.trim(params.getString("refmainId"));
		String mainId = Util.trim(params.getString("mainId"));
		int seq = Util.parseInt(Util.trim(params.getString("seq")));
		
		C160S01C c160s01c = clsService.findC160S01C_mainId_refMainid_seq(mainId, refmainId, seq);
		L140S02C l140s02c = clsService.findL140S02C(refmainId, seq);
		L140S02A l140s02a = clsService.findL140S02A_by_C160S01C(c160s01c);
		if (!Util.isEmpty(l140s02c)) {
			//傳到 client 端的 l140s02cflag, 當[0:顯示,1:隱藏]寬限期欄位  => 短期額度沒有寬限期 
			if (check_intway_subjCode(Util.trim(l140s02c.getIntWay()),
					Util.trim(c160s01c.getSubjCode()))) {
				result.set("l140s02cflag", 1);
			} else {
				result.set("l140s02cflag", 0);
			}
		} else {
			result.set("l140s02cflag", 0);
		}
		
		if(true){
			String key = "calcLnEndDateFlag";
			if (!Util.isEmpty(l140s02c)) {
				//傳到 client 端的 calcLnEndDateFlag => 當1, 則算頭不算尾[迄日=起日-1天]
				if (check_intway_subjCode(Util.trim(l140s02c.getIntWay()),
						Util.trim(c160s01c.getSubjCode())) || CrsUtil.is_68(c160s01c.getProdKind())) {
					result.set(key, 1);
				} else {
					result.set(key, 0);
				}
			} else {
				result.set(key, 0);
			}	
		}
		
		
		L140M01A l140m01a = clsService.findL140M01A_mainId(l140s02a.getMainId());
		// 動撥金額
		result.set("l140s02aamtflag",
				NumConverter.addComma(ClsUtil.decide_loanAmt(l140m01a, l140s02a)));
		
		BigDecimal l140s02a_rmRctAmt = BigDecimal.ZERO;
		if(l140s02a.getRmRctAmt()!=null){
			l140s02a_rmRctAmt = l140s02a.getRmRctAmt();
		}
		result.set("l140s02a_rmRctAmt", NumConverter.addComma(l140s02a_rmRctAmt));
		return result;
	}
	

	/**
	 * @param key
	 * @return
	 */
	private String getI18nMsg(String key) {
		String result = null;
		if (prop == null){
			prop = MessageBundleScriptCreator
					.getComponentResource(CLS1161S02APage.class);
		}
		if (prop != null) {
			result = prop.getProperty(Util.trim(key));
		}
		return Util.trim(result);
	}

	private boolean needLnStartEndDate(C160S01C c160s01c){
		String lnap_code = prodService.getSubject8to3(c160s01c.getSubjCode());
		if(lnap_code.startsWith("1")||lnap_code.startsWith("2")){
			/*
			  短期額度[期付金], 也要有[授信起日/迄日]
			  短期額度[按月計息, 透支]可key,可不key[授信起日/迄日]
			*/			
			L140S02C l140s02c = clsService.findL140S02C(c160s01c.getRefmainId(), c160s01c.getSeq());
			
			//l140s02c.intWay{1按月計息, 2期付金, P透支end, Q透支top}
			String intWay = Util.trim(l140s02c.getIntWay());
			
			if(Util.equals(intWay, UtilConstants.L140S02CIntway.按月計息)
				|| Util.equals(intWay, UtilConstants.L140S02CIntway.透支end)
				|| Util.equals(intWay, UtilConstants.L140S02CIntway.透支top)){
				return false;
			}
			return true;
		}else{
			return true;	
		}			
	}
	
	/**
	 * 查詢授信期間迄期是否算頭不算尾
	 * 
	 * @param subjCode
	 *            八碼授信科目
	 * @param IntWay
	 *            計息方式
	 */
	private boolean check_intway_subjCode(String intWay, String subjCode) {
		// checkcode 是 3碼科目中的第1碼(代表 短、中、長)
		String checkcode = Util.getLeftStr(
				Util.trim(prodService.getSubject8to3(subjCode)), 1);

		// 102,202 透支
		// 104,204 存摺存款透支
		if ((Util.equals(intWay, UtilConstants.L140S02CIntway.按月計息)
				|| Util.equals(intWay, UtilConstants.L140S02CIntway.透支end) 
				|| Util.equals(intWay, UtilConstants.L140S02CIntway.透支top))
			&& (Util.equals(checkcode, "1") || Util.equals(checkcode, "2"))) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 額度明細表-資料檢核
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	public IResult dataCheck(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		/*
		  	開啟 動審表 裡的「額度明細表」時，有[儲存]、[關閉]
		  	● 在[關閉]才去 call dataCheck(PageParameters params, Component parent) ===> 變更 c160m01b.datacheck
		  	● 在[關閉]也會出現「純提示」的 promptMsg
		  		例如：   C160S01C.warn4=此存款帳號擁有人不屬於借款人/連保人，請確認是否使用此存款帳號！
		 */
		String oid = Util.trim(params.getString(EloanConstants.OID));
		C160M01B c160m01b = service.findModelByOid(C160M01B.class, oid);
		String message = "";
		if (c160m01b != null) {
			boolean check = true;
			try {
				message = checkC160M01B(c160m01b);
			} catch (CapException e) {
				check = false;
				message = e.getMessage();
			} finally {
				c160m01b.setDataCheck(check ? UtilConstants.DEFAULT.是 : "");
				service.save(c160m01b);
			}
		}
		result.set("message", message);

		return result;
	}
	
	private StringBuilder checkVacantLandValue(C160M01B c160m01b){
		
		StringBuilder sb = new StringBuilder();
		
		//本案是否為撥貸未動工興建之空地貸款控管對象
		if(CapString.isEmpty(c160m01b.getIsClearLand())){
			sb.append(EloanConstants.HTML_NEWLINE);
			sb.append(getI18nMsg("L140M01M.isClearLand")+" 必填欄位");
			return sb;
		}
		
		//本案是否為撥貸未動工興建之空地貸款控管對象 與 ELF600資料不一致
		ELF600 elf600 = misELF600Service.findByContract(c160m01b.getCntrNo());
		String isClearLand = lmsService.isClearLandEffective(elf600);
		if(!CapString.isEmpty(c160m01b.getIsClearLand()) && !isClearLand.equals(c160m01b.getIsClearLand())){
			sb.append(EloanConstants.HTML_NEWLINE);
			sb.append(getI18nMsg("L140M01M.isClearLand")+ "為[" + ("Y".equals(isClearLand) ? "是" : "否") + "] 請重新引進");
			return sb;
		}
		
		//本案是否為撥貸未動工興建之空地貸款控管對象 為 "否"
		if("N".equals(c160m01b.getIsClearLand())){
			return sb;
		}
		
		//初次核定預計動工日
		if(null == c160m01b.getFstDate()){
			sb.append(EloanConstants.HTML_NEWLINE);
			sb.append(getI18nMsg("L140M01M.fstDate")+" 必填欄位");
		}
		//最新核定(動審)預計動工日
		if(null == c160m01b.getLstDate()){
			sb.append(EloanConstants.HTML_NEWLINE);
			sb.append(getI18nMsg("L140M01M.lstDate")+" 必填欄位");
		}
		
		if (CrsUtil.isNOT_null_and_NOTZeroDate(elf600.getElf600_fstdate()) 
				&& elf600.getElf600_fstdate().compareTo(c160m01b.getFstDate()) != 0) {
			
			sb.append(EloanConstants.HTML_NEWLINE);
			sb.append("初次核定預計動工日[" + CapDate.formatDate(c160m01b.getFstDate(), "yyyy-MM-dd")
					+ "] 與 ELF600控制檔[" 
					+ CapDate.formatDate(elf600.getElf600_fstdate(), "yyyy-MM-dd") + "]不一致");
		}

		if (CrsUtil.isNOT_null_and_NOTZeroDate(elf600.getElf600_lstdate()) 
				&& elf600.getElf600_lstdate().compareTo(c160m01b.getLstDate()) != 0) {
			
			sb.append(EloanConstants.HTML_NEWLINE);
			sb.append("最新核定(動審)預計動工日[" + CapDate.formatDate(c160m01b.getLstDate(), "yyyy-MM-dd")
					+ "] 與 ELF600控制檔["
					+ CapDate.formatDate(elf600.getElf600_lstdate(), "yyyy-MM-dd") + "]不一致");
		}
		
		//是否變更預計動工日
		String isChgStDate = c160m01b.getIsChgStDate();
		if(CapString.isEmpty(isChgStDate)){
			sb.append(EloanConstants.HTML_NEWLINE);
			sb.append(getI18nMsg("L140M01M.isChgStDate")+" 必填欄位");
		}
		
		if("Y".equals(isChgStDate)){
			//變更預計動工日
			if(null == c160m01b.getCstDate()){
				sb.append(EloanConstants.HTML_NEWLINE);
				sb.append(getI18nMsg("L140M01M.cstDate")+" 必填欄位");
			}
			//變更預計動工日原因
			if(CapString.isEmpty(c160m01b.getCstReason())){
				sb.append(EloanConstants.HTML_NEWLINE);
				sb.append(getI18nMsg("L140M01M.cstReason")+" 必填欄位");
			}
			//輸入本次採行措施
			if(CapString.isEmpty(c160m01b.getAdoptFg())){
				sb.append(EloanConstants.HTML_NEWLINE);
				sb.append(getI18nMsg("L140M01M.adoptFg")+" 必填欄位");
			}
		}
		//是否調整利率
		String isChgRate = c160m01b.getIsChgRate();
		if("N".equals(isChgStDate) && CapString.isEmpty(isChgRate)){
			sb.append(EloanConstants.HTML_NEWLINE);
			sb.append(getI18nMsg("L140M01M.isChgRate")+" 必填欄位");
		}
		
    	if("Y".equals(isChgRate) || ("Y".equals(isChgStDate) && StringUtils.contains(c160m01b.getAdoptFg(), "3"))){
    		//再加減碼幅度
    		if(null == c160m01b.getRateAdd()){
    			sb.append(EloanConstants.HTML_NEWLINE);
    			sb.append(getI18nMsg("L140M01M.rateAdd")+" 必填欄位");
			}
    		//借款人ROA
    		if(null == c160m01b.getCustRoa()){
    			sb.append(EloanConstants.HTML_NEWLINE);
    			sb.append(getI18nMsg("L140M01M.custRoa")+" 必填欄位");
			}
    		//關係人ROA
    		if(null == c160m01b.getRelRoa()){
    			sb.append(EloanConstants.HTML_NEWLINE);
    			sb.append(getI18nMsg("L140M01M.relRoa")+" 必填欄位");
			}
    	}
    	
    	//是否符合本行規定
    	if(("Y".equals(isChgStDate) || "Y".equals(isChgRate)) && CapString.isEmpty(c160m01b.getIsLegal())){
    		sb.append(EloanConstants.HTML_NEWLINE);
			sb.append(getI18nMsg("L140M01M.isLegal")+" 必填欄位");
		}
    	
    	return sb;
	}

	/**
	 * 檢查額度明細表項下所有資料是否完成
	 * 
	 * @param c160m01b
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings({ "unchecked" })
	public String checkC160M01B(C160M01B c160m01b) throws CapException {
		StringBuilder message = new StringBuilder();

		if (c160m01b != null) {
			String mainId = Util.trim(c160m01b.getMainId());
			String refMainId = Util.trim(c160m01b.getRefmainId());

			StringBuilder cntrNo = new StringBuilder();
			cntrNo.append(getI18nMsg("C160M01B.cntrNo")).append("[")
					.append(c160m01b.getCntrNo()).append("] ");

			StringBuilder errMsg = new StringBuilder();
			errMsg.append("<b>").append(getI18nMsg("title")).append("</b>");
			errMsg.append(EloanConstants.HTML_NEWLINE);
			errMsg.append(cntrNo.toString());

			boolean valid = BeanValidator.isValid(c160m01b, Check2.class);
			if (!valid) {
				errMsg.append(BeanValidator.getValidMsg(c160m01b,
						CLS1161S02APage.class, Check2.class));
				throw new CapMessageException(errMsg.toString(), getClass());
			}
			
			// J-108-0083 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制 動審表檢核
			StringBuilder vacantLandErrMsg = this.checkVacantLandValue(c160m01b);
			if (vacantLandErrMsg.length() > 0) {
	    		errMsg.append(getI18nMsg("title.tab01")).append(" ");
	    		errMsg.append("[").append(getI18nMsg("L140M01M.clearLand")).append("]");
				errMsg.append(EloanConstants.HTML_NEWLINE);
				errMsg.append(vacantLandErrMsg.toString());
				throw new CapMessageException(errMsg.toString(), getClass());
			}
			
			StringBuilder alert = new StringBuilder();
			Class<?>[] classes2 = { C160S01A.class, C160S01B.class, C160S01C.class };
			for (Class<?> clazz2 : classes2) {
				List<GenericBean> list2 = (List<GenericBean>) service
						.findModelByMainIdAndRefMainId(clazz2, mainId,
								refMainId, null);
				
				for (GenericBean bean2 : list2) {

					// 擔保品資料檔
					if (bean2 instanceof C160S01A) {

					}
					// 主從債務人資料表檔
					else if (bean2 instanceof C160S01B) {
						StringBuilder sb = new StringBuilder();

						C160S01B c160s01b = (C160S01B) bean2;
						String rId = Util.trim(c160s01b.getRId());
						String rDupNo = Util.trim(c160s01b.getRDupNo());
						Map<String, Object> custData = mcs
								.findAllByByCustIdAndDupNo(rId, rDupNo);
						if (custData == null || custData.isEmpty()) {
							sb.append(" 客戶中文檔0024 無此借款人資料 ！！");
						}
						// check valid msg
						sb.append(BeanValidator.getValidMsg(bean2,
								CLS1161S02APage.class, SaveCheck.class));

						// N.一般保證人
						if ("N".equals(c160s01b.getRType())) {
							sb.append(BeanValidator.getValidMsg(bean2,
									CLS1161S02APage.class, Check2.class));

							// 99.其它
							if ("99".equals(c160s01b.getReson())) {
								sb.append(BeanValidator.getValidMsg(bean2,
										CLS1161S02APage.class, Check3.class));
							}
						}

						if (sb.length() > 0) {
							errMsg.append(getI18nMsg("title.tab03"))
									.append(" ");
							errMsg.append(getI18nMsg("C160S01B.rId")).append(
									"[");
							errMsg.append(rId).append(" ").append(rDupNo)
									.append("]");
							errMsg.append(EloanConstants.HTML_NEWLINE);
							errMsg.append(sb.toString());
							throw new CapMessageException(errMsg.toString(),
									getClass());
						}
					}
					// 個金產品種類檔
					else if (bean2 instanceof C160S01C) {
						JSONObject json = DataParse.toJSON(bean2);
						
						// 扣款帳號
						alert.append(service.checkAccount(json, "atpayNo"));
						// 存款帳號/進帳帳號
						alert.append(service.checkAccount(json, "accNo"));

						StringBuilder sb = new StringBuilder();
						// check valid msg
						sb.append(BeanValidator.getValidMsg(bean2,
								CLS1161S02APage.class, SaveCheck.class));

						if(true){
							C160S01C c160s01c = (C160S01C)bean2;
							if(needLnStartEndDate(c160s01c)){
								Date lnStartDate = c160s01c.getLnStartDate();
								Date lnEndDate = c160s01c.getLnEndDate();
								if(lnStartDate==null){
									sb.append(EloanConstants.HTML_NEWLINE);
									sb.append(getI18nMsg("C160S01C.lnStartDate")+" 必填欄位");
								}
								if(lnEndDate==null){
									sb.append(EloanConstants.HTML_NEWLINE);
									sb.append(getI18nMsg("C160S01C.lnEndDate")+" 必填欄位");
								}
							}
						}
						
						//J-111-0227 配合開放入帳及扣款他行帳號調整檢核
						if (Util.equals("01", Util.trim(json.get("payType"))) &&
								Util.notEquals(UtilConstants.DEFAULT.是, Util.trim(json.get("autoPay"))) ){
							sb.append(EloanConstants.HTML_NEWLINE);
							sb.append(getI18nMsg("C160S01C.achPay")+"需為 "+getI18nMsg("C160S01C.autoPay"));//ACH扣帳需為自動扣帳
						}
						
						// 自動扣帳 autoPay check
						if (UtilConstants.DEFAULT.是.equals(Util.trim(json
								.get("autoPay")))) {
							if (Util.equals("01", Util.trim(json.get("payType")))){//ACH扣帳
								C160S01C c160s01c = (C160S01C)bean2;
								//檢核他行帳號扣款帳號
								String errorMsg = check_c160s01c_achAcct(c160s01c);
								if(Util.isNotEmpty(errorMsg)){
									sb.append(errorMsg);
								}
							}else{
								if (!BeanValidator.isValid(bean2, Check2.class)) {
									sb.append(EloanConstants.HTML_NEWLINE);
									sb.append(BeanValidator.getValidMsg(bean2,
										CLS1161S02APage.class, Check2.class));
								}
							}
						}
						// 自動進帳 autoRct check
						if (UtilConstants.DEFAULT.是.equals(Util.trim(json
								.get("autoRct")))) {
							if(	Util.isNotEmpty(Util.trim(json.get("appOtherAccount"))) ||
									Util.isNotEmpty(Util.trim(json.get("appOtherBranchNo"))) ||
									Util.isNotEmpty(Util.trim(json.get("appOtherBranchNm"))) ){
								//撥款他行帳號相關欄位不為空值的時候檢核走這邊
								C160S01C c160s01c = (C160S01C)bean2;
								String errorMsg = check_c160s01c_appOtherBankAcct(c160s01c);
								if(Util.isNotEmpty(errorMsg)){
									sb.append(errorMsg);
								}
							}else{
								if (!BeanValidator.isValid(bean2, Check3.class)) {
									sb.append(EloanConstants.HTML_NEWLINE);
									sb.append(BeanValidator.getValidMsg(bean2,
										CLS1161S02APage.class, Check3.class));
								}
							}
							if(true){
								C160S01C c160s01c = (C160S01C)bean2;
								String check_msg = check_C160S01C_autoRct(c160s01c);
								if(Util.isNotEmpty(check_msg)){
									sb.append(EloanConstants.HTML_NEWLINE);
									sb.append(check_msg);
								}
							}
						}
						//J-111-0636 新增私人銀行自動進帳帳號跟自動扣帳帳號檢核
						if(true){
							C160S01C c160s01c = (C160S01C)bean2;
							sb.append(checkAcctForBranch149(c160s01c));
						}
						
						//-----------
						List<String> err_check_c160s01c = check_c160s01c((C160S01C)bean2);
						if(err_check_c160s01c.size()>0){
							if(sb.length()>0){
								sb.append(EloanConstants.HTML_NEWLINE);
							}
							sb.append(StringUtils.join(err_check_c160s01c, EloanConstants.HTML_NEWLINE));
						}
						//-----------
						C160S01E c160s01e = (C160S01E) bean2.get("c160s01e");
						if (c160s01e != null) {
							// 轉貸 model check
							if (UtilConstants.DEFAULT.是.equals(Util
									.trim(c160s01e.getChgOther()))) {
								if (!BeanValidator.isValid(c160s01e,
										Check2.class)) {
									sb.append(EloanConstants.HTML_NEWLINE);
									sb.append(BeanValidator.getValidMsg(
											c160s01e, CLS1161S02APage.class,
											Check2.class));
								}
								// 如果轉貸為是且自動進帳為是時，則要檢核轉貸日期=自動進帳
								if (UtilConstants.DEFAULT.是.equals(Util
										.trim(bean2.get("autoRct")))) {
									Date rctDate = (Date) bean2.get("rctDate");
									if (rctDate != null) {
										if (!rctDate.equals(c160s01e
												.getOnlentDate())) {
											sb.append(EloanConstants.HTML_NEWLINE);
											sb.append("轉貸日期必需等於自動進帳");
										}
									}
								}

								// 轉貸為是時至少需要有一筆代償明細
								List<GenericBean> list3 = (List<GenericBean>) service
										.findModelByMainIdAndRefMainId(
												C160S01F.class, mainId,
												refMainId,
												Util.parseInt(json.get("seq")));
								if (list3 != null && list3.size() == 0) {
									sb.append("至少需要有一筆代償明細。");
								}

							}
							// check 手續費
							if (UtilConstants.DEFAULT.是.equals(Util
									.trim(c160s01e.getChargeFlag()))) {
								if (!BeanValidator.isValid(c160s01e,
										Check3.class)) {
									sb.append(EloanConstants.HTML_NEWLINE);
									sb.append(BeanValidator.getValidMsg(
											c160s01e, CLS1161S02APage.class,
											Check3.class));
								}
							}
						}

						// out check message
						if (sb.length() > 0) {
							errMsg.append(getI18nMsg("title.tab04"))
									.append(" ");
							errMsg.append(getI18nMsg("C160S01C.seq")).append(
									"[");
							errMsg.append(Util.trim(json.get("seq"))).append(
									"]");
							errMsg.append(EloanConstants.HTML_NEWLINE);
							errMsg.append(sb.toString());
							throw new CapMessageException(errMsg.toString(),
									getClass());
						}
					}
				}
			}

			if (alert.length() > 0) {
				message.append(cntrNo.toString());
				message.append(EloanConstants.HTML_NEWLINE);
				message.append(alert.toString());
			}
		}

		return message.toString();
	}
	/**
	 * 刪除各項費用資料
	 * 
	 * @param params
	 *            PageParameters
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL140M01R(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString("deleteMainOid");
		String mainId = params.getString("deleteMainId");

		L140M01R l140m01r = new L140M01R();
		l140m01r.setOid(oid);
		l140m01r.setMainId(mainId);
		cls1141Service.deleteL140M01R(l140m01r);

		// throw new CapException();

		return result;
	}

	/**
	 * 新增各項費用資料
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult addL140M01R(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String formL1140m01r = params.getString("formL1140m01r");
		L140M01R l140m01r = new L140M01R();
		DataParse.toBean(formL1140m01r, l140m01r);

		l140m01r.setCreator(user.getUserId());
		l140m01r.setCreateTime(CapDate.getCurrentTimestamp());
		l140m01r.setUpdater(user.getUserId());
		l140m01r.setUpdateTime(CapDate.getCurrentTimestamp());
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String tCaseNO = Util.toSemiCharString(Util.trim(params
				.getString("caseNo"))); // l120m01a.getCaseNo();

		l140m01r.setMainId(mainId);
		l140m01r.setCaseNo(tCaseNO);
		Integer tCaseYear = params.getAsInteger("caseYear");
		String tCaseBrId = Util.trim(params.getString("caseBrId"));
		Integer tCaseSeq = params.getAsInteger("caseSeq");

		l140m01r.setCaseYear(tCaseYear);
		l140m01r.setCaseBrId(tCaseBrId);
		l140m01r.setCaseSeq(tCaseSeq);

		l140m01r.setFeeSrc("2");

		cls1141Service.saveL140M01R(l140m01r);
		return result;
	}

	/**
	 * 更新各項費用資料
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult updateL140M01R(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String formL1140m01r = params.getString("formL1140m01r");
		String l140m01roid = params.getString("l140m01roid");
		L140M01R l140m01r = cls1141Service.getL140M01R(l140m01roid);

		if (l140m01r == null) {
			l140m01r = new L140M01R();
		}

		StringBuilder errMsg = new StringBuilder();
		if ("N".equals(service.checkMisToUpdateL140M01R(l140m01r))) {
			// errorMsg.011 = 本筆資料已經被帳務系統異動過不可再變動。
			errMsg.append(getI18nMsg("errorMsg.011"));
			throw new CapMessageException(errMsg.toString(), getClass());
		}

		DataParse.toBean(formL1140m01r, l140m01r);

		l140m01r.setUpdater(user.getUserId());
		l140m01r.setUpdateTime(new Timestamp(System.currentTimeMillis()));
		// parseL140M01R(params, l140m01r);

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String tCaseNO = Util.toSemiCharString(Util.trim(params
				.getString("caseNo"))); // l120m01a.getCaseNo();

		l140m01r.setMainId(mainId);
		l140m01r.setCaseNo(tCaseNO);

		Integer tCaseYear = NumConverter.delCommaInteger(params
				.getString("caseYear"));
		String tCaseBrId = Util.trim(params.getString("caseBrId"));
		Integer tCaseSeq = NumConverter.delCommaInteger(params
				.getString("caseSeq"));

		l140m01r.setCaseYear(tCaseYear);
		l140m01r.setCaseBrId(tCaseBrId);
		l140m01r.setCaseSeq(tCaseSeq);

		cls1141Service.saveL140M01R(l140m01r);

		return result;
	}

	/**
	 * 取得各項費用內容資料
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getL140M01R(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString("l140m01roid");
		L140M01R l140m01r = cls1141Service.getL140M01R(oid);

		// logger.debug(l192s02a.toString());

		// result.add(new CapAjaxFormResult(l192s02a.toString()));
		result.add(DataParse.toResult(l140m01r));
		String l140m01roid = (String) result.get("oid");
		result.removeField("oid");
		result.set("l140m01roid", l140m01roid);
		// logger.debug(result.get("oid").toString());
		logger.debug(result.get("l140m01roid").toString());

		return result;
	}

	/**
	 * 由動審表中所對應的簽報書中引入各項費用資料
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult getL140M01RByC160M01A(PageParameters params)
			throws CapException {
		// MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID)); // CLS1161M01.mainId

		List<Map<String, Object>> list = eloandbbaseservice
				.findL120M01AByC160M01A(mainId);

		String[] mainIds = new String[list.size()];

		for (int i = 0; i < list.size(); i++) {
			mainIds[i] = Util.trim(list.get(i).get("mainId"));
		}

		service.reNewL140M01R(mainIds, mainId);

		return result;
	}

	/**
	 * 由動審表中所對應的額度序號引入個人清冊
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult getC801M01AByL140M01A(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID)); // CLS1161M01.mainId

		List<Map<String, Object>> list = eloandbbaseservice
				.findL140M01AByC160M01A(mainId);

		String[] cntrNos = new String[list.size()];

		for (int i = 0; i < list.size(); i++) {
			cntrNos[i] = Util.trim(list.get(i).get("CNTRNO"));
		}

		service.reNewC801M01A(cntrNos, mainId);

		return result;
	}

	/**
	 * 動審表異動核准額度資料
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult changeC160M01BAmt(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		// String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String mainId = params.getString("mainId");
		String refMainId = params.getString("refmainId");
		BigDecimal loanTotAmt = Util.parseToBigDecimal(params
				.getString("loanTotAmt"));

		service.setC160M01BApprovedAmt(mainId, refMainId, loanTotAmt);
		add_s_loanTotAmt(result, loanTotAmt);

		this._calLoanTotalData(result, mainId);
		// result.set("totalForm", totalForm);
		return result;
	}

	private void add_s_loanTotAmt(CapAjaxFormResult formResult,
			BigDecimal loanTotAmt) {
		formResult.set("s_loanTotAmt", NumConverter.addComma(loanTotAmt));
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult queryC160M01BAmt(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		// String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String C160M01BForm = params.getString("C160M01BForm");
		JSONObject json = JSONObject.fromObject(C160M01BForm);
		String oid = json.getString("oid");
		C160M01B c160m01b = service.findModelByOid(C160M01B.class, oid);

		if ("".equals(Util.trim(c160m01b.getBfLoanTotAmt()))) {
			result.set("bfLoanTotAmt",
					NumConverter.addComma(c160m01b.getLoanTotAmt()));
		} else {
			result.set("bfLoanTotAmt",
					NumConverter.addComma(c160m01b.getBfLoanTotAmt()));
		}

		return result;
	}

	@Deprecated
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult proc_l120s09a(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		if(LMSUtil.cmpDate(CapDate.getCurrentTimestamp(), ">=", CapDate.parseDate("2018-01-10"))){
			throw new CapMessageException("本功能已停用，請按Ctrl+F5後重新操作。", getClass());
		}
		return result;
	}
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult sync_l120s09a_to_c160m01b(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		C160M01A c160m01a = clsService.findC160M01A_mainId(mainId);
					
		String caseType = c160m01a.getCaseType();
		if (Util.equals(UtilConstants.Usedoc.caseType2.一般, caseType) 
				|| Util.equals(UtilConstants.Usedoc.caseType2.團貸, caseType)) {
			
			List<C160M01B> c160m01b_list = clsService.findC160M01B_mainId(c160m01a.getMainId());
			List<L120S09A> l120s09a_list = clsService.findL120S09A(mainId);
			Map<String, String> idDup_queryDate_map = new HashMap<String, String>();
			for(L120S09A l120s09a : l120s09a_list){
				idDup_queryDate_map.put(LMSUtil.getCustKey_len10custId(l120s09a.getCustId(), l120s09a.getDupNo())
						, Util.trim(TWNDate.toAD(l120s09a.getQueryDateS())));
			}
			
			for (C160M01B c160m01b : c160m01b_list) {
				LinkedHashMap<String, String> itemMap = new LinkedHashMap<String, String>();
				if(true){
					// 額度明細表借款人
					itemMap.put(LMSUtil.getCustKey_len10custId(c160m01b.getCustId(), c160m01b.getDupNo()), c160m01b.getCustName());
					
					// 額度明細表-從債務人			
					for (C160S01B c160s01b : clsService.findC160S01B(c160m01b)) {
						String cId = c160s01b.getRId();
						String cNo = c160s01b.getRDupNo();
						itemMap.put(LMSUtil.getCustKey_len10custId(cId, cNo), c160s01b.getRName());
					}	
				}
				
				if(true){
					c160m01b.setBlackDataDate(null);
					String val = idDup_queryDate_map.get(LMSUtil.getCustKey_len10custId(c160m01b.getCustId(), c160m01b.getDupNo()));
					if(Util.isNotEmpty(val)){
						c160m01b.setBlackDataDate(CapDate.parseDate(val));	
					}
				}
				c160m01b.setBlackDesc(build_blackDesc(l120s09a_list, itemMap));
				clsService.save(c160m01b);
				//=================================					
			}
		}		
		return result;
	}
	
	/** 參考 LMSCOMMONFormHandler :: queryBlackName
	 * @param l120s09a_list
	 * @param itemMap
	 * @return
	 */
	private String build_blackDesc(List<L120S09A> l120s09a_list, LinkedHashMap<String, String> itemMap){
		List<String> r = new ArrayList<String>(); 
		for(String idDup : itemMap.keySet()){
			String output_id = Util.trim(StringUtils.substring(idDup, 0, 10));
			String output_dup = Util.trim(StringUtils.substring(idDup, 10));
			String output_eName = "";
			String output_cName = "";
			String output_result = "";
			
			for(L120S09A l120s09a: l120s09a_list){				
				if(Util.equals(idDup, LMSUtil.getCustKey_len10custId(l120s09a.getCustId(), l120s09a.getDupNo()))){
					output_eName = l120s09a.getCustEName();
					output_cName = l120s09a.getCustName();
					output_result = l120s09a.getBlackListCode();
					break;
				}
			}
			
			String message = Util.trim(output_id)+(" ")+
						 Util.trim(output_dup)+(" ")+
						 Util.trim(output_eName);
				
			if (Util.isNotEmpty(output_cName)) {
				//已在 】後面, 加入半形空白
				message+= ("【"+Util.trim(output_cName)+"】 ");
			}
			
			if(Util.equals(output_result, UtilConstants.Casedoc.L120s09aBlackListCode.是黑名單)){
				message+=("存在於黑名單資料");
			}else if(Util.equals(output_result, UtilConstants.Casedoc.L120s09aBlackListCode.可能是黑名單)){
				/*
				 * 2017-02-08 經向四組確認 未來可能隨時增加 "不同組織/單位"
				 * 的黑名單比對 為免每增加一個名單, 前端系統就要再改一次程式
				 * 
				 * 目前黑名單比對,只會傳回 02(正中),04(疑似) *
				 */
				message+=("請至 0015-11 系統提示疑似黑名單查詢");
			}else if(Util.equals(output_result, UtilConstants.Casedoc.L120s09aBlackListCode.未列於黑名單)){
				message+=("未列於黑名單");
			}else{
				message+=(output_result);
			}
			//=============
			r.add(message);
		}
		return StringUtils.join(r, "<BR>");
	}
	private void _calLoanTotalData(CapAjaxFormResult totalForm, String mainId) 
	throws CapMessageException{
		BigDecimal total = new BigDecimal(0);

		List<C160M01B> list = clsService.findC160M01B_mainId(mainId);

		Set<String> isHavecntrNos = new HashSet<String>();
		String refMainId = "";
		for (C160M01B c160m01b : list) {
			refMainId = Util.trim(c160m01b.getRefmainId());
			L140M01A l140m01a = service.findModelByMainId(L140M01A.class,
					refMainId);
			if(l140m01a==null){
				throw new CapMessageException(Util.trim(c160m01b.getCntrNo())
						+" 查無關聯資料 "+refMainId
						, getClass());
			}
			if (!isHavecntrNos.contains(l140m01a.getCommSno())) {
				isHavecntrNos.add(l140m01a.getCommSno());
			}
		}

		for (C160M01B c160m01b : list) {
			// refMainId = Util.trim(c160m01b.getRefmainId());
			// L140M01A l140m01a = service.findModelByMainId(L140M01A.class,
			// refMainId);
			if (!isHavecntrNos.contains(c160m01b.getCntrNo())) {
				BigDecimal bd = c160m01b.getLoanTotAmt();
				if (bd != null)
					total = total.add(bd);
				isHavecntrNos.add(c160m01b.getCntrNo());
			}
		}
		totalForm.set("loanTotal", NumConverter.addComma(total.toString()));
		totalForm.set("loanCount", String.valueOf(list.size()));
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult rmCalc_cls1161(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		if(Util.isEmpty(Util.trim(params.getString("rctAMT")))){
			throw new CapMessageException(getI18nMsg(get_rctAmt_descKey(params.getString("prodKind")))+"不可空白", getClass());
		}
		BigDecimal approvedAmt = BigDecimal.ZERO;
		BigDecimal rmIntMax = BigDecimal.ZERO;
		if(true){
			BigDecimal rctAMT = Util.parseBigDecimal(params.getDouble("rctAMT"));	
        	Integer year = Util.parseInt(params.getString("year"));
        	Integer month = Util.parseInt(params.getString("month"));
        	
        	int totalPeriod = ClsUtil.prod_totalPeriod(year, month);
        	approvedAmt = Arithmetic.mul(rctAMT, new BigDecimal(totalPeriod));
        	rmIntMax = LMSUtil.prod_rmIntMax(rctAMT);
		}
		result.set("approvedAmt", NumConverter.addComma(approvedAmt));
		result.set("rmIntMax", NumConverter.addComma(rmIntMax));
		
		return result;
	}
	
	/** 提問單 20190724F000021 反映, 按月計息 選 自動進帳
	*/
	private String check_C160S01C_autoRct(C160S01C c160s01c){
		if(Util.equals("Y", c160s01c.getAutoRct()) && clsService.is_function_on_codetype("20190724F000021")){
			//按月計息(除了 67:以房養老-傳統型 之外) => 自動進帳 需為 否
			if(CrsUtil.is_67(c160s01c.getProdKind())){
				return "";
			}else if(CrsUtil.is_70(c160s01c.getProdKind())){
				//70:以房養老-累積型 限制｛臨櫃撥款｝=> 自動進帳 需為 否
				return "";
			}
			
			L140S02C l140s02c = clsService.findL140S02C(c160s01c.getRefmainId(), c160s01c.getSeq());
			if(l140s02c!=null){
				String intWay = Util.trim(l140s02c.getIntWay());// 計息方式
				String rIntWay = Util.trim(l140s02c.getRIntWay());// 收息方式
				if (!Util.isEmpty(rIntWay)) {
					if (Util.equals(rIntWay, "6")) {// 收息方式等於期付金時
						return "";// 期付金
					}else{
						if(Util.equals(intWay, UtilConstants.L140S02CIntway.透支end)
								|| Util.equals(intWay, UtilConstants.L140S02CIntway.透支top)){
							return "計息方式「透支」不可選擇 自動進帳";
						}else{
							return "計息方式「按月計息」不可選擇 自動進帳"; //按月計息
						}
					} 
				}
			}
		}
		return "";
	}
	// J-109-0150_10702_B1001 Web e-Loan IVR頁籤由模擬動審移至動審表

	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult addIVRList(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		List<String> datas = Arrays.asList(params.getStringArray("rows"));
		if (!Util.isEmpty(datas) && datas.size() > 0) {
			service.saveIVRFlag2(
					params.getString(EloanConstants.MAIN_OID), datas);
		}
		return result;
	}

	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteIVRList(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String oid = params.getString(EloanConstants.MAIN_OID);
		String datas = params.getString("record_FileName");
		String deleteCustId = params.getString("deleteCustId");

		service.deleteIVRFlag2(oid, deleteCustId, datas);

		return result;
	}

	/**
	 * 經辦照會
	 *
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult noteTheApply(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		C160M01A meta = clsService.findC160M01A_mainId(mainId);
		if(meta==null){
			throw new CapMessageException("["+mainId+"]not found", getClass());
		} 
		meta.setNoter(user.getUserId());
		meta.setNoteTime(CapDate.getCurrentTimestamp());
		clsService.daoSave(meta);

		result.set("noter", meta.getNoter());
		result.set("noteTime", meta.getNoteTime());

		return result;
	}
	
	/**
	 * 紀錄聯徵查詢結果
	 *
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult recordEjcicResultData(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		C160M01A meta = clsService.findC160M01A_mainId(mainId);
		if(meta==null){
			throw new CapMessageException("["+mainId+"]not found", getClass());
		}
		// 若簽報書無聯徵資料時，不引入資料
		List<C120S02A> c120s02as = clsService.findC120S02A(meta.getSrcMainId(), meta.getCustId(), meta.getDupNo());
		if (c120s02as == null || c120s02as.isEmpty()) {
			return result;
		}
		String custId = meta.getCustId();
		String dupNo = meta.getDupNo();
		String prodId = ejcicService.get_cls_PRODID(custId);
		Map<String, Object> dateMap = ejcicService.getDate(custId, prodId);
		if (dateMap == null) {
			// message.c_query_credit.noJCIC=無聯徵查詢結果。請先查詢聯徵後，再執行引進！
			throw new CapMessageException(getI18nMsg("message.c_query_credit.noJCIC"), getClass());
		}
		// 取得相關日期
		String QDATE = Util.trim(dateMap.get("QDATE"));
		Map<String, Object> ejcicRecord = cls1131Service.getEjcicReusltRecord(custId, prodId, QDATE, meta.getMainId(), meta.getDupNo());
		Map<String, Object> ejcicSTLogMap = this.ejcicService.getLatestLogFileByQkey1AndProdId(custId, "ST");
		if(ejcicSTLogMap != null){
			this.cls1161Service.getEjcicReusltRecordForPreMoneyAllocatingInquiry(custId, (String)ejcicSTLogMap.get("PRODID"), (String)ejcicSTLogMap.get("QDATE"));
		}
		
		List<CodeType> codeTypes = cts.findByCodeTypeList("C101S02A_ejcicItem");
		for (CodeType codeType : codeTypes) {
			String item = codeType.getCodeValue();
			C160S02A s02a = clsService.findC160S02AByItem(mainId, custId, dupNo, item);
			if (s02a == null) {
				s02a = new C160S02A();
				s02a.setMainId(mainId);
				s02a.setCustId(custId);
				s02a.setDupNo(dupNo);
				s02a.setEjcicItem(item);
				s02a.setJsonOb(new JSONObject().toString());
			}
			if (ejcicRecord.containsKey(item)) {
				JSONObject json = JSONObject.fromObject(ejcicRecord.get(item));
				s02a.setJsonOb(json.toString());
				s02a.setDataDate(TWNDate.valueOf(QDATE).toDate());
			}
			clsService.save(s02a);
		}
		// 判斷是否資信轉差
		List<Map<String, Object>> list = service.getJcicResultCompareMap(mainId);
		for (Map<String, Object> map : list) {
			if ("Y".equals(CapString.trimNull(map.get("worse")))) {
				// cls1161.msg2=本案借款人資信轉差，請再次檢視授信條件是否合規
				result.set("msg", prop_CLS1161M01Page.getProperty("cls1161.msg2"));
				break;
			}
		}
		return result;
	}
	
	/**
	 * 判斷登入者是否僅有EL電銷權限
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult check_only_expermission(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		result.set("only_ex_permission", user.isEXAuth());//是否僅有電銷權限	
		return result;
	}
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult queryBeforeMoneyAllocating(PageParameters params)
			throws Exception {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId_c160m01a = Util.trim(params.getString(EloanConstants.MAIN_ID));
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		C160M01A meta = clsService.findC160M01A_mainId(mainId_c160m01a);
		
		boolean isRun = this.cls1161Service.isRunInQueryBeforeMoneyAllocating(meta.getMainId());
		if(!isRun){
			result.set("tipsMsg", "無產品種類為07或71額度明細表!");
			return result;
		}
			
		List<C160M01B> c160m01bList = this.clsService.findC160M01B_mainId(mainId_c160m01a);
		for(C160M01B c160m01b : c160m01bList){
			
			L140M01A l140m01a = this.clsService.findL140M01A_mainId(c160m01b.getRefmainId());
			List<L140S02A> l140s02aList = this.clsService.findL140S02A(l140m01a);
			
			String ownBrid = meta.getOwnBrId();
			String custId = l140m01a.getCustId();
			String dupNo = l140m01a.getDupNo();
			
			for(L140S02A l140s02a : l140s02aList){
				if("07".equals(l140s02a.getProdKind()) || "71".equals(l140s02a.getProdKind())){
					
					C101M01A c101m01a = clsService.findC101M01A_brIdDup(ownBrid, custId, dupNo);
					
					//check before query
					this.cls1161Service.checkBeforeEjcicInquiry(user, c101m01a.getMainId(), mainId_c160m01a);
					//query ejcic
					this.cls1161Service.queryEjcicBeforeSendMoney(user.getUserId(), user.getUnitNo(), mainId_c160m01a, custId, dupNo);
					
					// 客戶是否為利害關係人資料
					Map<String, Object> map = cls1131Service.getStakeholderData(custId, dupNo, c101m01a.getCustName());
					params.put("stakeholderMap", map);
					byte[] report2 = cls1131R04RptService.getContent(params);
					cls1131Service
							.modifyC101S01SForMixRecordData(mainId_c160m01a, custId, dupNo,
									ClsConstants.C101S01S_dataType.客戶是否為利害關係人資料, (String) map.get("dataStatus"), report2);
					//家事查詢
					this.rpaProcessService.deleteBeforeQueryData(mainId_c160m01a, custId);
					this.rpaProcessService.gotoRPAJobs(new C101S04W(mainId_c160m01a, custId));
				}
			}
		}
		
		return new CapAjaxFormResult();
	}
	
}
