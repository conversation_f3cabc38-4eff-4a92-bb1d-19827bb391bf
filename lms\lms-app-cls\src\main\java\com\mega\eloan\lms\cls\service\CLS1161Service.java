/* 
 * CLS1161Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.http.client.ClientProtocolException;
import org.kordamp.json.JSONObject;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.lms.model.C101S04W;
import com.mega.eloan.lms.model.C120S01Q;
import com.mega.eloan.lms.model.C160M01A;
import com.mega.eloan.lms.model.C160M02A;
import com.mega.eloan.lms.model.C160M03A;
import com.mega.eloan.lms.model.C160S01C;
import com.mega.eloan.lms.model.C160S01D;
import com.mega.eloan.lms.model.C160S01F;
import com.mega.eloan.lms.model.C160S03A;
import com.mega.eloan.lms.model.C340M01A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01R;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.sso.userdetails.MegaSSOUserDetails;

// UPGRADETODO: JXL 轉換為 Apache POI - 修改 import 宣告
// import jxl.write.WritableSheet;
// import jxl.write.WriteException;
import org.apache.poi.ss.usermodel.Sheet;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.model.GenericBean;

/**
 * <pre>
 * 動用審核表Service
 * </pre>
 * 
 * @since 2012/12/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/21,Fantasy,new
 *          <li>2013/06/26,Fantasy,add setDeleteTime
 *          </ul>
 */
public interface CLS1161Service extends CLS1160Service {

	/**
	 * 引進資料
	 * 
	 * @param params
	 */
	void importCase(PageParameters params) throws CapException;

	/**
	 * 引進資料
	 * 
	 * @param mainId
	 * @throws CapException
	 */
	void importCase(String mainId) throws CapException;

	/**
	 * 複製產品種類
	 * 
	 * @param model
	 */
	void copyC160S01C(C160S01C model) throws CapException;

	/**
	 * 刪除產品種類
	 * 
	 * @param model
	 */
	void deleteC160S01C(C160S01C model) throws CapException;

	/**
	 * 取得借保人清單
	 * 
	 * @param mainId
	 * @param refmainId
	 * @return
	 */
	List<GenericBean> findC160S01BList(String mainId, String refmainId)
			throws CapException;

	/**
	 * 檢查帳號
	 * 
	 * @param json
	 * @param id
	 * @return
	 */
	String checkAccount(JSONObject json, String column);

	/**
	 * saveModel
	 * 
	 * @param model
	 * @return
	 */
	String saveModel(GenericBean model);

	/**
	 * 取得流用序號
	 * 
	 * @param json
	 * @return
	 */
	String getChildrenSeq(JSONObject json) throws CapException;

	/**
	 * 取得共同行銷訊息
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	String getJoinMarkDesc(String custId, String dupNo);

	/**
	 * 流程
	 * 
	 * @param id
	 * @param target
	 * @throws CapMessageException
	 */
	void flowAction(String id, String target) throws CapMessageException;

	/**
	 * 取得
	 * 
	 * @param mainId
	 * @param staffNo
	 * @param custId
	 * @return
	 */
	C160S01D findByUniqueKey(String mainId, String staffNo, String custId);

	/**
	 * @param mainId
	 * @return
	 */
	List<C160S01D> getC160S01DList(String mainId);

	/**
	 * @param mainId
	 * @param all
	 * @return
	 */
	List<C160S01D> getC160S01DList(String mainId, boolean all);

	/**
	 * @param mainId
	 * @return
	 */
	int getC160S01DCount(String mainId);
	int getC160S01D_Success_Count(String mainId, String successMsg);
	/**
	 * 取得額度序號
	 * 
	 * @return
	 */
	String setCntrNo(C160S01D c160s01d);

	/**
	 * 取得檢附資訊檔明細檔的筆數
	 * 
	 * @param mainId
	 * @return
	 */
	int getC160M01CSize(String mainId, String itemCheck);

	boolean checkC160s01bRepeat(String mainId, String refmainId, String custId,
			String dupNo, String cntrNo, String rId, String rDupNo, String rType);

	/**
	 * 儲存
	 * 
	 * @param list
	 */
	void setDeleteTime(C160M01A model);

	/**
	 * 儲存
	 * 
	 * @param list
	 */
	void setDeleteTime(C160M02A model);

	/**
	 * 取得對應的產品種類檔資料
	 * 
	 * @param mainId
	 *            額度明細表mainId
	 * @param seq
	 *            序列號
	 * @return
	 */
	public L140S02A findL140S02A(String mainId, Integer seq);

	/**
	 * 刪除各項費用資料
	 * 
	 * @param l140m01r
	 *            L140M01R
	 */
	void deleteL140M01R(L140M01R l140m01r);

	/**
	 * 儲存各項費用資料
	 * 
	 * @param l140m01r
	 *            L140M01R
	 */
	void saveL140M01R(L140M01R l140m01r);

	/**
	 * 取得各項費用資料BY OID
	 * 
	 * @param oid
	 *            key
	 * @return L140M01R
	 */
	L140M01R getL140M01R(String oid);

	/**
	 * 依 Unique Index 去搜尋 C160S01F
	 */
	C160S01F findByUniqueKey(String mainId, Integer seq, String bankNo,
			String branchNo, String subACNo, String refmainId);

	public void setC160M01BApprovedAmt(String mainId, String refMainId,
			BigDecimal loanTotAmt) throws CapException;

	/**
	 * 刪除以該mainId的相關Table
	 * 
	 * @param c160m02a
	 */
	void deleteByC160M02A(C160M02A c160m02a);

	/**
	 * 中鋼整批滙入處理Excel
	 * 
	 * @param sheet
	 * @param c160m02a
	 * @return
	 */
	// UPGRADETODO: JXL 轉換為 Apache POI - 修改方法參數類型
	JSONObject parseExcel(Sheet sheet, C160M02A c160m02a);

	/**
	 * 中鋼整批轉檔用,找出jcic及etch近一個月查詢，且GrdCDate最大的C120S01Q
	 * @param branchNo
	 * @return
	 */
	public List<C120S01Q> findC120S01QByC160M02AJcicEtchIn1Month(String branchNo);

	/**
	 * 刪除C120S01B_C120S01E_C120S01Q By mainId<br/>
	 * 為了把 [信用卡張數] 上傳DW, 多增加處理 c120m01a 
	 * @param mainId
	 */
	public void deleteC120S01B_C120S01E_C120S01QByMainId(String mainId);

	/**
	 * 中鋼整批滙入查詢C120S01Q是否已被動審表引用
	 * 
	 * @param srcMainId
	 * @return List<C120S01Q> 
	 */
	public List<C120S01Q>  findC120S01QBySrcMainId(String srcMainId);
	
	// UPGRADETODO: JXL 轉換為 Apache POI - 修改方法參數類型
	public void updateExcel(Sheet sheet, C160M02A c160m02a);
	
	public List<C160S03A>  findC160S03AByMainIdOrderBySeqNo(String mainId);
	public int findC160M03AMaxPackNoByCntrNo(String cntrNo);
	public int findC160S03AMaxSeqNoByCntrNo(String cntrNo);
	// UPGRADETODO: JXL 轉換為 Apache POI - 修改方法參數類型
	public boolean parseC160S03A(Sheet sheet, C160M03A c160m03a, List<C160S03A> old_List, int maxSeq);
	public void upELF533(C160M03A meta, List<C160S03A> list);	
	
	/** 不透過 CLS1160Service，會rewrite updater */
	public void daoSaveC160M03A(C160M03A meta);
	
	public boolean check_can_delElf533(List<C160S03A> list);
	public void delElf533(List<C160S03A> list) throws CapException;

	public void export_Superficies(ByteArrayOutputStream outputStream, String custId, 
			Date procDateB, Date procDateE, String tableNm, String formId) throws IOException;

	public void sync_C160M01A_totAmt(String mainId);
	
	public boolean getProjClassFromL250M01A(String oid) ;
	
	public void saveIVRFlag(String oid, List<String> addIVRList) throws CapException;
	
	public void deleteIVRFlag(String oid, String custId, String fileName) throws CapException;
	
	public List<Map<String, Object>> getIVRgrid(String oid) throws CapException;
	public List<Map<String, Object>> getIVRFiltergrid(String oid) throws CapException;

	public boolean is_only_apply_prod69(String c160_mainId);
	
	/** 是否包含 檢附文件 47, 48, 49, 50等項目 */
	public String useProd69Fmt(String c160_mainId);
	
	//J-109-0150_10702_B1001 Web e-Loan IVR頁籤由模擬動審移至動審表
	public boolean getProjClassFromC160M01A(String oid) ;
	public void saveIVRFlag2(String oid, List<String> addIVRList) throws CapException;
	public void deleteIVRFlag2(String oid, String custId, String fileName) throws CapException;
	public List<Map<String, Object>> getIVRgrid2(String oid) throws CapException;
	public List<Map<String, Object>> getIVRFiltergrid2(String oid) throws CapException;
	public L140M01A findByMainId(String reMainId);
	public List<C101S04W> findC101S04WByMainid(String mainId,String custId);
	public C340M01A find_c340m01a_CtrTypeL_CtrStatus9(String l120m01aMainId);
	public C340M01A find_c340m01a_CtrTypeA_ploanCtrStatus9_orderBy_ploanCtrBegDateDesc(L140M01A l140m01a);
	
	public List<C160M02A> findC160m02aByCreateTimeAndImportStatus(String createTime,String importExcelStatus);
	/**
	 * 取得聯徵查詢結果比較資料MAP
	 * @param mainId C160M01A.mainId
	 * @return
	 */
	public List<Map<String, Object>> getJcicResultCompareMap(String mainId);

	public boolean isAddAllocateFundsCheckList(String mainId_c160s01b, boolean isCheckEjcicB29B33);

	public void checkBeforeEjcicInquiry(MegaSSOUserDetails user, String c101m01a_mainId, String c160m01a_mainId) throws CapMessageException;

	public void queryEjcicBy(String txId, String userId, String unitNo, String mainId, String custId, String dupNo) throws CapMessageException, ClientProtocolException, IOException;

	public void queryEjcicBeforeSendMoney(String userId, String unitNo, String mainId_c160m01a, String custId, String dupNo) throws CapMessageException, ClientProtocolException, IOException;

	public void setC160M02AExcelStatus(C160M02A c160m02a,String status);
	
	public Map<String, Object> getEjcicReusltRecordForPreMoneyAllocatingInquiry(String custId, String prodId, String qDate);

	public boolean isRunInQueryBeforeMoneyAllocating(String mainId_c160m01b);
}