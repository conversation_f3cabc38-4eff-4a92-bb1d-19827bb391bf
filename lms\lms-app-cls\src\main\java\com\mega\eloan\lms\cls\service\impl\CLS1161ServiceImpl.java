/* 
 * CLS1161ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.cls.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.StringWriter;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URL;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Properties;
import java.util.Set;
import java.util.TreeSet;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.http.client.ClientProtocolException;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.constants.SysParamConstants;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.gwclient.EJCICGwClient;
import com.mega.eloan.common.gwclient.EJCICGwReqMessage;
import com.mega.eloan.common.gwclient.IVRGwClient;
import com.mega.eloan.common.gwclient.IVRGwReqMessage;
import com.mega.eloan.common.gwclient.PLOAN005;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.ClsScoreUtil;
import com.mega.eloan.lms.base.common.ClsUtility;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.MISRows;
import com.mega.eloan.lms.base.constants.ContractDocConstants;
import com.mega.eloan.lms.base.constants.Score;
import com.mega.eloan.lms.base.constants.ScoreNotHouseLoan;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.LMS2501Service;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.RPAProcessService;
import com.mega.eloan.lms.base.service.ScoreService;
import com.mega.eloan.lms.base.service.impl.RPAProcessServiceImpl;
import com.mega.eloan.lms.cls.common.ClsUtil;
import com.mega.eloan.lms.cls.constants.ClsConstants;
import com.mega.eloan.lms.cls.pages.CLS1161S02APage;
import com.mega.eloan.lms.cls.report.CLS1131R04RptService;
import com.mega.eloan.lms.cls.report.CLS1131R05RptService;
import com.mega.eloan.lms.cls.report.CLS1131R06RptService;
import com.mega.eloan.lms.cls.service.CLS1161Service;
import com.mega.eloan.lms.cls.service.CLS3301Service;
import com.mega.eloan.lms.dao.C100M01Dao;
import com.mega.eloan.lms.dao.C101S01SDao;
import com.mega.eloan.lms.dao.C101S04WDao;
import com.mega.eloan.lms.dao.C120S01SDao;
import com.mega.eloan.lms.dao.C340M01ADao;
import com.mega.eloan.lms.dao.C340M01CDao;
import com.mega.eloan.lms.dao.L120S04ADao;
import com.mega.eloan.lms.dao.L120S04BDao;
import com.mega.eloan.lms.dao.L120S04CDao;
import com.mega.eloan.lms.dao.L140M01RDao;
import com.mega.eloan.lms.ejcic.service.EjcicService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.eloandb.service.EloandbcmsBASEService;
import com.mega.eloan.lms.mfaloan.bean.ELF533;
import com.mega.eloan.lms.mfaloan.bean.ELF600;
import com.mega.eloan.lms.mfaloan.bean.LNF916S;
import com.mega.eloan.lms.mfaloan.bean.LNF917S;
import com.mega.eloan.lms.mfaloan.bean.LNF919S;
import com.mega.eloan.lms.mfaloan.service.MISSEFService;
import com.mega.eloan.lms.mfaloan.service.MisELF600Service;
import com.mega.eloan.lms.mfaloan.service.MisELLNGTEEService;
import com.mega.eloan.lms.mfaloan.service.MisPTEAMAPPService;
import com.mega.eloan.lms.mfaloan.service.MisStoredProcService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C100M01;
import com.mega.eloan.lms.model.C101M01A;
import com.mega.eloan.lms.model.C101S01H;
import com.mega.eloan.lms.model.C101S01I;
import com.mega.eloan.lms.model.C101S01S;
import com.mega.eloan.lms.model.C101S01U;
import com.mega.eloan.lms.model.C101S04W;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C120S01C;
import com.mega.eloan.lms.model.C120S01D;
import com.mega.eloan.lms.model.C120S01E;
import com.mega.eloan.lms.model.C120S01H;
import com.mega.eloan.lms.model.C120S01I;
import com.mega.eloan.lms.model.C120S01Q;
import com.mega.eloan.lms.model.C120S01S;
import com.mega.eloan.lms.model.C120S02A;
import com.mega.eloan.lms.model.C160M01A;
import com.mega.eloan.lms.model.C160M01B;
import com.mega.eloan.lms.model.C160M01C;
import com.mega.eloan.lms.model.C160M02A;
import com.mega.eloan.lms.model.C160M03A;
import com.mega.eloan.lms.model.C160S01A;
import com.mega.eloan.lms.model.C160S01B;
import com.mega.eloan.lms.model.C160S01C;
import com.mega.eloan.lms.model.C160S01D;
import com.mega.eloan.lms.model.C160S01E;
import com.mega.eloan.lms.model.C160S01F;
import com.mega.eloan.lms.model.C160S02A;
import com.mega.eloan.lms.model.C160S03A;
import com.mega.eloan.lms.model.C340M01A;
import com.mega.eloan.lms.model.C340M01C;
import com.mega.eloan.lms.model.C900M01G;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01C;
import com.mega.eloan.lms.model.L120S04A;
import com.mega.eloan.lms.model.L120S04B;
import com.mega.eloan.lms.model.L120S04C;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01M;
import com.mega.eloan.lms.model.L140M01O;
import com.mega.eloan.lms.model.L140M01R;
import com.mega.eloan.lms.model.L140M03A;
import com.mega.eloan.lms.model.L140S01A;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.eloan.lms.model.L140S02C;
import com.mega.eloan.lms.model.L140S02D;
import com.mega.eloan.lms.model.L140S02E;
import com.mega.eloan.lms.model.L140S02G;
import com.mega.eloan.lms.model.L140S02H;
import com.mega.eloan.lms.model.L250M01A;
import com.mega.eloan.lms.model.L250M01B;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Workbook;
// import jxl.Cell;
// import jxl.CellType;
// import jxl.DateCell;
// import jxl.format.Colour;
// import jxl.write.Label;
// import jxl.write.WritableCellFormat;
// import jxl.write.WritableFont;
// import jxl.write.WritableSheet;
// import jxl.write.WriteException;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.common.Arithmetic;
import tw.com.jcs.common.CustidValidator;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;

/**
 * <pre>
 * 動用審核表ServiceImpl
 * </pre>
 * 
 * @since 2012/12/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/21,Fantasy,new
 *          </ul>
 */
@Service
@Qualifier("CLS1161Service")
public class CLS1161ServiceImpl extends CLS1160ServiceImpl implements
		CLS1161Service {

	private static final Logger logger = LoggerFactory
			.getLogger(CLS1161ServiceImpl.class);

	@Resource
	MisdbBASEService mis;

	@Resource
	MisStoredProcService msps;

	@Resource
	MisPTEAMAPPService mps;

	@Resource
	MISSEFService msefs;

	@Resource
	L140M01RDao l140m01rDao;

	@Resource
	ScoreService scoreService;

	@Resource
	C100M01Dao c100m01Dao;

	@Resource
	ICustomerService iCustomerService;
	
	@Resource
	MisELLNGTEEService misELLNGTEEService;
	
	@Resource
	EjcicService ejcicService;
	
	@Resource
	LMSService lmsService;
	
	@Resource
	MisELF600Service misELF600Service;
	
	@Resource
	EloandbBASEService eloandbBASEService;
	
	@Resource
	EloandbcmsBASEService eloandbcmsService;
	
	@Resource
	L120S04ADao l120s04aDao;
	
	@Resource
	L120S04BDao l120s04bDao;
	
	@Resource
	L120S04CDao l120s04cDao;
	
	@Resource
	C340M01ADao c340m01aDao;
	
	@Resource
	C340M01CDao c340m01cDao;
	
	@Resource
	C101S04WDao c101s04wDao;
	
	@Resource
	LMS2501Service lms2501Service;
	
	@Resource
	SysParameterService sysParameterService;
	
	@Resource
	CLS3301Service cls3301Service;
	
	@Resource
	CodeTypeService codeTypeService;
	
	@Resource
	private IVRGwClient ivrGwClient;
	
	@Resource
	EJCICGwClient ejcicClient;
	
	@Resource
	RPAProcessService rpaProcessService;
	
	@Resource
	C101S01SDao c101s01sDao;
	
	@Resource
	CLS1131R04RptService cls1131R04RptService;
	
	@Resource
	CodeTypeService codeService;
	
	@Resource
	CLS1131R05RptService cls1131R05RptService;
	
	@Resource
	CLS1131R06RptService cls1131R06RptService;
	
	@Resource
	C120S01SDao c120s01sDao;

	private static final int MAXLEN_C160S03A_MEMO = StrUtils.getEntityFileldLegth(C160S03A.class, "memo_s", 60);
	private static final String DRATE_NA_FMT = "N/A";
	private static final BigDecimal 一百 = new BigDecimal(100);
	private static final BigDecimal 萬元 = new BigDecimal(10000);
	private static final BigDecimal 仟元 = new BigDecimal(1000);
	
	private static final String[] Ejcic_TxId = new String[]{"B29", "B33", "B68"};
	
	@SuppressWarnings("unchecked")
	@Override
	public void importCase(String mainId) throws CapException {
		List<GenericBean> saveList = new ArrayList<GenericBean>();

		C160M01A c160m01a = findModelByMainId(C160M01A.class, mainId);
		String srcMainId = Util.trim(c160m01a.getSrcMainId());

		List<C160M01B> c160m01bList = (List<C160M01B>) findListByMainId(
				C160M01B.class, mainId);
		for (C160M01B c160m01b : c160m01bList) {
			String refMainId = Util.trim(c160m01b.getRefmainId());
			L140M01A l140m01a = findModelByMainId(L140M01A.class, refMainId);
			if (l140m01a != null) {
				// 取得相關明細資料
				// 在 getDetialData(...) 裡
				// 包含[C160S01A, C160S01B, C160S01C, C160S01E.class, C160S01F]
				// 沒有 C160M01B(=L140M01A)
				saveList.addAll(getDetialData(mainId, refMainId, srcMainId,
						l140m01a, c160m01a));
				
				//刪除關係戶於本行各項業務
				this.deleteEachOperationOfRelationshipAccount(c160m01b.getUid());
				
				// 以下針對 C160M01B(=L140M01A)
				// 是以 update 的方式處理
				DataParse.copy(l140m01a, c160m01b);
				c160m01b.setMainId(mainId);
				c160m01b.setUid(IDGenerator.getUUID());
				c160m01b.setRefmainId(refMainId);
				c160m01b.setLoanTotCurr(l140m01a.getCurrentApplyCurr());
				c160m01b.setLoanTotAmt(l140m01a.getCurrentApplyAmt());
				// J-110-0233_10702_B1001 Web e-Loan調整勞工紓困對保相關邏輯調整
				List<L140S02A> l140s02aList = l140s02aDao.findByMainId(refMainId);
				for (L140S02A l140s02a : l140s02aList) {
					if(CrsUtil.is_69(l140s02a.getProdKind())){
						L120M01C l120m01c = clsService.findL120M01C_refMainId(l140s02a.getMainId());
						if(l120m01c!=null){
							C340M01A c340m01a = c340m01aDao.findByCaseMainid(l120m01c.getMainId(), new String[]{ContractDocConstants.C340M01A_CtrType.Type_L});
							if(c340m01a!=null){
								C340M01C tmpc340m01c = c340m01cDao.findByMainIdItemType(c340m01a.getMainId(), ContractDocConstants.C340M01C_ItemType.TYPE_0);
								if(tmpc340m01c!=null){
									JSONObject c340m01c_json =JSONObject.fromObject(tmpc340m01c.getJsonData());
									String loanAmt = c340m01c_json.getString("loanAmt");
									BigDecimal BigDecimal_loanAmt =Util.parseBigDecimal(loanAmt);
									
									c160m01b.setLoanTotAmt(BigDecimal_loanAmt);
								}
							}
						}
					}
				}
				saveList.add(c160m01b);
				
				
			}
		}
		// 儲存
		save(saveList);
	}
	
	private void deleteEachOperationOfRelationshipAccount(String c160m01bUid){
		
		List<L120S04A> list = l120s04aDao.findByMainId(c160m01bUid);
		if (list != null) {
			// 如果有資料就刪除再引進
			l120s04aDao.delete(list);
		}

		List<L120S04B> listS04b = l120s04bDao.findByMainId(c160m01bUid);
		List<L120S04C> listS04c = l120s04cDao.findByMainId(c160m01bUid);
		if (listS04b != null) {
			// 有資料就刪除
			l120s04bDao.delete(listS04b);
		}
		
		if (listS04c != null) {
			// 有資料就刪除
			l120s04cDao.delete(listS04c);
		}
	}

	@Override
	public void importCase(PageParameters params) throws CapException {
		List<GenericBean> saveList = new ArrayList<GenericBean>();
		// 動審表主檔
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		C160M01A c160m01a = findModelByMainId(C160M01A.class, mainId);
		String caseMainId = "";
		if (c160m01a != null) {
			String caseType = Util.trim(c160m01a.getCaseType());
			if (UtilConstants.Usedoc.caseType2.一般.equals(caseType)) {
				String dataSrc = Util.trim(params.getString("dataSrc"));
				caseMainId = Util.trim(params.getString("caseMainId"));
				L120M01A l120m01a = findModelByMainId(L120M01A.class,
						caseMainId);
				if (l120m01a != null) {
					c160m01a.setCustId(l120m01a.getCustId());
					c160m01a.setDupNo(l120m01a.getDupNo());
					c160m01a.setCustName(l120m01a.getCustName());
					c160m01a.setCaseBrId(l120m01a.getCaseBrId());
					c160m01a.setCaseYear(l120m01a.getCaseYear());
					c160m01a.setCaseSeq(l120m01a.getCaseSeq());
					c160m01a.setCaseNo(l120m01a.getCaseNo());
					c160m01a.setCaseLvl(l120m01a.getCaseLvl());
					c160m01a.setCaseDate(l120m01a.getCaseDate());
					c160m01a.setSrcMainId(caseMainId);
					c160m01a.setDataSrc(dataSrc);
				}
			} else if (UtilConstants.Usedoc.caseType2.團貸.equals(caseType)) {
				c160m01a.setCustId(params.getString("CUSTID", ""));
				c160m01a.setDupNo(params.getString("DUPNO", ""));
				c160m01a.setCustName(params.getString("PROJECTNM", ""));
				c160m01a.setCaseBrId(params.getString("ISSUEBRNO", ""));
				c160m01a.setLoanMasterNo(params.getString("GRPCNTRNO", ""));
				c160m01a.setLoanPackNo(params.getString("AMTAPPNO", ""));
				c160m01a.setIssueBrNo(params.getString("ISSUEBRNO", ""));
				c160m01a.setChildrenId(params.getString("SUBCOMPID", ""));
				c160m01a.setChildrenName(params.getString("SUBCOMPNM", ""));
				c160m01a.setChildrenSeq(params.getString("MGRPCNTRNO", ""));

				c160m01a.setBuildName(params.getString("BUILDNAME", ""));

				int year = params.getAsInteger("YEAR", 0);
				c160m01a.setParentYear(year != 0 ? year : null);
			}
			saveList.add(c160m01a);
		}

		String[] needCol = new String[] { "isClearLand", "ctlType", "fstDate", "lstDate", "isChgStDate", 
											"cstDate", "cstReason", "adoptFg", "isChgRate", "rateAdd", 
											"custRoa", "relRoa", "roaBgnDate", "roaEndDate", "isLegal" };
		
		// 動審表額度明細
		String[] caseOids = params.getStringArray("caseOids");
		// UPGRADETODO: 修正額度明細表匯入時 caseOids 空值檢查問題
		// 原本程式碼沒有檢查 caseOids 是否為 null，可能導致 NullPointerException
		if (caseOids == null || caseOids.length == 0) {
			throw new CapMessageException("未接收到選擇的額度明細表資料，請重新選擇後再試", getClass());
		}
		for (String oid : caseOids) {
			L140M01A l140m01a = findModelByOid(L140M01A.class, oid);
			C160M01B c160m01b = new C160M01B();
			if (l140m01a != null) {
				String refMainId = Util.trim(l140m01a.getMainId());
				// 取得相關明細資料
				saveList.addAll(getDetialData(mainId, refMainId, caseMainId,
						l140m01a, c160m01a));
				// 取得動審表額度明細
				DataParse.copy(l140m01a, c160m01b);
				c160m01b.setOid(null);
				c160m01b.setMainId(mainId);
				c160m01b.setUid(IDGenerator.getUUID());
				c160m01b.setRefmainId(refMainId);
				c160m01b.setLoanTotCurr(l140m01a.getCurrentApplyCurr());
				c160m01b.setLoanTotAmt(l140m01a.getCurrentApplyAmt());
				// J-110-0233_10702_B1001 Web e-Loan調整勞工紓困對保相關邏輯調整
				List<L140S02A> l140s02aList = l140s02aDao.findByMainId(refMainId);
				for (L140S02A l140s02a : l140s02aList) {
					if(CrsUtil.is_69(l140s02a.getProdKind())){
						C340M01A c340m01a = c340m01aDao.findByCaseMainid(Util.trim(params.getString("caseMainId")), new String[]{ContractDocConstants.C340M01A_CtrType.Type_L});
						if(c340m01a!=null){
							C340M01C tmpc340m01c = c340m01cDao.findByMainIdItemType(c340m01a.getMainId(), ContractDocConstants.C340M01C_ItemType.TYPE_0);
							if(tmpc340m01c!=null){
								JSONObject c340m01c_json =JSONObject.fromObject(tmpc340m01c.getJsonData());
								String loanAmt = c340m01c_json.getString("loanAmt");
								BigDecimal BigDecimal_loanAmt =Util.parseBigDecimal(loanAmt);
								
								c160m01b.setLoanTotAmt(BigDecimal_loanAmt);
							}
							
						}
					}
				}
				
				//J-113-0435 動審表新增ploan傳過來之認證完成email欄位
				C340M01A c340m01a = clsService.findByCaseMainid_PloanCtrStatus9(Util.trim(params.getString("caseMainId")));
				if(c340m01a != null){
					C340M01C c340m01c_9 = clsService.findC340M01C(c340m01a.getMainId(), "9");
					if (c340m01c_9 != null) {
						JSONObject jsonData = c340m01c_9.getJsonData() == null ? new JSONObject()
							: JSONObject.fromObject(c340m01c_9.getJsonData());
						String confirmEmail = jsonData.optString("borrowerEmail");
						c160m01b.setConfirmEmail(confirmEmail);
					}
				}
				
				saveList.add(c160m01b);

				String caseType = Util.trim(c160m01a.getCaseType());
				if (UtilConstants.Usedoc.caseType2.團貸.equals(caseType)) {
					String cntrNo = Util.trim(l140m01a.getCntrNo());
					C900M01G c900m01gOld = c900m01gDao.findByCntrNo(cntrNo);
					if (c900m01gOld == null) {
						this.checkAndCreateC900M01G(l140m01a, null, c160m01a,
								"2", saveList);
					}
				}
			}
			
			//J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
			L140M01M l140m01m = lmsService.findModelByMainId(L140M01M.class, l140m01a.getMainId());
			if (l140m01m != null && Util.notEquals(Util.trim(l140m01m.getIsClearLand()), "")) {
				DataParse.copyBean(l140m01m, c160m01b, needCol);
			}
			// 簽報書沒有註記，改抓ELF600
			else{
				ELF600 elf600 = misELF600Service.findByContract(l140m01a.getCntrNo());
				String isClearLand = lmsService.isClearLandEffective(elf600);
				c160m01b.setIsClearLand(isClearLand);
				if (Util.equals(isClearLand, "Y")) {
					c160m01b.setCtlType(Util.trim(elf600.getElf600_ctltype()));
					c160m01b.setFstDate(elf600.getElf600_fstdate());
					c160m01b.setLstDate(elf600.getElf600_lstdate());

				} else {
					c160m01b.setCtlType("");
					c160m01b.setFstDate(null);
					c160m01b.setLstDate(null);
				}
			}
			
		}
		// 儲存
		save(saveList);
	}

	/**
	 * 取得額度明細表之相關資料
	 * 
	 * @param c160m01b
	 * @param l140m01a
	 * @throws CapException
	 */
	private List<GenericBean> getDetialData(String mainId, String refMainId,
			String srcMainId, L140M01A l140m01a, C160M01A c160m01a) throws CapException {
		List<GenericBean> result = new ArrayList<GenericBean>();
		// 擔保品資料檔
		List<L140M01O> l140m01oList = l140m01oDao.findByMainId(refMainId);
		if(l140m01oList != null && l140m01oList.size()>0){ //簽報書有件擔保品，走正常流程
			for (L140M01O l140m01o : l140m01oList) {
				C160S01A c160s01a = new C160S01A();
				DataParse.copy(l140m01o, c160s01a);
				c160s01a.setSetordStr(l140m01o.getSetOrdStr());
				if (Util.isEmpty(Util.trim(c160s01a.getCollNo()))) {
					c160s01a.setCollNo("");
				}
				if(true){
					//為避免 CLS180R03 在呈現 【放款值、估價或購值】有誤
					//比照 CLS1151ServiceImpl :: inculdeL140M01O(...) 重抓 cms 最新的資料
					//比照 CLS1151ServiceImpl :: getCmsSetData(...) 重抓 cms 最新的資料
					//畫面上有開放修改的欄位，可不重抓
					C100M01 c100m01 = c100m01Dao.findByOid(Util.trim(c160s01a.getCmsOid()));
					if(c100m01!=null){
						List<String> debugMsg = new ArrayList<String>();
						BigDecimal loanTwd = null;
						BigDecimal totalLoanAmt = null;
						BigDecimal inAmt = null;
						if(true){ //loanTwd
							/*
							 在 CLS1151ServiceImpl :: inculdeL140M01O(...) 裡，有一行 
							 	DataParse.copy(c100m01, l140m01o);
							*/					
							if (UtilConstants.CollTyp1.不動產.equals(c100m01.getCollTyp1())) {
								loanTwd = c100m01.getLoanTwd();
							}else{
								/*
								目前僅開放「不動產」才可在動審時，修改內容
								其它種類(例如股票)，列印時直接抓 c160s01a.cmsDesc(並未重新組字)
								若抓最新的 loanTwd, 但不能edit動審內容, 且 cmsDesc 仍是舊的
								⇒ 會造成不一致  ⇒ 僅針對 不動產 去抓 loanTwd
								*/
							}
						}
						if(true){ //totalLoanAmt
							totalLoanAmt = c100m01.getLoanAmt();
						}
						if(true){ //inAmt
							inAmt = c100m01.getTimeVal();
							if (!UtilConstants.CollTyp1.不動產.equals(c100m01.getCollTyp1())) {
								inAmt = c100m01.getAppAmt();
							}
						}
						//===============
						if(c160s01a.getLoanTwd()!=null && loanTwd!=null && c160s01a.getLoanTwd().compareTo(loanTwd)!=0){
							debugMsg.add("loanTwd[old:"+c160s01a.getLoanTwd()+", new:"+loanTwd+"]");
							c160s01a.setLoanTwd(loanTwd);
						}
						if(c160s01a.getTotalLoanAmt()!=null && totalLoanAmt!=null && c160s01a.getTotalLoanAmt().compareTo(totalLoanAmt)!=0){
							debugMsg.add("totalLoanAmt[old:"+c160s01a.getTotalLoanAmt()+", new:"+totalLoanAmt+"]");
							c160s01a.setTotalLoanAmt(totalLoanAmt);
						}
						if(c160s01a.getInAmt()!=null && inAmt!=null && c160s01a.getInAmt().compareTo(inAmt)!=0){
							debugMsg.add("inAmt[old:"+c160s01a.getInAmt()+", new:"+inAmt+"]");
							c160s01a.setInAmt(inAmt);
						}
						//===============
						if(Util.isEmpty(Util.trim(c160s01a.getCollNo())) && Util.isNotEmpty(Util.trim(c100m01.getCollNo()))){
							debugMsg.add("collNo[old:"+""+", new:"+c100m01.getCollNo()+"]");
							c160s01a.setCollNo(c100m01.getCollNo());
						}
						String prefix = "update_c160s01a_with_latestCMS"
							+"[cntrNo="+(l140m01a==null?"":l140m01a.getCntrNo())
							+",refMainId="+Util.trim(refMainId)+"]";
						logger.debug(prefix+":"+StringUtils.join(debugMsg, " , "));
					}
				}
				result.add(c160s01a);
			}
		}else{ //簽報書沒有設定擔保品，若該案件為勞工紓困，自動抓C100M01資料寫入C160S01A
			List<Map<String, Object>> l120m01aList = eloandbBASEService.findL120M01AByL140M01A(l140m01a.getMainId());
			String simplifyflag = "";
			if(l120m01aList != null && l120m01aList.size()>0){ //正常來說只會有一筆
				Map l120m01a = l120m01aList.get(0);
				simplifyflag = Util.trim(l120m01a.get("SIMPLIFYFLAG"));
			}
			if(UtilConstants.Casedoc.SimplifyFlag.勞工紓困.equals(simplifyflag)){
				MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
				List<Map<String, Object>> c100m01List = eloandbBASEService.getEloanCollateralFrom(c160m01a.getCustId(),c160m01a.getOwnBrId());
				if(c100m01List != null && c100m01List.size()>0){
					List<C160S01A> newc160s01as = new ArrayList<C160S01A>();
					String oids[] = new String[c100m01List.size()];
					for(int i=0;i<c100m01List.size();i++){
						Map oidmap = c100m01List.get(i);
						oids[i] = Util.trim(oidmap.get("OID"));
					}
					List<C100M01> c100m01s = c100m01Dao.findByOids(oids);
					int seqNo = 0;
					for (C100M01 c100m01 : c100m01s) {
						seqNo++;
						C160S01A c160s01a = new C160S01A();
						c160s01a = makeC160S01A(c100m01, seqNo, mainId, user, c160m01a, l140m01a);
						newc160s01as.add(c160s01a);	
					}
					c160s01aDao.save(newc160s01as);
				}
			}
		}
		

		// 取得 借款人國別
		Map<String, String> countryMap = getCountryMap(refMainId, srcMainId);

		// 個金借保人檔
		List<L140S01A> l140s01aList = l140s01aDao.findByMainId(refMainId);
		for (L140S01A l140s01a : l140s01aList) {
			C160S01B c160s01b = new C160S01B();
			DataParse.copy(l140s01a, c160s01b);
			DataParse.copy(l140m01a, c160s01b);
			String custId = Util.trim(l140s01a.getCustId());
			String dupNo = Util.trim(l140s01a.getDupNo());
			c160s01b.setRId(custId);
			c160s01b.setRDupNo(dupNo);
			c160s01b.setRName(l140s01a.getCustName());
			c160s01b.setRType(l140s01a.getCustPos());
			c160s01b.setRCountry(Util.trim(countryMap.get(custId + dupNo)));
			
			//J-107-0104 可因一般保證人資信佳，升等
			c160s01b.setRKindM(Util.trim(l140s01a.getRKindM())); 
			c160s01b.setRKindD(Util.trim(l140s01a.getRKindD())); 
			c160s01b.setReson(Util.trim(l140s01a.getReson()));
			c160s01b.setResonOther(Util.trim(l140s01a.getResonOther())); 
			
			result.add(c160s01b);
		}
		// 產品種類檔
		List<L140S02A> l140s02aList = l140s02aDao.findByMainId(refMainId);
		String error = new String();
		for (L140S02A l140s02a : l140s02aList) {
			if (Util.notEquals(Util.trim(l140s02a.getProperty()),
					UtilConstants.Cntrdoc.Property.取消)) {
				if (l140s02a.getLoanAmt().compareTo(BigDecimal.ZERO) == 1) {
					L140S02E l140s02e = l140s02a.getL140S02E();
					Integer seq = Util.parseInt(l140s02a.getSeq());
					C160S01C c160s01c = new C160S01C();
					c160s01c.setUiSeq(0);// 之後會由 L140S02A.uiSeq 來覆蓋
					DataParse.copy(l140s02a, c160s01c);
					// 2013-06-19,Rex,抓取流水號
					c160s01c.setCaseSeq(l140s02a.getSecNo());
					c160s01c.setDRateAdd(null);
					c160s01c.setDMonth1(6);
					c160s01c.setDRate1(10);
					c160s01c.setDMonth2(6);
					c160s01c.setDRate2(20);
					if(c160m01a!=null){
						if(Util.equals(ClsUtil.C160M01A_RPTID_V20190920, c160m01a.getRptId())){
							c160s01c.setDRateAdd(BigDecimal.ZERO); //計收延遲利息[不可]加碼
							c160s01c.setPenaltyMaxContTm(9); //每次違約狀態最高連續收取期數9期
						}else{
							
						}
					}
					c160s01c.setEfctBH(l140s02a.getSellBank());
					c160s01c.setYear(l140s02a.getLnYear());
					c160s01c.setMonth(l140s02a.getLnMonth());
					if(CrsUtil.is_67(l140s02a.getProdKind())){
						c160s01c.setAutoPay(UtilConstants.DEFAULT.是);
						c160s01c.setAutoRct(UtilConstants.DEFAULT.是);
						//=====
						c160s01c.setRctAMT(l140s02a.getRmRctAmt());
						c160s01c.setRmIntMax(l140s02a.getRmIntMax());
						//=====
						c160s01c.setCtrType(ContractDocConstants.C340M01A_CtrType.Type_4);
					}else if(CrsUtil.is_70(l140s02a.getProdKind())){
						c160s01c.setAutoPay(UtilConstants.DEFAULT.是); //以房養老-累積型 {自動扣帳：只能 Y}
						c160s01c.setAutoRct(UtilConstants.DEFAULT.是); //以房養老-累積型 {自動進帳：只能 Y}
						//=====
						c160s01c.setRctAMT(l140s02a.getRmRctAmt());
						c160s01c.setRmIntMax(l140s02a.getRmIntMax());
						//=====
						c160s01c.setCtrType(ContractDocConstants.C340M01A_CtrType.Type_4);
					}else{
						c160s01c.setAutoPay(UtilConstants.DEFAULT.否);
						c160s01c.setAutoRct(UtilConstants.DEFAULT.否);
						//=====
						//c160s01c.setRctAMT(...);
						c160s01c.setRmIntMax(null);
					}
					
					if(CrsUtil.is_69(l140s02a.getProdKind())){
						c160s01c.setCtrType(ContractDocConstants.C340M01A_CtrType.Type_3);//勞工紓困貸款，預設 契約書種類  =借款契約書（其他貸款）
					}
					
					c160s01c.setApprovedAmt(ClsUtil.decide_loanAmt(l140m01a, l140s02a));
					c160s01c.setLoanAmt(ClsUtil.decide_loanAmt(l140m01a, l140s02a));
					
					c160s01c.setPayType("00");
					if (!Util.isEmpty(l140s02e)) {
						if (Util.equals(l140s02e.getNowExtend(),
								UtilConstants.DEFAULT.是)) {
							c160s01c.setNowFrom(l140s02e.getNowFrom());
							c160s01c.setNowEnd(l140s02e.getNowEnd());
						}
					}
					c160s01c.setCntrNo(l140m01a.getCntrNo());
					if(true){
							C340M01A c340m01a = find_c340m01a_CtrTypeA_ploanCtrStatus9_orderBy_ploanCtrBegDateDesc(l140m01a);

							//J-113-0471 web eLoan消金動審表線上房貸增貸客戶自動帶入首次還款日欄位
							Boolean RIntWay = false;
							L140S02C l140s02c = clsService.findL140S02C(l140s02a.getMainId(), l140s02a.getSeq());
							if(l140s02c!=null){
								if (Util.equals(l140s02c.getRIntWay(), "6")) {// 期付金
									RIntWay = true;
								}
							}

							if(c340m01a!=null && c340m01a.getPloanCtrBegDate()!=null && LMSUtil.cmpDate(c340m01a.getPloanCtrBegDate(), ">=", CapDate.getCurrentTimestamp()) && RIntWay){
								//若有 PLOAN 線上對保的資料，優先帶入
								c160s01c.setLnStartDate(c340m01a.getPloanCtrBegDate());
								c160s01c.setLnEndDate(c340m01a.getPloanCtrEndDate());
								c160s01c.setPmt_1st_rt_dt(c340m01a.getPloanCtr1stRtDt());
								if(c340m01a.getPloanCtr1stRtDt()==null){//增加 PloanCtr1stRtDt 前的舊案
									try{
										C340M01C c340m01c = c340m01cDao.findByMainIdItemType(c340m01a.getMainId(), ContractDocConstants.C340M01C_ItemType.TYPE_9);
										if(c340m01c!=null){
											ObjectMapper objectMapper = new ObjectMapper();
											PLOAN005 ploan_obj = objectMapper.readValue(JSONObject.fromObject(c340m01c.getJsonData()).toString(), PLOAN005.class);

											c160s01c.setPmt_1st_rt_dt(CapDate.parseDate(Util.trim(ploan_obj.getFirstPaymentDate())));
										}
									}catch(Exception e){
										logger.error(StrUtils.getStackTrace(e));
									}
								}
								// J-111-0227 PLOAN開放撥款入他行帳號 及ACH還款 判斷此案件是否符合條件要寫入相關資訊
								if( Util.isNotEmpty(c340m01a.getBankAcctCode()) && Util.notEquals(c340m01a.getBankAcctCode(), "017" ) ){
									//BankAcctCode不為本行代碼即代表要撥款入他行
									c160s01c.setAutoPay(UtilConstants.DEFAULT.否);//自動扣帳
									c160s01c.setAutoRct(UtilConstants.DEFAULT.是);//自動進帳

									//設定撥款他行資訊
									c160s01c.setAppOtherBankNo(c340m01a.getBankAcctCode());//撥款他行銀行代碼
									c160s01c.setAppOtherBranchNo(c340m01a.getBankAcctBranchCode());//撥款他行分行代碼
									c160s01c.setAppOtherAccount(c340m01a.getBankAcctNo());//撥款他行帳號

									String otherBankNm=findOtherBankNm(c340m01a.getBankAcctCode());
									c160s01c.setAppOtherBankNm(otherBankNm);//撥款他行銀行名稱
									if(Util.isNotEmpty(c340m01a.getBankAcctBranchCode())){
										String otherBranchNm=findBranchNm(c340m01a.getBankAcctCode(),c340m01a.getBankAcctBranchCode());//撥款他行分行名稱
										c160s01c.setAppOtherBranchNm(Util.isNotEmpty(otherBranchNm) ? otherBranchNm : otherBankNm );//撥款他行分行名稱
									}else{
										c160s01c.setAppOtherBranchNm(otherBankNm);
									}

									//若有同意ACH還款
									c160s01c.setPloanIsNeedACH(c340m01a.getIsNeedACH());//線上簽約同意ACH扣款
									if(Util.equals(c340m01a.getIsNeedACH(), "Y")){//ACH
										c160s01c.setPayType("01");//01-ACH扣帳
										c160s01c.setAchBankNo(c340m01a.getBankACHAcctCode());
										c160s01c.setAchBranchNo(c340m01a.getBankACHAcctBranchCode());
										c160s01c.setAchAccount(c340m01a.getBankACHAcctNo());

										String achBankNm=findOtherBankNm(c340m01a.getBankACHAcctCode());
										c160s01c.setAchBankNm(achBankNm);
										if(Util.isNotEmpty(c340m01a.getBankACHAcctBranchCode())){
											String achBranchNm=findBranchNm(c340m01a.getBankACHAcctCode(),c340m01a.getBankACHAcctBranchCode());//扣款他行分行名稱
											c160s01c.setAchBranchNm(Util.isNotEmpty(achBranchNm) ? achBranchNm : achBankNm );
										}else{
											c160s01c.setAchBranchNm(achBankNm);
										}
										c160s01c.setAutoPay(UtilConstants.DEFAULT.是);//自動扣帳
									}
								}
							}
					}
					
					// J-110-0233_10702_B1001 Web e-Loan調整勞工紓困對保相關邏輯調整
					if(CrsUtil.is_69(l140s02a.getProdKind())){
						L120M01C l120m01c = clsService.findL120M01C_refMainId(l140s02a.getMainId());
						if(l120m01c!=null){
							String l120m01a_mainId = l120m01c.getMainId();
							C340M01A c340m01a = c340m01aDao.findByCaseMainid(l120m01a_mainId, new String[]{ContractDocConstants.C340M01A_CtrType.Type_L});
							if(c340m01a!=null){
								c160s01c.setAutoPay(UtilConstants.DEFAULT.是); //勞工紓困 {自動扣帳：只能 Y}
								c160s01c.setAutoRct(UtilConstants.DEFAULT.是); //勞工紓困 {自動進帳：只能 Y}
								if(c340m01a.getPloanCtrEndDate().compareTo(c340m01a.getPloanCtrBegDate())>0){
									if(Util.isNotEmpty(c340m01a.getPloanCtrBegDate())){
										c160s01c.setLnStartDate(c340m01a.getPloanCtrBegDate());
										c160s01c.setUseStartDate(c340m01a.getPloanCtrBegDate());
										c160s01c.setUseEndDate(CapDate.addMonth(c340m01a.getPloanCtrBegDate(),3));
										c160s01c.setRctDate(c340m01a.getPloanCtrBegDate());
									}
									if(Util.isNotEmpty(c340m01a.getPloanCtrEndDate())){
										c160s01c.setLnEndDate(c340m01a.getPloanCtrEndDate());
									}
								}
								C340M01C c340m01c = c340m01cDao.findByMainIdItemType(c340m01a.getMainId(), ContractDocConstants.C340M01C_ItemType.TYPE_9);
								if(c340m01c!=null){
									JSONObject c340m01c_json =JSONObject.fromObject(c340m01c.getJsonData());
									String loan_accNo = c340m01c_json.getString("bankAcctNo");
									c160s01c.setAccNo(loan_accNo);		//帶入對保契約書所選帳號
									c160s01c.setAtpayNo(loan_accNo);	//帶入對保契約書所選帳號
								}
								C340M01C tmpc340m01c = c340m01cDao.findByMainIdItemType(c340m01a.getMainId(), ContractDocConstants.C340M01C_ItemType.TYPE_0);
								if(tmpc340m01c!=null){
									JSONObject c340m01c_json =JSONObject.fromObject(tmpc340m01c.getJsonData());
									String loanAmt = c340m01c_json.getString("loanAmt");
									BigDecimal BigDecimal_loanAmt =Util.parseBigDecimal(loanAmt);
									
									c160s01c.setRctAMT(BigDecimal_loanAmt); 		//帶入對保契約書金額
									c160s01c.setApprovedAmt(BigDecimal_loanAmt);	//帶入對保契約書金額
									c160s01c.setLoanAmt(BigDecimal_loanAmt);		//帶入對保契約書金額
									
								}
								
							}
						}
					}

					//J-113-0139 eLoan配合升息自動更新動審表利率，並檢核利率與最新利率不一致跳提醒
					if (CrsUtil.is_71(l140s02a.getProdKind())) {
						List<L140S02D> l140s02ds = clsService.findL140S02D_orderByPhase(l140m01a.getMainId(), l140s02a.getSeq(), "Y");
						String rateDesc = c160s01c.getRateDesc();
						for (L140S02D l140s02d:  l140s02ds ) {
							if (Util.equals(Util.trim(l140s02d.getRateType()),CrsUtil.RATE_TYPE_6R)) {
								//更新基礎利率
								BigDecimal baseRate = clsService._get_latest_mis_MISLNRAT_currTWD(Util.trim(l140s02d.getRateType()));
								if (!Util.equals(baseRate,l140s02d.getBaseRate())) {
									BigDecimal nowRate = ClsUtility.calc_nowRate(l140s02d.getRateType(), l140s02d.getRateUserType(), l140s02d.getRateFlag(), l140s02d.getNowRate(), baseRate, l140s02d.getPmFlag(), l140s02d.getPmRate());
									rateDesc = rateDesc.replace(LMSUtil.pretty_numStr(l140s02d.getNowRate()),LMSUtil.pretty_numStr(nowRate));
								}
							}
						}
						c160s01c.setRateDesc(rateDesc);
					}

					result.add(c160s01c);

					// 代償轉貸借新還舊主檔
					if(true){
						L140S02G l140s02g = l140s02gDao.findByUniqueKey(refMainId,
								seq);
						if (l140s02g != null) {
							C160S01E c160s01e = new C160S01E();
							DataParse.copy(l140s02g, c160s01e);
							
							if (Util.isEmpty(Util.trim(c160s01e.getChgOther()))) {
								c160s01e.setChgOther(UtilConstants.DEFAULT.否);
							}
							result.add(c160s01e);
						}
					}
					// 代償轉貸借新還舊明細檔
					List<L140S02H> l140s02hList = l140s02hDao.findByMainIdSeq(
							refMainId, seq);
					for (L140S02H l140s02h : l140s02hList) {
						C160S01F c160s01f = new C160S01F();
						DataParse.copy(l140s02h, c160s01f);
						result.add(c160s01f);
					}
				}

			} else {
				error = error + "<br/>" + Util.trim(l140s02a.getCntrNo());
			}
		}
		// init
		for (GenericBean model : result) {
			model.set("oid", null);
			model.set("mainId", mainId);
			model.set("refmainId", refMainId);
		}

		return result;
	}
	
	private String findOtherBankNm(String bankCode){
		String otherBankNm="";
		Map<String, String> bankType = codeService.findByCodeType("lms1605s03_slBankType","zh_TW");
		for (Iterator<Map.Entry<String, String>> entries = bankType.entrySet().iterator(); entries.hasNext(); ){
			Map.Entry<String, String> entry = entries.next();
			String BankCodeType="BankCode"+entry.getKey();
			CodeType bankInfo=codeService.findByCodeTypeAndCodeValue(BankCodeType,bankCode,"zh_TW");
			if(bankInfo != null){
				otherBankNm=bankInfo.getCodeDesc();
				break;
			}
		}
		return otherBankNm;
	}
	
	private String findBranchNm(String bankCode,String bankBranchCode){
		String result="";
		List<Map<String, Object>> branchList=mis.findMISSynBank(bankCode);
		for(Map<String, Object> branchNm : branchList){
			if(Util.equals(branchNm.get("CODE"),bankBranchCode)){
				result =  branchNm.get("NAME").toString();//撥款他行分行名稱
				break;
			}
		}
		return result;
	}

	@Override
	public void copyC160S01C(C160S01C model) throws CapException {
		List<GenericBean> saveList = new ArrayList<GenericBean>();

		if (model != null) {
			String mainId = Util.trim(model.getMainId());
			String refmainId = Util.trim(model.getRefmainId());
			int seq = Util.parseInt(model.getSeq());
			int newSeq = getMaxSeq(mainId, refmainId);
			// 個金產品種類檔
			C160S01C newC160s01c = new C160S01C();
			DataParse.copy(model, newC160s01c);
			// 2013-06-19,Rex,修改抓取流水號，getSeq()改為getSecNo()
			newC160s01c.setCaseSeq(getMaxCaseSeq(mainId, refmainId));
			newC160s01c.setSeq(newSeq);
			newC160s01c.setUiSeq(0);
			saveList.add(newC160s01c);
			// 代償轉貸借新還舊主檔
			C160S01E c160s01e = model.getC160s01e();
			if (c160s01e != null) {
				C160S01E newC160s01e = new C160S01E();
				DataParse.copy(c160s01e, newC160s01e);
				newC160s01e.setSeq(newSeq);
				saveList.add(newC160s01e);
			}
			// 代償轉貸借新還舊明細檔
			List<C160S01F> list = c160s01fDao.findByIndex01(mainId, seq, null,
					null, null, refmainId);
			for (C160S01F c160s01f : list) {
				C160S01F newC160s01f = new C160S01F();
				DataParse.copy(c160s01f, newC160s01f);
				newC160s01f.setSeq(newSeq);
				saveList.add(newC160s01f);
			}
			// save
			save(saveList);
		}
	}

	private int getMaxSeq(String mainId, String refmainId) {
		int result = 0;
		ISearch search = c160s01cDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, Util.trim(mainId));
		search.addSearchModeParameters(SearchMode.EQUALS, "refmainId",
				Util.trim(refmainId));
		search.addOrderBy("seq", true);

		C160S01C model = c160s01cDao.findUniqueOrNone(search);
		if (model != null) {
			result = Util.parseInt(model.getSeq());
		}
		return result + 1;
	}

	private int getMaxCaseSeq(String mainId, String refmainId) {
		int result = 0;
		ISearch search = c160s01cDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, Util.trim(mainId));
		search.addSearchModeParameters(SearchMode.EQUALS, "refmainId",
				Util.trim(refmainId));
		search.addOrderBy("caseSeq", true);

		C160S01C model = c160s01cDao.findUniqueOrNone(search);
		if (model != null) {
			result = Util.parseInt(model.getCaseSeq());
		}
		return result + 1;
	}

	@Override
	public void deleteC160S01C(C160S01C model) throws CapException {
		List<GenericBean> deleteList = new ArrayList<GenericBean>();

		if (model != null) {
			// 個金產品種類檔
			deleteList.add(model);
			// 代償轉貸借新還舊主檔
			C160S01E c160s01e = model.getC160s01e();
			if (c160s01e != null)
				deleteList.add(c160s01e);
			// 代償轉貸借新還舊明細檔
			List<C160S01F> list = c160s01fDao.findByIndex01(
					Util.trim(model.getMainId()),
					Util.parseInt(model.getSeq()), null, null, null,
					Util.trim(model.getRefmainId()));
			deleteList.addAll(list);
			// delete
			delete(deleteList);
		}
	}

	@Override
	public List<GenericBean> findC160S01BList(String mainId, String refmainId)
			throws CapException {
		List<GenericBean> result = new ArrayList<GenericBean>();

		C160M01A c160m01a = findModelByMainId(C160M01A.class, mainId);
		String srcMainId = Util.trim(c160m01a.getSrcMainId());

		// 取得 借款人國別
		Map<String, String> countryMap = getCountryMap(refmainId, srcMainId);
		// Map<String, String> countryMap = getCountryMap(refmainId);
		L140M01A l140m01a = findModelByMainId(L140M01A.class, refmainId);
		if (l140m01a != null) {
			List<L140S01A> l140s01aList = l140s01aDao.findByMainId(refmainId);
			for (L140S01A l140s01a : l140s01aList) {
				C160S01B c160s01b = new C160S01B();
				DataParse.copy(l140s01a, c160s01b);
				DataParse.copy(l140m01a, c160s01b);
				c160s01b.setRName(l140s01a.getCustName());
				c160s01b.setOid(null);
				c160s01b.setMainId(mainId);
				c160s01b.setRefmainId(refmainId);
				String custId = Util.trim(l140s01a.getCustId());
				String dupNo = Util.trim(l140s01a.getDupNo());
				c160s01b.setRId(custId);
				c160s01b.setRDupNo(dupNo);
				c160s01b.setRName(l140s01a.getCustName());
				c160s01b.setRType(l140s01a.getCustPos());
				c160s01b.setRCountry(Util.trim(countryMap.get(custId + dupNo)));

				result.add(c160s01b);
			}
		}
		// 刪除已存在的借保人資料
		List<C160S01B> c160s01bList = c160s01bDao.findByMainIdRefMainId(mainId,
				refmainId);
		delete(c160s01bList);

		// save(result); //因UNIQUE INDEX,故此save會失敗
		return result;
	}

	/**
	 * // 取得 借款人國別
	 * 
	 * @param l140m01a_mainId
	 * @param c160m01a_srcMainId
	 * @return
	 */
	private Map<String, String> getCountryMap(String l140m01a_mainId, String c160m01a_srcMainId) {
		String l120m01a_mainId = c160m01a_srcMainId;
		if(Util.isEmpty(Util.trim(l120m01a_mainId))){
			L120M01C l120m01c = clsService.findL120M01C_refMainId(l140m01a_mainId);
			if(l120m01c!=null){
				l120m01a_mainId = l120m01c.getMainId();
			}
		}
		Map<String, String> result = new HashMap<String, String>();
		List<C120S01A> c120s01aList = c120s01aDao.findByMainId(l120m01a_mainId);
		for (C120S01A c120s01a : c120s01aList) {
			String custid = Util.trim(c120s01a.getCustId());
			String dupNo = Util.trim(c120s01a.getDupNo());
			String ntCode = Util.trim(c120s01a.getNtCode());
			result.put(custid + dupNo, ntCode);
		}
		return result;
	}

	@Override
	public String checkAccount(JSONObject json, String column) {
		StringBuilder sb = new StringBuilder();
		if (json != null) {
			boolean check = true;
			String account = Util.trim(json.get(column));
			if (Util.isNotEmpty(account)) {
				check = false;
				if (account.length() >= 11) {
					String mainId = Util.trim(json.get(EloanConstants.MAIN_ID));
					String refmainId = Util.trim(json.get("refmainId"));
					String custId = Util.trim(json.get("custId"));
					ISearch search = c160s01bDao.createSearchTemplete();
					search.addSearchModeParameters(SearchMode.EQUALS,
							EloanConstants.MAIN_ID, mainId);
					search.addSearchModeParameters(SearchMode.EQUALS,
							"refmainId", refmainId);
					// 相關身份
					if (column.equals("accNo")) {
						search.addSearchModeParameters(SearchMode.EQUALS,
								"rType", "C");
					}
					C160M01B c160m01b = c160m01bDao.findByMainIdRefMainId(
							mainId, refmainId);
					List<C160S01B> c160s01bList = c160s01bDao.find(search);
					Set<String> set = new HashSet<String>();
					set.add(custId);
					set.add(Util.trim(c160m01b.getCustId()));
					for (C160S01B c160s01b : c160s01bList) {
						String rId = Util.trim(c160s01b.getRId());
						if (!set.contains(rId))
							set.add(rId);
					}
					// get mis data
					String brno = account.substring(0, 3);
					String apcd = account.substring(3, 5);
					String seqno = account.substring(5, 11);
					List<Map<String, Object>> list = dwdbBaseService
							.findDW_IDDP_DPF_CustData_ByCustIdAccount(
									set.toArray(new String[] {}), brno, apcd,
									seqno);
					if (list != null) {
						if (list.size() > 0)
							check = true;
					}
				}
			}

			if (!check) {
				sb.append(EloanConstants.HTML_NEWLINE);
				sb.append(getI18nMsg("C160S01C." + column));
				sb.append("：").append(account);
				sb.append(EloanConstants.HTML_NEWLINE);
				sb.append(getI18nMsg("C160S01C.warn4"));
			}
		}

		return sb.toString();
	}

	Properties prop;

	/**
	 * @param key
	 * @return
	 */
	private String getI18nMsg(String key) {
		String result = null;
		if (prop == null)
			prop = MessageBundleScriptCreator
					.getComponentResource(CLS1161S02APage.class);
		if (prop != null) {
			result = prop.getProperty(Util.trim(key));
		}
		return Util.trim(result);
	}

	@Override
	public String saveModel(GenericBean model) {
		if (model instanceof C160S01D) {
			C160S01D c160s01d = ((C160S01D) model);
			if (Util.isEmpty(c160s01d.getCntrNo())) {
				Map<String, Object> map = msps.callLNSP0050(
						MegaSSOSecurityContext.getUnitNo(),
						TypCdEnum.DBU.getCode(), "0");
				if ("YES".equals(Util.trim(map.get("chkFlag")))) {
					c160s01d.setCntrNo(Util.trim(map.get("cntrNo")));
					save(c160s01d);
				} else {
					return Util.trim(map.get("errMsg"));
				}
			}
		}
		return "";
	}

	@Override
	public String getChildrenSeq(JSONObject json) throws CapException {
		String result = null;
		// 取得當年度
		String[] date = Util.trim(TWNDate.toTW(new Date())).split("/");
		String year = date[0];

		String custId = Util.trim(json.get("custId"));
		String dupNo = Util.trim(json.get("dupNo"));
		String approvedNo = Util.trim(json.get("approvedNo"));
		// Map<String, Object> map = mps.findByIdNoYear(custId, dupNo, year);
		if (Util.isNotEmpty(approvedNo)) {
			// if (map != null) {
			// String mNo = Util.trim(map.get("GRPCNTRNO"));
			String isUseChildren = Util.trim(json.get("isUseChildren"));
			// 是否流用旗下子公司
			if (UtilConstants.DEFAULT.是.equals(isUseChildren)) {
				String subcompId = Util.trim(json.get("childrenId"));
				String subcompnm = Util.trim(json.get("childrenName"));
				Map<String, Object> sMap = mps.findByMgrnoSubid(approvedNo,
						subcompId);
				if (sMap != null) {
					result = Util.trim(sMap.get("GRPCNTRNO"));
				} else {
					// 取得額度序號(判斷是否GRPCNTRNO已存在 add by fantasy 2013/04/25)
					boolean finish = false;
					int getCount = 0;
					while (!finish && ++getCount <= 5) {
						Map<String, Object> cntrMap = msps.callLNSP0050(
								UtilConstants.BankNo.授管處,
								TypCdEnum.DBU.getCode(), "0");
						logger.trace("cntrMap:" + cntrMap);
						if ("YES".equals(Util.trim(cntrMap.get("chkFlag")))) {
							result = Util.trim(cntrMap.get("cntrNo"));
							Map<String, Object> grpMap = mps
									.getDataByGrpCntrNo(result);
							if (grpMap == null)
								finish = true;
						} else {
							throw new CapException(Util.trim(cntrMap
									.get("errMsg")), this.getClass());
						}
					}

					if (finish) {
						mps.insertByGrpno(result, subcompId, subcompnm,
								approvedNo);
					} else {
						throw new CapException("新增流用序號失敗!", this.getClass());
					}

					/*
					 * Map<String, Object> cntrMap = msps.callLNSP0050(
					 * UtilConstants.BankNo.授管處, TypCdEnum.DBU.getCode(), "0");
					 * if ("YES".equals(Util.trim(cntrMap.get("chkFlag")))) {
					 * String sNo = Util.trim(cntrMap.get("cntrNo"));
					 * mps.insertByGrpno(sNo, subcompId, subcompnm, approvedNo);
					 * result = sNo; } else { throw new CapException(
					 * Util.trim(cntrMap.get("errMsg")), this.getClass()); }
					 */
				}
			}
		} else {

			StringBuilder sb = new StringBuilder(EloanConstants.HTML_NEWLINE);
			sb.append("年度：").append(year).append(EloanConstants.HTML_NEWLINE);
			sb.append("統編：").append(custId).append("&nbsp;").append(dupNo)
					.append(EloanConstants.HTML_NEWLINE);
			sb.append("團貸年度總額度檔尚未建檔!");
			// throw new CapException(sb.toString(), this.getClass());

			// throw new CapMessageException("請選擇本案核准之編號",
			// this.getClass());
			return result; // test
		}

		return result;
	}

	@Override
	public String getJoinMarkDesc(String custId, String dupNo) {
		StringBuffer result = new StringBuffer();

		// C160M01B.joinMarkDescYES=同意
		// C160M01B.joinMarkDescNO=不同意
		// C160M01B.joinMarkDesc01=共用姓名、地址基本資料。
		// C160M01B.joinMarkDesc02=共用姓名、地址以外之基本資料及帳務資料。

		Map<String, Object> map = msefs.getAll(custId, dupNo);
		if (map != null) {
			// UPGRADETODO: 簡化邏輯，直接執行預設行為
			result.append("【同意】共用基本資料").append("，");
			// UPGRADETODO: 簡化邏輯，直接執行預設行為
			result.append("【不同意】共用帳務資料").append("。");

			if (UtilConstants.DEFAULT.是
					.equals(Util.trim(map.get("SE_BAS_DATA")))) {
				result.append(getI18nMsg("C160M01B.joinMarkDescYES"))
						.append(getI18nMsg("C160M01B.joinMarkDesc01"))
						.append("，");
			} else {
				result.append(getI18nMsg("C160M01B.joinMarkDescNO"))
						.append(getI18nMsg("C160M01B.joinMarkDesc01"))
						.append("，");
			}
			if (UtilConstants.DEFAULT.是
					.equals(Util.trim(map.get("SE_ACC_DATA")))) {
				result.append(getI18nMsg("C160M01B.joinMarkDescYES"))
						.append(getI18nMsg("C160M01B.joinMarkDesc02"))
						.append("。");
			} else {
				result.append(getI18nMsg("C160M01B.joinMarkDescNO"))
						.append(getI18nMsg("C160M01B.joinMarkDesc02"))
						.append("。");
			}

		} else {
			result.append("0024交易無此客戶共同行銷維護資訊");
		}

		return result.toString();
	}

	@Override
	public void flowAction(String id, String target) throws CapMessageException {
		FlowInstance inst = flowService.createQuery().id(id).query();
		if (inst != null) {
			inst.setAttribute("result", Util.trim(target));
			inst.next();
			// 核准時上傳MIS
			if ("核准".equals(target) || "先行動用".equals(target)) {
				C160M01A c160m01a = findModelByOid(C160M01A.class, id);
				String caseType = Util.trim(c160m01a.getCaseType());
				if (UtilConstants.Usedoc.caseType2.一般.equals(caseType)
						|| UtilConstants.Usedoc.caseType2.團貸.equals(caseType))
					uploadMIS_DW(c160m01a);
				this.setL140M03A(c160m01a);
			}
		}
	}

	/**
	 * 當動審表核准時要回寫額度明細表 補充檔
	 * 
	 * 
	 * @param c160m01a
	 */
	private void setL140M03A(C160M01A c160m01a) {
		String mainid = c160m01a.getMainId();
		List<C160M01B> c160m01bs = c160m01bDao.findByMainId(mainid);
		ArrayList<String> mainIds = new ArrayList<String>();
		for (C160M01B c160m01b : c160m01bs) {
			mainIds.add(c160m01b.getRefmainId());
		}
		List<L140M03A> l140m03as = l140m03aDao.findByMainIds(mainIds);
		for (L140M03A l140m03a : l140m03as) {
			// 當還沒上傳過的才需要塞動用日期
			if (Util.isEmpty(l140m03a.getIsUseDate())) {
				l140m03a.setIsUse(UtilConstants.DEFAULT.是);
				l140m03a.setIsUseDate(new Date());
			}

		}
		l140m03aDao.save(l140m03as);
	}

	@Override
	public void setC160M01BApprovedAmt(String mainId, String refMainId,
			BigDecimal loanTotAmt) throws CapException {
		StringBuilder errMsg = new StringBuilder();
		BigDecimal bfLoanTotAmt = BigDecimal.ZERO;

		C160M01B c160m01b = c160m01bDao
				.findByMainIdRefMainId(mainId, refMainId);
		BigDecimal c160m01bAmt = c160m01b.getLoanTotAmt();
		if ("".equals(Util.trim(c160m01b.getBfLoanTotAmt()))) {
			c160m01b.setBfLoanTotAmt(c160m01bAmt);
			bfLoanTotAmt = c160m01bAmt;
		} else {
			bfLoanTotAmt = c160m01b.getBfLoanTotAmt();
		}

		if (loanTotAmt.compareTo(bfLoanTotAmt) > 0) {
			errMsg.append(getI18nMsg("errorMsg.012")); // errorMsg.012=調整後核准額度不可大於原始核准額度。
			throw new CapMessageException(errMsg.toString(), getClass());
		} else {
			c160m01b.setLoanTotAmt(loanTotAmt);
		}
		c160m01bDao.save(c160m01b);
	}

	@Override
	public C160S01D findByUniqueKey(String mainId, String staffNo, String custId) {
		C160S01D result = c160s01dDao.findByUniqueKey(mainId, staffNo, custId);
		return result == null ? new C160S01D() : result;
	}

	@Override
	public List<C160S01D> getC160S01DList(String mainId) {
		return getC160S01DList(mainId, false);
	}

	@Override
	public List<C160S01D> getC160S01DList(String mainId, boolean all) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		// search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo",
		// UtilConstants.Mark.SPACE);
		// search.addSearchModeParameters(SearchMode.NOT_EQUALS, "misflag",
		// UtilConstants.DEFAULT.是);
		search.addSearchModeParameters(SearchMode.IS_NULL, "misflag", null);

		if (all)
			search.setFirstResult(0).setMaxResults(Integer.MAX_VALUE);

		return c160s01dDao.find(search);
	}

	@Override
	public int getC160S01DCount(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);

		return c160s01dDao.count(search);
	}
	@Override
	public int getC160S01D_Success_Count(String mainId, String successMsg){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "result", successMsg);
		return c160s01dDao.count(search);
	}
	
	@Override
	public String setCntrNo(C160S01D c160s01d) {
		if (c160s01d != null) {
			if (Util.isEmpty(c160s01d.getCntrNo())) {
				Map<String, Object> map = msps.callLNSP0050(
						MegaSSOSecurityContext.getUnitNo(),
						TypCdEnum.DBU.getCode(), "0");
				if ("YES".equals(Util.trim(map.get("chkFlag")))) {
					c160s01d.setCntrNo(Util.trim(map.get("cntrNo")));
				} else {
					return Util.trim(map.get("errMsg"));
				}
			}
		}
		return "";
	}

	@Override
	public int getC160M01CSize(String mainId, String itemCheck) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "itemCheck",
				itemCheck);
		return c160m01cDao.count(search);
	}

	@Override
	public boolean checkC160s01bRepeat(String mainId, String refmainId,
			String custId, String dupNo, String cntrNo, String rId,
			String rDupNo, String rType) {
		C160S01B c160s01b = c160s01bDao.findByUniqueKey(mainId, refmainId,
				custId, dupNo, cntrNo, rId, rDupNo, rType);
		return c160s01b != null ? true : false;
	}

	@Override
	public void setDeleteTime(C160M01A model) {
		if (model != null) {
			model.setDeletedTime(CapDate.getCurrentTimestamp());
			c160m01aDao.save(model);
		}
	}

	@Override
	public void setDeleteTime(C160M02A model) {
		if (model != null) {
			model.setDeletedTime(CapDate.getCurrentTimestamp());
			c160m02aDao.save(model);
		}
	}

	@Override
	public L140S02A findL140S02A(String mainId, Integer seq) {
		return l140s02aDao.findByUniqueKey(mainId, seq);
	}

	@Override
	public void deleteL140M01R(L140M01R l140m01r) {
		l140m01rDao.delete(l140m01r);
	}

	@Override
	public void saveL140M01R(L140M01R l140m01r) {
		l140m01rDao.save(l140m01r);
	}

	@Override
	public L140M01R getL140M01R(String oid) {
		return l140m01rDao.find(oid);
	}

	@Override
	public C160S01F findByUniqueKey(String mainId, Integer seq, String bankNo,
			String branchNo, String subACNo, String refmainId) {
		return c160s01fDao.findByUniqueKey(mainId, seq, bankNo, branchNo,
				subACNo, refmainId);
	}

	@Override
	/**
	 * 刪除中鋼整批滙入連同C160M02A
	 */
	public void deleteByC160M02A(C160M02A c160m02a) {
		deleteC120ByMainId(c160m02a.getMainId());
		delete(c160m02a);
	}

	/**
	 * 刪除中鋼整批滙入
	 */
	public void deleteC120ByMainId(String mainId) {
		List<GenericBean> deleteList = new ArrayList<GenericBean>();
		List<C120M01A> c120m01aList = c120m01aDao.findByMainId(mainId);
		deleteList.addAll(c120m01aList);
		List<C120S01A> c120s01aList = c120s01aDao.findByMainId(mainId);
		deleteList.addAll(c120s01aList);
		List<C120S01B> c120s01bList = c120s01bDao.findByMainId(mainId);
		deleteList.addAll(c120s01bList);
		List<C120S01C> c120s01cList = c120s01cDao.findByMainId(mainId);
		deleteList.addAll(c120s01cList);
		List<C120S01D> c120s01dList = c120s01dDao.findByMainId(mainId);
		deleteList.addAll(c120s01dList);
		List<C120S01E> c120s01eList = c120s01eDao.findByMainId(mainId);
		deleteList.addAll(c120s01eList);
		List<C120S01H> c120s01hList = c120s01hDao.findByMainId(mainId);
		deleteList.addAll(c120s01hList);
		List<C120S01I> c120s01iList = c120s01iDao.findByMainId(mainId);
		deleteList.addAll(c120s01iList);
		List<C120S01Q> c120s01qList = c120s01qDao.findByMainId(mainId);
		deleteList.addAll(c120s01qList);
		List<C120S01S> c120s01sList = c120s01sDao.findByMainId(mainId);
		deleteList.addAll(c120s01sList);
		cls1131Service.deleteByJPQL(deleteList);
	}

	final String DATAFORMAT = "yyyy/MM/dd";

	// UPGRADETODO: JXL 轉換為 Apache POI - 新增輔助方法來安全取得 POI 儲存格內容
	/**
	 * 安全地取得 POI 儲存格內容，相當於 JXL 的 getContents() 方法
	 * 注意：JXL 的座標是 (column, row)，POI 的座標是 (row, column)
	 * @param sheet POI Sheet
	 * @param column 欄位索引 (對應 JXL 的第一個參數)
	 * @param row 列索引 (對應 JXL 的第二個參數)
	 * @return 儲存格內容字串，若儲存格不存在則回傳空字串
	 */
	private String getPOICellContents(Sheet sheet, int column, int row) {
		if (sheet == null) return "";
		
		Row poiRow = sheet.getRow(row);
		if (poiRow == null) return "";
		
		Cell cell = poiRow.getCell(column);
		if (cell == null) return "";
		
		switch (cell.getCellType()) {
			case STRING:
				return cell.getStringCellValue();
			case NUMERIC:
				if (DateUtil.isCellDateFormatted(cell)) {
					// 處理日期格式
					return new SimpleDateFormat("yyyy/MM/dd").format(cell.getDateCellValue());
				} else {
					// 處理數字格式，避免科學記號
					double numValue = cell.getNumericCellValue();
					if (numValue == (long) numValue) {
						return String.valueOf((long) numValue);
					} else {
						return String.valueOf(numValue);
					}
				}
			case BOOLEAN:
				return String.valueOf(cell.getBooleanCellValue());
			case FORMULA:
				try {
					return cell.getStringCellValue();
				} catch (Exception e) {
					return String.valueOf(cell.getNumericCellValue());
				}
			default:
				return "";
		}
	}
	
	/**
	 * 取得 POI Sheet 的最後使用列數，相當於 JXL 的 getRows() 方法
	 * @param sheet POI Sheet
	 * @return 最後使用列數 + 1
	 */
	private int getPOISheetRows(Sheet sheet) {
		if (sheet == null) return 0;
		return sheet.getLastRowNum() + 1;
	}

	@Override
	// UPGRADETODO: JXL 轉換為 Apache POI - 修改方法簽名使用 POI Sheet
	public JSONObject parseExcel(Sheet sheet, C160M02A c160m02a) {
		String version_Q = scoreService.get_Version_NotHouseLoan();
		if(Util.equals(version_Q, ClsScoreUtil.V3_0_NOT_HOUSE_LOAN) 
				|| Util.equals(version_Q, ClsScoreUtil.V3_1_NOT_HOUSE_LOAN)){
			return _parseExcel_since_Q_3_0(sheet, c160m02a);
		}else if(Util.equals(version_Q, ClsScoreUtil.V2_1_NOT_HOUSE_LOAN)
				|| Util.equals(version_Q, ClsScoreUtil.V2_0_NOT_HOUSE_LOAN)
				|| Util.equals(version_Q, ClsScoreUtil.V1_0_NOT_HOUSE_LOAN)){
			return _parseExcel_bef_Q_2_1(sheet, c160m02a);			
		}
		return _parseExcel_since_Q_3_0(sheet, c160m02a);
	}
	
	// UPGRADETODO: JXL 轉換為 Apache POI - 修改私有方法簽名使用 POI Sheet
	private JSONObject _parseExcel_since_Q_3_0(Sheet sheet, C160M02A c160m02a) {
		String mainId = c160m02a.getMainId();

		JSONObject json = new JSONObject();
		List<GenericBean> list = new ArrayList<GenericBean>();
		int rows = 0, hasFinalRateCount = 0;
		final int isOkColumn = 24;
		Set<String> idDupSet = new HashSet<String>();
		// UPGRADETODO: JXL 轉換為 Apache POI - 修改 Font 和顏色設定（已完成）
		Font ok_font = sheet.getWorkbook().createFont();
		Font fail_font = sheet.getWorkbook().createFont();
		ok_font.setFontName("Arial");
		ok_font.setFontHeightInPoints((short) 12);
		ok_font.setBold(true);
		ok_font.setColor(IndexedColors.BLUE.getIndex());
		
		fail_font.setFontName("Arial");
		fail_font.setFontHeightInPoints((short) 12);
		fail_font.setBold(true);
		fail_font.setColor(IndexedColors.RED.getIndex());
		Map<String, String> map_jobType1 = clsService.get_codeTypeWithOrder("jobType");
		Map<String, Map<String, String>> map_jobType1And2 = new HashMap<String, Map<String, String>>();
		for(String jobType1 : map_jobType1.keySet()){
			Map<String, String> map_jobType2 = clsService.get_codeTypeWithOrder("jobType"+jobType1);
			map_jobType1And2.put(jobType1, map_jobType2);
		}
		Map<String, String> map_jobTitle = clsService.get_codeTypeWithOrder("lms1205s01_jobTitle");
		
		// 學歷map
		Map<String, String> map_edu = clsService.get_codeTypeWithOrder("cls1131m01_edu");

		// UPGRADETODO: JXL 轉換為 Apache POI - 修正 getRows() 和 getCell().getContents() 調用
		for (int row = 1; row < getPOISheetRows(sheet); row++) {
			int column = 0;
			if (Util.isEmpty(Util.trim(getPOICellContents(sheet, 1, row)))) {
				break;
			}
			StringBuffer checkMsg = new StringBuffer();
			C120M01A c120m01a = new C120M01A();
			C120S01A c120s01a = new C120S01A();
			C120S01B c120s01b = new C120S01B();
			C120S01C c120s01c = new C120S01C();
			C120S01D c120s01d = new C120S01D();
			C120S01E c120s01e = new C120S01E();
			List<C120S01H> c120s01h_list = new ArrayList<C120S01H>();
			List<C120S01I> c120s01i_list = new ArrayList<C120S01I>();
			C120S01Q c120s01q = new C120S01Q();
			c120m01a.setMainId(mainId);
			String staffNo = Util.trim(getPOICellContents(sheet, column++, row));
			if (Util.isEmpty(staffNo)) {
				checkMsg.append("員工編號不可空白!!");
			}
			c120m01a.setStaffNo(staffNo);
			String idDup = Util.trim(getPOICellContents(sheet, column++, row));
			String custId = StringUtils.substring(idDup, 0, 10); 
			String dupNo = StringUtils.substring(idDup, 10);			
			if (Util.isEmpty(custId)) {
				checkMsg.append("借款人身分證字號不可空白!!");
			} else if ( idDup.length()!=11) {
				checkMsg.append("身分證字號["+idDup+"]長度應為11碼");
				if(true){
					setMsgAtColumn(sheet, row, isOkColumn, checkMsg, ok_font, fail_font);							
				}
				continue;
			} else if (!CustidValidator.verifyID(custId)) {
				checkMsg.append("非正確身分證字號["+idDup+"]!!");
			}
			
			if(true){
				//若 idDup 重複, 會造成 mainId, custId, dupNo 的 key值重複, 拋出SQL Exception
				if(idDupSet.contains(idDup)){
					checkMsg.append("身分證字號["+idDup+"]不可重複!");
					if(true){
						setMsgAtColumn(sheet, row, isOkColumn, checkMsg, ok_font, fail_font);							
					}
					continue;
				}else{
					idDupSet.add(idDup);
				}
			}
			c120m01a.setCustId(custId);
			c120m01a.setDupNo(dupNo);
			c120m01a.setOwnBrId(c160m02a.getOwnBrId());
			c120m01a.setTypCd(c160m02a.getTypCd());
			String custName = Util.trim(getPOICellContents(sheet, column++, row));
			if (Util.isEmpty(custName)) {
				checkMsg.append("借款人姓名不可空白!!");
			}
			c120m01a.setCustName(custName);

			try {
				CapBeanUtil.copyBean(c120m01a, c120s01a, new String[] {
						"mainId", "custId", "dupNo" });
				CapBeanUtil.copyBean(c120m01a, c120s01b, new String[] {
						"mainId", "custId", "dupNo" });
				CapBeanUtil.copyBean(c120m01a, c120s01c, new String[] {
						"mainId", "custId", "dupNo" });
				CapBeanUtil.copyBean(c120m01a, c120s01d, new String[] {
						"mainId", "custId", "dupNo" });
				CapBeanUtil.copyBean(c120m01a, c120s01e, new String[] {
						"mainId", "custId", "dupNo" });
				CapBeanUtil.copyBean(c120m01a, c120s01q, new String[] {
						"mainId", "custId", "dupNo", "custName", "ownBrId" });
			} catch (CapException e) {
				logger.error(e.getMessage(), e);
			}
			// 年薪 單位:萬元
			c120s01b.setPayCurr("TWD");
			String payAmt = Util.trim(getPOICellContents(sheet, column++, row));
			if (Util.isNumeric(payAmt)) {
				 //XXX 四捨五入到整數位, 再去跑模型評等
				 //避免用 60.03萬去跑模型 → 模型得分 0
				 //但上傳DW卻是60萬               → 模型得分 -5.7682
				c120s01b.setPayAmt(Arithmetic.round(CapMath.getBigDecimal(payAmt), 0));
			} else {
				checkMsg.append("請檢查年薪需為數字!!");
			}
			// 其它所得 單位:萬元
			c120s01b.setOthCurr("TWD");
			String othAmt = Util.trim(getPOICellContents(sheet, column++, row));
			if (Util.isNumeric(othAmt)) {
				c120s01b.setOthAmt(Arithmetic.round(CapMath.getBigDecimal(othAmt), 0));
			} else {
				checkMsg.append("請檢查其它所得需為數字!!");
			}
			// 年資
			String seniority = Util.trim(getPOICellContents(sheet, column++, row));
			if (Util.isNumeric(seniority)) {
				c120s01b.setSeniority(CrsUtil.parseBigDecimal(seniority));
			} else {
				checkMsg.append("請檢查年資需為數字!!");
			}
			// 個人負債比
			String str_dRate = Util.trim(getPOICellContents(sheet, column++, row));
			if(Util.equals(DRATE_NA_FMT, str_dRate)){
				c120s01c.setDRate(null);
			}else{ 
				if(Util.isNumeric(str_dRate)) {			
					c120s01c.setDRate(CrsUtil.parseBigDecimal(str_dRate));
				}else{
					checkMsg.append("請檢查個人負債比需為數字或"+DRATE_NA_FMT+"!!");
				}
			}
			// 退票
			String keyin_1a = Util.trim(getPOICellContents(sheet, column++, row));			
			// 拒往
			String keyin_1b = Util.trim(getPOICellContents(sheet, column++, row));			
			// 票信查詢日期(自行輸入)
			// 在動審表上傳的 excel 其日期相關 cell「儲存格格式」是文字
			Date keyin_etchDate = CapDate.parseDate(Util.trim(getPOICellContents(sheet, column++, row)));			
			if (keyin_etchDate!=null) {
				if(Util.isEmpty(keyin_1a)){
					checkMsg.append("自行輸入票信查詢日期時，需自行輸入「退票」。");	
				}
				if(Util.isEmpty(keyin_1b)){
					checkMsg.append("自行輸入票信查詢日期時，需自行輸入「拒往」。");	
				}
			}
			
			// 學歷 - 配合「非房貸申請模型」改版，加上"學歷"
			String edu = Util.trim(getPOICellContents(sheet, column++, row));
			
			if(!map_edu.containsValue(edu)){
				checkMsg.append("請確認學歷!!");
			}else{
				for(Entry<String, String> entry : map_edu.entrySet()){
					if (Util.equals(edu, entry.getValue())) {
			            c120s01a.setEdu(entry.getKey());
			        }
				}
			}
					
			String lnf010_job_class = Util.trim(getPOICellContents(sheet, column++, row));
			String lnf010_job_class_desc = "";
			if(lnf010_job_class.length()==4){
				String jobType1 = StringUtils.substring(lnf010_job_class, 0, 2);
				String jobType2 = StringUtils.substring(lnf010_job_class, 2, 3);
				String jobTitle = StringUtils.substring(lnf010_job_class, 3, 4);
				String jobType1_desc = "";
				String jobType2_desc = "";
				String jobTitle_desc = "";
				boolean pass_jobType1 = false;
				boolean pass_jobType2 = false;
				boolean pass_jobTitle = false;
				
				if(map_jobType1And2.containsKey(jobType1)){
					pass_jobType1 = true;
					jobType1_desc = map_jobType1.get(jobType1);
					//==========
					Map<String, String> map_jobType2 = map_jobType1And2.get(jobType1);
					if(map_jobType2.containsKey(jobType2)){
						pass_jobType2 = true;
						jobType2_desc = map_jobType2.get(jobType2);
					}
				}
				
				if(map_jobTitle.containsKey(jobTitle)){
					pass_jobTitle = true;
					jobTitle_desc = map_jobTitle.get(jobTitle);
				}
				//=========================
				if(pass_jobType1 && pass_jobType2 && pass_jobTitle){
					c120s01b.setJobType1(jobType1);
					c120s01b.setJobType2(jobType2);
					c120s01b.setJobTitle(jobTitle);
					
					lnf010_job_class_desc = jobType1_desc+"_"+jobType2_desc+"_"+jobTitle_desc;
				}else{
					if(!pass_jobType1){
						checkMsg.append("職業大類"+jobType1+"不存在。");
					}
					if(pass_jobType1 && !pass_jobType2){
						checkMsg.append("職業大類"+jobType1+"內的職業小類"+jobType2+"不存在。");
					}
					if(!pass_jobTitle){
						checkMsg.append("職稱"+jobTitle+"不存在。");
					}
				}
			}else{
				checkMsg.append("職業大小類及職稱代號長度固定4碼。");	
			}
			
			try {
				if(true){
					int jobType1And2AndTitleColumn = column++;
					//~~~~~~~~~~~~~~
					// UPGRADETODO: JXL 轉換為 Apache POI - 需要改寫為 POI 寫入方式
					// Label valLabel = new Label(jobType1And2AndTitleColumn, row, lnf010_job_class_desc );
					// valLabel.setCellFormat(ClsUtil.getCellFormat(sheet.getCell(jobType1And2AndTitleColumn, row)));
					// sheet.addCell(valLabel);
				}
				
				// 取得JCIC和ETCH資料
				String version_G = scoreService.get_Version_HouseLoan();
				String version_Q = scoreService
						.get_Version_NotHouseLoan();
				String version_R = scoreService
						.get_Version_CardLoan();
				JSONObject data = new JSONObject();
				data.putAll(scoreService.getData(c120s01e.getCustId(),
						c120s01e.getDupNo(), version_G, version_Q, version_R, data));
				
				if (Util.isEmpty(data.get(Score.column.聯徵查詢日期))) {
					checkMsg.append("查無聯徵資料!!");
				}
				if (Util.isEmpty(data.get(Score.column.票信查詢日期))) {
					if (keyin_etchDate!=null && Util.isNotEmpty(keyin_1a) && Util.isNotEmpty(keyin_1b)) {
						boolean b_1a = Util.equals("Y", keyin_1a);
						boolean b_1b = Util.equals("Y", keyin_1b);
						
						data.put(Score.column.退票, b_1a?UtilConstants.haveNo.有:UtilConstants.haveNo.無);
						data.put(Score.column.拒往, b_1b?UtilConstants.haveNo.有:UtilConstants.haveNo.無);
						//XXX 用此欄位來區分「票信的結果」是否採用 user 輸入的資料
						data.put(Score.column.票信資料日期, CapDate.ZERO_DATE);
						data.put(Score.column.票信查詢日期, Util.toAD(keyin_etchDate));
						if(true){
							String orgVal = Util.trim(data.optString(Score.column.有退票_拒往_信用卡強停或催收呆帳紀錄));
							String newVal = orgVal;
							if(b_1a||b_1b){
								newVal = UtilConstants.haveNo.有;
							}else{
								if(Util.equals(UtilConstants.haveNo.NA, orgVal)){
									newVal = UtilConstants.haveNo.無;
								}
							}
							data.put(Score.column.有退票_拒往_信用卡強停或催收呆帳紀錄, newVal);
						}
						
					}else{
						checkMsg.append("查無票信資料!!");	
					}					
				}
				// 無錯誤訊息
				if (checkMsg.length() == 0) {
					String prodId = Util.trim(data.get(ClsScoreUtil.PRODID_KEY));
					data.put(ClsConstants.C101S01E.查詢組合, prodId);
					data.put(ClsConstants.C101S01E.聯徵資料日期,
							Util.trim(data.get(Score.column.聯徵資料日期)));
					data.put(ClsConstants.C101S01E.聯徵查詢日期,
							Util.trim(data.get(Score.column.聯徵查詢日期)));
					data.put(ClsConstants.C101S01E.票信資料截止日,
							Util.trim(data.get(Score.column.票信資料日期)));
					data.put(ClsConstants.C101S01E.票信查詢日期,
							Util.trim(data.get(Score.column.票信查詢日期)));
					DataParse.map2Bean(data, c120s01e);

					// 取得聯徵票信HTML內容C120S01H,C120S01I
					if(true){
						for(GenericBean gb : cls1131Service.getHtml(c120s01e.getMainId(),
								c120s01e.getCustId(), c120s01e.getDupNo(), prodId)){
							if(gb instanceof C101S01H){
								C120S01H c120s01h = new C120S01H();
								CapBeanUtil.copyBean(c120m01a, c120s01h, new String[] {
										"mainId", "custId", "dupNo" });								
								DataParse.copy(gb, c120s01h);
								c120s01h_list.add(c120s01h);
							}else if(gb instanceof C101S01I){
								C120S01I c120s01i = new C120S01I();
								CapBeanUtil.copyBean(c120m01a, c120s01i, new String[] {
										"mainId", "custId", "dupNo" });
								DataParse.copy(gb, c120s01i);
								c120s01i_list.add(c120s01i);
							}
						} 	
					}
					if(true){
						c120m01a.setJcicFlg(Util.trim(data.optString(ClsUtility.C101M01A_JCICFLG)));
						
						c120m01a.setPrimary_card(LMSUtil.fetch_BigDecimal_from_json(data, ClsUtility.C101M01A_PRIMARY_CARD));
						c120m01a.setAdditional_card(LMSUtil.fetch_BigDecimal_from_json(data, ClsUtility.C101M01A_ADDITIONAL_CARD));
						c120m01a.setBusiness_or_p_card(LMSUtil.fetch_BigDecimal_from_json(data, ClsUtility.C101M01A_BUSINESS_OR_P_CARD));
					}
					if(true){ // 非房貸3.0評分，將 xls 填入因子，帶到評分的input 參數
						ClsScoreUtil.set_c101s01q_factor_V3_0_c101s01b(data, DataParse.toJSON(c120s01b));
						ClsScoreUtil.set_c101s01q_factor_V3_0_c101s01c(data, DataParse.toJSON(c120s01c));						
					}
					
					JSONObject score_NotHouseLoan = new JSONObject();
					if(true){// 非房貸
						score_NotHouseLoan.putAll(data);
						//計算 score、評等、DR
						score_NotHouseLoan.putAll(scoreService.scoreNotHouseLoan(
								ScoreNotHouseLoan.type.非房貸基本, data, version_Q));
					}				
					if (Util.isNotEmpty(score_NotHouseLoan.get(ScoreNotHouseLoan.column.最終評等))) {
						// 設定非房貸個人評等模型
						c120m01a.setMarkModel(UtilConstants.L140S02AModelKind.非房貸);
						score_NotHouseLoan.put(ScoreNotHouseLoan.column.引用_非房貸, "Y");
						// 有最終評等
						hasFinalRateCount++;
					}
					DataParse.toBean(score_NotHouseLoan, c120s01q);

					
					if(true){
						// J-107-0130 增加 KRM040 彙總資料
						if(true){							
							List<Object> krm040_sumArr = _krm040_sumArr(custId, dupNo, prodId, c120s01q.getJcicQDate());
							for(int i=0;i<7;i++){
								boolean numericFmt = false;
								if(i==4){
									Object obj = krm040_sumArr.get(i);
									if(Util.equals(obj, "")){
										
									}else if(obj instanceof Number){
										numericFmt = true;
									}									
								}
								if(numericFmt){
									// UPGRADETODO: JXL 轉換為 Apache POI - 需要改寫為 POI 寫入方式
									// sheet.addCell(new jxl.write.Number(column, row, ((BigDecimal)krm040_sumArr.get(i)).doubleValue() , ClsUtil.getCellFormat(sheet.getCell(column++, row))));
									column++; // 跳過該欄位
								}else{
									// UPGRADETODO: JXL 轉換為 Apache POI - 需要改寫為 POI 寫入方式
									// Label valLabel = new Label(column, row, String.valueOf(krm040_sumArr.get(i) ));
									// valLabel.setCellFormat(ClsUtil.getCellFormat(sheet.getCell(column++, row)));
									// sheet.addCell(valLabel);
									column++; // 跳過該欄位
								}									
							}							
						}						
					}
					// record message
					// 非房貸模型最終評等
					// UPGRADETODO: JXL 轉換為 Apache POI - 需要改寫為 POI 寫入方式
					/*
					Label finalRateLab = new Label(column, row,
							Util.trim(c120s01q.getGrade3()));
					finalRateLab.setCellFormat(ClsUtil.getCellFormat(sheet
							.getCell(column++, row)));
					sheet.addCell(finalRateLab);
					*/
					column++; // 跳過該欄位

					// 票交所與聯徵負面資訊					
					// UPGRADETODO: JXL 轉換為 Apache POI - 需要改寫為 POI 寫入方式
					/*
					Label negativeInfoLab = new Label(column, row, ClsUtil.c120s01q_negativeInfo(c120s01q));
					negativeInfoLab.setCellFormat(ClsUtil.getCellFormat(sheet
							.getCell(column++, row)));
					sheet.addCell(negativeInfoLab);
					*/
					column++; // 跳過該欄位

					// 聯徵查詢日期
					String jcicDate = CapDate.formatDate(
							c120s01q.getJcicQDate(), DATAFORMAT);
					// UPGRADETODO: JXL 轉換為 Apache POI - 需要改寫為 POI 寫入方式
					/*
					Label jcicDateLab = new Label(column, row, jcicDate);
					jcicDateLab.setCellFormat(ClsUtil.getCellFormat(sheet
							.getCell(column++, row)));
					sheet.addCell(jcicDateLab);
					*/
					column++; // 跳過該欄位

					// 票信查詢日期
					String etchDate = CapDate.formatDate(
							c120s01q.getEtchQDate(), DATAFORMAT);
					// UPGRADETODO: JXL 轉換為 Apache POI - 需要改寫為 POI 寫入方式
					/*
					Label etchDateLab = new Label(column, row,
							etchDate.toString());
					etchDateLab.setCellFormat(ClsUtil.getCellFormat(sheet
							.getCell(column++, row)));
					sheet.addCell(etchDateLab);
					*/
					column++; // 跳過該欄位
				}
				if(true){
					setMsgAtColumn(sheet, row, isOkColumn, checkMsg, ok_font, fail_font);
					c120m01a.setCustNo(Util.addZeroWithValue(row, 6));						
				}				

				list.add(c120m01a);
				list.add(c120s01a);
				list.add(c120s01b);
				list.add(c120s01c);
				list.add(c120s01d);
				list.add(c120s01e);
				if(c120s01h_list.size()>0){
					list.addAll(c120s01h_list);
				}
				if(c120s01i_list.size()>0){
					list.addAll(c120s01i_list);
				}
				list.add(c120s01q);
			// UPGRADETODO: JXL 轉換為 Apache POI - WriteException 不再需要
			// } catch (WriteException e) {
			//	logger.error(e.getMessage(), e);
			} catch (CapException e) {
			logger.error(e.getMessage(), e);
			}
			rows++;
		} // end for-loop

		c160m02a.setFinCount(hasFinalRateCount);
		c160m02a.setTotCount(rows);
//		c160m02a.setDeletedTime(null);
		list.add(c160m02a);
		// 滙入前先刪除舊案
		deleteC120ByMainId(mainId);
		cls1131Service.save(list);
		return json;
	}
	
	// UPGRADETODO: JXL 轉換為 Apache POI - 修改方法參數從 WritableSheet 改為 POI Sheet
	@Override
	public void updateExcel(Sheet sheet, C160M02A c160m02a){
		String mainId = c160m02a.getMainId();
		Set<String> idDupSet = new HashSet<String>();
		int rows = 0;
		// (徵信整批匯入回寫外部系統查詢結果)
		// 如果有以下情境這邊要跟著調整
		// 1.外部系統有停用，這會讓外部系統資料筆數對不上
		// 2.EXCEL欄位有調整，這可能會造成資料寫錯欄
		// UPGRADETODO: JXL 轉換為 Apache POI - 修正 getRows() 和 getCell().getContents() 調用
		for (int row = 1; row < getPOISheetRows(sheet); row++) {
			String idDup = Util.trim(getPOICellContents(sheet, 1, row));
			if (Util.isEmpty(idDup)) {
				break;
			}
			
			if ( idDup.length()!= 11) {//比照信評計算時判斷標準 長度不符不處理
				continue;
			}
			
			if(true){
				//比照信評計算時判斷標準 ( idDup 重複就跳過 )
				if(idDupSet.contains(idDup)){
					continue;
				}else{
					idDupSet.add(idDup);
				}
			}

			String custId = StringUtils.substring(idDup, 0, 10); 
			String dupNo = StringUtils.substring(idDup, 10);	
			
			// UPGRADETODO: JXL 轉換為 Apache POI - 修正 getCell().getContents() 調用
			String RPS_type1_result = Util.trim(getPOICellContents(sheet, 25, row));// 往來客戶信用異常資料-結果(Y:有異常/N:無異常)
			String RPS_type3_result = Util.trim(getPOICellContents(sheet, 26, row));// 婉卻紀錄資料-結果(Y:有異常/N:無異常)
			String CURIQ_type4_result = Util.trim(getPOICellContents(sheet, 27, row));// 證券暨期貨違約交割查詢-結果(Y:有異常/N:無異常)
									// J-113-0083  配合金控資訊系統113年起無法查詢證券暨期貨違約交割紀錄
									// 改成查詢聯徵T70證券違約交割紀錄
			try {
				// 更新三個查外部
				if(Util.isNotEmpty(RPS_type1_result) 
						&& Util.isNotEmpty(RPS_type3_result) && Util.isNotEmpty(CURIQ_type4_result)){
					rows++;
				}else{
					List<C120S01S> c120s01sList = c120s01sDao.findByMainIdAndCustIdAndDupNo(mainId, custId, dupNo);
					for(C120S01S c120s01s : c120s01sList){
						// 往來客戶信用異常資料
						if(Util.equals(c120s01s.getDataType(), "1")){
							if(Util.isNotEmpty(c120s01s.getDataStatus()) &&
									Util.trim(c120s01s.getDataStatus()).contains("1")){
								RPS_type1_result = "Y";
							}else{
								RPS_type1_result = "N";
							}
						}
						// 婉卻紀錄資料
						if(Util.equals(c120s01s.getDataType(), "3")){
							if(Util.isNotEmpty(c120s01s.getDataStatus()) &&
									Util.trim(c120s01s.getDataStatus()).contains("1")){
								RPS_type3_result = "Y";
							}else{
								RPS_type3_result = "N";
							}
						}
						
						// 證券暨期貨違約交割查詢
//						if(Util.equals(c120s01s.getDataType(), "4")){
//							if(Util.isNotEmpty(c120s01s.getDataStatus()) &&
//									Util.trim(c120s01s.getDataStatus()).contains("1")){
//								CURIQ_type4_result = "Y";
//							}else{
//								CURIQ_type4_result = "N";
//							}
//						}
					}
					
					// J-113-0083  配合金控資訊系統113年起無法查詢證券暨期貨違約交割紀錄
					// 改成查詢聯徵T70證券違約交割紀錄
					if(true){//runEJCIC_T70
						Map<String, Object> tas700Data = ejcicService.findTAS700ById(custId);
						if (tas700Data != null) {// EJCIC有資料就用EJCIC的
							String negFlag = (String) tas700Data.get("NEG_FLAG"); // 負面紀錄
							//若聯徵T70查詢結果為Y0或Y1則代表異常，於xls儲存格顯示「Y」
							if(Util.equals(negFlag, "Y0") || 
									Util.equals(negFlag, "Y1")){
								CURIQ_type4_result = "Y";
							}
							//若聯徵T70查詢結果為N0或N1則代表正常，於xls儲存格顯示「N」
							if(Util.equals(negFlag, "N0") || 
									Util.equals(negFlag, "N1")){
								CURIQ_type4_result = "N";
							}
						}
					}
					if(Util.isNotEmpty(RPS_type1_result) 
							&& Util.isNotEmpty(RPS_type3_result) && Util.isNotEmpty(CURIQ_type4_result)){
						//往來客戶信用異常資料-結果 
						// UPGRADETODO: JXL 轉換為 Apache POI - 需要改寫為 POI 寫入方式
						/*
						Label RPS_type1_Lab = new Label(25, row, RPS_type1_result);
						RPS_type1_Lab.setCellFormat(ClsUtil.getCellFormat(sheet.getCell(25, row)));
						sheet.addCell(RPS_type1_Lab);
						*/
						
						//婉卻紀錄資料-結果 
						// UPGRADETODO: JXL 轉換為 Apache POI - 需要改寫為 POI 寫入方式
						/*
						Label RPS_type3_Lab = new Label(26, row, RPS_type3_result);
						RPS_type3_Lab.setCellFormat(ClsUtil.getCellFormat(sheet.getCell(26, row)));
						sheet.addCell(RPS_type3_Lab);
						*/
						
						//證券暨期貨違約交割查詢-結果 
						//J-113-0083  配合金控資訊系統113年起無法查詢證券暨期貨違約交割紀錄
						//改成查詢聯徵T70證券違約交割紀錄
						// UPGRADETODO: JXL 轉換為 Apache POI - 需要改寫為 POI 寫入方式
						/*
						Label CURIQ_type4_Lab = new Label(27, row, CURIQ_type4_result);
						CURIQ_type4_Lab.setCellFormat(ClsUtil.getCellFormat(sheet.getCell(27, row)));
						sheet.addCell(CURIQ_type4_Lab);
						*/
						
						rows++;
					}
				}
			// UPGRADETODO: JXL 轉換為 Apache POI - WriteException 不再需要，但需要保留 catch 結構
			} catch (Exception e) {
				logger.error(e.getMessage(), e);
			}
			
		} // end for-loop
		if(c160m02a.getTotCount() == rows){
			this.setC160M02AExcelStatus(c160m02a, "04");
		}
	}
	
	@Override
	public List<C160M02A> findC160m02aByCreateTimeAndImportStatus(String createTime,String importExcelStatus) {
		ISearch search = c160m02aDao.createSearchTemplete();

		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.addSearchModeParameters(SearchMode.GREATER_EQUALS, "createTime", createTime);
		search.addSearchModeParameters(SearchMode.EQUALS, "importExcelStatus", "02");
		
		return c160m02aDao.find(search);
	}
	
	// UPGRADETODO: JXL 轉換為 Apache POI - 修改方法參數從 WritableSheet 改為 POI Sheet
	private JSONObject _parseExcel_bef_Q_2_1(Sheet sheet, C160M02A c160m02a) {
		String mainId = c160m02a.getMainId();

		JSONObject json = new JSONObject();
		List<GenericBean> list = new ArrayList<GenericBean>();
		int rows = 0, hasFinalRateCount = 0;
		final int isOkColumn = 20;
		Set<String> idDupSet = new HashSet<String>();
		// UPGRADETODO: JXL 轉換為 Apache POI - 修改 Font 創建和顏色設定
		Font ok_font = sheet.getWorkbook().createFont();
		Font fail_font = sheet.getWorkbook().createFont();
		ok_font.setFontName("Arial");
		ok_font.setFontHeightInPoints((short) 12);
		ok_font.setBold(true);
		ok_font.setColor(IndexedColors.BLUE.getIndex());
		
		fail_font.setFontName("Arial");
		fail_font.setFontHeightInPoints((short) 12);
		fail_font.setBold(true);
		fail_font.setColor(IndexedColors.RED.getIndex());
		// UPGRADETODO: JXL 轉換為 Apache POI - 修正第二個方法中的 getRows() 和 getCell().getContents() 調用
		for (int row = 1; row < getPOISheetRows(sheet); row++) {
			int column = 0;  
			if (Util.isEmpty(Util.trim(getPOICellContents(sheet, 1, row)))) {
				break;
			}
			StringBuffer checkMsg = new StringBuffer();
			C120M01A c120m01a = new C120M01A();
			C120S01A c120s01a = new C120S01A();
			C120S01B c120s01b = new C120S01B();
			C120S01C c120s01c = new C120S01C();
			C120S01D c120s01d = new C120S01D();
			C120S01E c120s01e = new C120S01E();
			List<C120S01H> c120s01h_list = new ArrayList<C120S01H>();
			List<C120S01I> c120s01i_list = new ArrayList<C120S01I>();
			C120S01Q c120s01q = new C120S01Q();
			c120m01a.setMainId(mainId);
			String staffNo = Util.trim(getPOICellContents(sheet, column++, row));
			if (Util.isEmpty(staffNo)) {
				checkMsg.append("員工編號不可空白!!");
			}
			c120m01a.setStaffNo(staffNo);
			String idDup = Util.trim(getPOICellContents(sheet, column++, row));
			String custId = StringUtils.substring(idDup, 0, 10); 
			String dupNo = StringUtils.substring(idDup, 10);			
			if (Util.isEmpty(custId)) {
				checkMsg.append("借款人身分證字號不可空白!!");
			} else if ( idDup.length()!=11) {
				checkMsg.append("身分證字號["+idDup+"]長度應為11碼");
				if(true){
					setMsgAtColumn(sheet, row, isOkColumn, checkMsg, ok_font, fail_font);							
				}
				continue;
			} else if (!CustidValidator.verifyID(custId)) {
				checkMsg.append("非正確身分證字號["+idDup+"]!!");
			}
			
			if(true){
				//若 idDup 重複, 會造成 mainId, custId, dupNo 的 key值重複, 拋出SQL Exception
				if(idDupSet.contains(idDup)){
					checkMsg.append("身分證字號["+idDup+"]不可重複!");
					if(true){
						setMsgAtColumn(sheet, row, isOkColumn, checkMsg, ok_font, fail_font);							
					}
					continue;
				}else{
					idDupSet.add(idDup);
				}
			}
			c120m01a.setCustId(custId);
			c120m01a.setDupNo(dupNo);
			c120m01a.setOwnBrId(c160m02a.getOwnBrId());
			c120m01a.setTypCd(c160m02a.getTypCd());
			String custName = Util.trim(getPOICellContents(sheet, column++, row));
			if (Util.isEmpty(custName)) {
				checkMsg.append("借款人姓名不可空白!!");
			}
			c120m01a.setCustName(custName);

			try {
				CapBeanUtil.copyBean(c120m01a, c120s01a, new String[] {
						"mainId", "custId", "dupNo" });
				CapBeanUtil.copyBean(c120m01a, c120s01b, new String[] {
						"mainId", "custId", "dupNo" });
				CapBeanUtil.copyBean(c120m01a, c120s01c, new String[] {
						"mainId", "custId", "dupNo" });
				CapBeanUtil.copyBean(c120m01a, c120s01d, new String[] {
						"mainId", "custId", "dupNo" });
				CapBeanUtil.copyBean(c120m01a, c120s01e, new String[] {
						"mainId", "custId", "dupNo" });
				CapBeanUtil.copyBean(c120m01a, c120s01q, new String[] {
						"mainId", "custId", "dupNo", "custName", "ownBrId" });
			} catch (CapException e) {
				logger.error(e.getMessage(), e);
			}
			// 年薪 單位:萬元
			c120s01b.setPayCurr("TWD");
			String payAmt = Util.trim(getPOICellContents(sheet, column++, row));
			if (Util.isNumeric(payAmt)) {
				 //XXX 四捨五入到整數位, 再去跑模型評等
				 //避免用 60.03萬去跑模型 → 模型得分 0
				 //但上傳DW卻是60萬               → 模型得分 -5.7682
				c120s01b.setPayAmt(Arithmetic.round(CapMath.getBigDecimal(payAmt), 0));
			} else {
				checkMsg.append("請檢查年薪需為數字!!");
			}
			// 其它所得 單位:萬元
			c120s01b.setOthCurr("TWD");
			String othAmt = Util.trim(getPOICellContents(sheet, column++, row));
			if (Util.isNumeric(othAmt)) {
				c120s01b.setOthAmt(Arithmetic.round(CapMath.getBigDecimal(othAmt), 0));
			} else {
				checkMsg.append("請檢查其它所得需為數字!!");
			}
			// 年資
			String seniority = Util.trim(getPOICellContents(sheet, column++, row));
			if (Util.isNumeric(seniority)) {
				c120s01b.setSeniority(CrsUtil.parseBigDecimal(seniority));
			} else {
				checkMsg.append("請檢查年資需為數字!!");
			}
			// 退票
			String keyin_1a = Util.trim(getPOICellContents(sheet, column++, row));			
			// 拒往
			String keyin_1b = Util.trim(getPOICellContents(sheet, column++, row));			
			// 票信查詢日期(自行輸入)
			// 在動審表上傳的 excel 其日期相關 cell「儲存格格式」是文字
			Date keyin_etchDate = CapDate.parseDate(Util.trim(getPOICellContents(sheet, column++, row)));			
			if (keyin_etchDate!=null) {
				if(Util.isEmpty(keyin_1a)){
					checkMsg.append("自行輸入票信查詢日期時，需自行輸入「退票」。");	
				}
				if(Util.isEmpty(keyin_1b)){
					checkMsg.append("自行輸入票信查詢日期時，需自行輸入「拒往」。");	
				}
			}
						
			try {
				// 取得JCIC和ETCH資料
				String version_G = scoreService.get_Version_HouseLoan();
				String version_Q = scoreService
						.get_Version_NotHouseLoan();
				String version_R = scoreService
						.get_Version_CardLoan();
				JSONObject data = new JSONObject();
				data.putAll(scoreService.getData(c120s01e.getCustId(),
						c120s01e.getDupNo(), version_G, version_Q, version_R, data));
				
				if (Util.isEmpty(data.get(Score.column.聯徵查詢日期))) {
					checkMsg.append("查無聯徵資料!!");
				}
				if (Util.isEmpty(data.get(Score.column.票信查詢日期))) {
					if (keyin_etchDate!=null && Util.isNotEmpty(keyin_1a) && Util.isNotEmpty(keyin_1b)) {
						boolean b_1a = Util.equals("Y", keyin_1a);
						boolean b_1b = Util.equals("Y", keyin_1b);
						
						data.put(Score.column.退票, b_1a?UtilConstants.haveNo.有:UtilConstants.haveNo.無);
						data.put(Score.column.拒往, b_1b?UtilConstants.haveNo.有:UtilConstants.haveNo.無);
						//XXX 用此欄位來區分「票信的結果」是否採用 user 輸入的資料
						data.put(Score.column.票信資料日期, CapDate.ZERO_DATE);
						data.put(Score.column.票信查詢日期, Util.toAD(keyin_etchDate));
						if(true){
							String orgVal = Util.trim(data.optString(Score.column.有退票_拒往_信用卡強停或催收呆帳紀錄));
							String newVal = orgVal;
							if(b_1a||b_1b){
								newVal = UtilConstants.haveNo.有;
							}else{
								if(Util.equals(UtilConstants.haveNo.NA, orgVal)){
									newVal = UtilConstants.haveNo.無;
								}
							}
							data.put(Score.column.有退票_拒往_信用卡強停或催收呆帳紀錄, newVal);
						}
						
					}else{
						checkMsg.append("查無票信資料!!");	
					}					
				}
				// 無錯誤訊息
				if (checkMsg.length() == 0) {
					String prodId = Util.trim(data.get(ClsScoreUtil.PRODID_KEY));
					data.put(ClsConstants.C101S01E.查詢組合, prodId);
					data.put(ClsConstants.C101S01E.聯徵資料日期,
							Util.trim(data.get(Score.column.聯徵資料日期)));
					data.put(ClsConstants.C101S01E.聯徵查詢日期,
							Util.trim(data.get(Score.column.聯徵查詢日期)));
					data.put(ClsConstants.C101S01E.票信資料截止日,
							Util.trim(data.get(Score.column.票信資料日期)));
					data.put(ClsConstants.C101S01E.票信查詢日期,
							Util.trim(data.get(Score.column.票信查詢日期)));
					DataParse.map2Bean(data, c120s01e);

					// 取得聯徵票信HTML內容C120S01H,C120S01I
					if(true){
						for(GenericBean gb : cls1131Service.getHtml(c120s01e.getMainId(),
								c120s01e.getCustId(), c120s01e.getDupNo(), prodId)){
							if(gb instanceof C101S01H){
								C120S01H c120s01h = new C120S01H();
								CapBeanUtil.copyBean(c120m01a, c120s01h, new String[] {
										"mainId", "custId", "dupNo" });								
								DataParse.copy(gb, c120s01h);
								c120s01h_list.add(c120s01h);
							}else if(gb instanceof C101S01I){
								C120S01I c120s01i = new C120S01I();
								CapBeanUtil.copyBean(c120m01a, c120s01i, new String[] {
										"mainId", "custId", "dupNo" });
								DataParse.copy(gb, c120s01i);
								c120s01i_list.add(c120s01i);
							}
						} 	
					}
					if(true){
						c120m01a.setJcicFlg(Util.trim(data.optString(ClsUtility.C101M01A_JCICFLG)));
						
						c120m01a.setPrimary_card(LMSUtil.fetch_BigDecimal_from_json(data, ClsUtility.C101M01A_PRIMARY_CARD));
						c120m01a.setAdditional_card(LMSUtil.fetch_BigDecimal_from_json(data, ClsUtility.C101M01A_ADDITIONAL_CARD));
						c120m01a.setBusiness_or_p_card(LMSUtil.fetch_BigDecimal_from_json(data, ClsUtility.C101M01A_BUSINESS_OR_P_CARD));
					}
					// 非房貸評分
					BigDecimal pIncome = c120s01b.getPayAmt().add(
							c120s01b.getOthAmt());

					data.put(ScoreNotHouseLoan.column.個人年所得, pIncome);
					data.put(ScoreNotHouseLoan.column.年資, seniority);
					DataParse.map2Bean(data, c120s01q);
					data = scoreService.scoreNotHouseLoan(
							ScoreNotHouseLoan.type.非房貸基本, data,
							version_Q);
					if (Util.isNotEmpty(data.get(ScoreNotHouseLoan.column.最終評等))) {
						// 設定非房貸個人評等模型
						c120m01a.setMarkModel(UtilConstants.L140S02AModelKind.非房貸);
						data.put(ScoreNotHouseLoan.column.引用_非房貸, "Y");
						// 有最終評等
						hasFinalRateCount++;
					}
					DataParse.map2Bean(data, c120s01q);

					
					if(true){
						// J-107-0130 增加 KRM040 彙總資料
						if(true){							
							List<Object> krm040_sumArr = _krm040_sumArr(custId, dupNo, prodId, c120s01q.getJcicQDate());
							for(int i=0;i<7;i++){
								boolean numericFmt = false;
								if(i==4){
									Object obj = krm040_sumArr.get(i);
									if(Util.equals(obj, "")){
										
									}else if(obj instanceof Number){
										numericFmt = true;
									}									
								}
								// UPGRADETODO: JXL 轉換為 Apache POI - 需要改寫為 POI 寫入方式
								if(numericFmt){
									// sheet.addCell(new jxl.write.Number(column, row, ((BigDecimal)krm040_sumArr.get(i)).doubleValue() , ClsUtil.getCellFormat(sheet.getCell(column++, row))));
									column++; // 跳過該欄位
								}else{
									/*
									Label valLabel = new Label(column, row, String.valueOf(krm040_sumArr.get(i) ));
									valLabel.setCellFormat(ClsUtil.getCellFormat(sheet.getCell(column++, row)));
									sheet.addCell(valLabel);
									*/
									column++; // 跳過該欄位
								}									
							}							
						}						
					}
					// record message
					// 非房貸模型最終評等
					// UPGRADETODO: JXL 轉換為 Apache POI - 需要改寫為 POI 寫入方式
					/*
					Label finalRateLab = new Label(column, row,
							Util.trim(c120s01q.getGrade3()));
					finalRateLab.setCellFormat(ClsUtil.getCellFormat(sheet
							.getCell(column++, row)));
					sheet.addCell(finalRateLab);
					*/
					column++; // 跳過該欄位

					// 票交所與聯徵負面資訊					
					// UPGRADETODO: JXL 轉換為 Apache POI - 需要改寫為 POI 寫入方式
					/*
					Label negativeInfoLab = new Label(column, row, ClsUtil.c120s01q_negativeInfo(c120s01q));
					negativeInfoLab.setCellFormat(ClsUtil.getCellFormat(sheet
							.getCell(column++, row)));
					sheet.addCell(negativeInfoLab);
					*/
					column++; // 跳過該欄位

					// 聯徵查詢日期
					String jcicDate = CapDate.formatDate(
							c120s01q.getJcicQDate(), DATAFORMAT);
					// UPGRADETODO: JXL 轉換為 Apache POI - 需要改寫為 POI 寫入方式
					/*
					Label jcicDateLab = new Label(column, row, jcicDate);
					jcicDateLab.setCellFormat(ClsUtil.getCellFormat(sheet
							.getCell(column++, row)));
					sheet.addCell(jcicDateLab);
					*/
					column++; // 跳過該欄位

					// 票信查詢日期
					String etchDate = CapDate.formatDate(
							c120s01q.getEtchQDate(), DATAFORMAT);
					// UPGRADETODO: JXL 轉換為 Apache POI - 需要改寫為 POI 寫入方式
					/*
					Label etchDateLab = new Label(column, row,
							etchDate.toString());
					etchDateLab.setCellFormat(ClsUtil.getCellFormat(sheet
							.getCell(column++, row)));
					sheet.addCell(etchDateLab);
					*/
					column++; // 跳過該欄位
				}
				if(true){
					setMsgAtColumn(sheet, row, isOkColumn, checkMsg, ok_font, fail_font);
					c120m01a.setCustNo(Util.addZeroWithValue(row, 6));						
				}				

				list.add(c120m01a);
				list.add(c120s01a);
				list.add(c120s01b);
				list.add(c120s01c);
				list.add(c120s01d);
				list.add(c120s01e);
				if(c120s01h_list.size()>0){
					list.addAll(c120s01h_list);
				}
				if(c120s01i_list.size()>0){
					list.addAll(c120s01i_list);
				}
				list.add(c120s01q);
			// UPGRADETODO: JXL 轉換為 Apache POI - WriteException 不再需要
			// } catch (WriteException e) {
			//	logger.error(e.getMessage(), e);
			} catch (CapException e) {
			logger.error(e.getMessage(), e);
			}
			rows++;
		} // end for-loop

		c160m02a.setFinCount(hasFinalRateCount);
		c160m02a.setTotCount(rows);
		//c160m02a.setDeletedTime(null);
		list.add(c160m02a);
		// 滙入前先刪除舊案
		deleteC120ByMainId(mainId);
		cls1131Service.save(list);
		return json;
	}

	// UPGRADETODO: JXL 轉換為 Apache POI - 修改方法參數從 WritableSheet 和 WritableFont 改為 POI 類型，內部邏輯需要改寫
	private void setMsgAtColumn(Sheet sheet, int rowIdx, int isOkColumn, StringBuffer checkMsg
			, Font ok_font, Font fail_font){
		// UPGRADETODO: JXL 轉換為 Apache POI - 需要改寫為 POI 寫入方式
		/*
		WritableCellFormat format = ClsUtil.getCellFormat(sheet.getCell(isOkColumn, rowIdx));
		format.setFont(checkMsg.length() == 0 ? ok_font:fail_font);

		//因 成功/失敗的 column++ 不一致，所以指定 columnIdx
		Label label = new Label(isOkColumn, rowIdx,
				checkMsg.length() == 0 ? ClsConstants.SUCCESS
						: checkMsg.toString());
		label.setCellFormat(format);
		try{
			sheet.addCell(label);
		}catch (Exception e) {
			
		}
		*/
	}
	
	// UPGRADETODO: JXL 轉換為 Apache POI - 修改方法參數從 WritableSheet 和 WritableFont 改為 POI 類型，內部邏輯需要改寫
	private void setPromptMsgAtColumn(Sheet sheet, int rowIdx, int column, String msg
			, Font ok_font){
		// UPGRADETODO: JXL 轉換為 Apache POI - 需要改寫為 POI 寫入方式
		/*
		WritableCellFormat format = ClsUtil.getCellFormat(sheet.getCell(column, rowIdx));
		format.setFont(ok_font);

		Label label = new Label(column, rowIdx, msg);
		label.setCellFormat(format);
		try{
			sheet.addCell(label);
		}catch (Exception e) {
			
		}
		*/
	}
	
	private void setC120S01SData(C120S01S c120s01s, String dataType,
			String dataStatus, byte[] loanCreditDesFile) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Timestamp currentDateTime = CapDate.getCurrentTimestamp();

		c120s01s.setDataType(dataType);
		c120s01s.setDataStatus(dataStatus);
		c120s01s.setReportFile(loanCreditDesFile);
		c120s01s.setFileSeq("1");
		c120s01s.setCreateTime(currentDateTime);
		c120s01s.setUpdater(user.getUserId());
		c120s01s.setUpdateTime(currentDateTime);
		c120s01s.setDataCreateTime(currentDateTime);
		c120s01s.setReportFileType("J");

	}
	
	@Override
	public List<C120S01Q> findC120S01QByC160M02AJcicEtchIn1Month(String branchNo) {
		String cntMonth = Util.trim(lmsService.getSysParamDataValue("LMS_J1060187_S01Q"));
		int m = Util.parseInt(cntMonth);
		if(m==2){
			return c120s01qDao.findByC160M02AJcicEtchIn2Month(branchNo);
		}
		return c120s01qDao.findByC160M02AJcicEtchIn1Month(branchNo);
	}

	@Override
	public void deleteC120S01B_C120S01E_C120S01QByMainId(String mainId) {
		c120m01aDao.deleteByMainId(mainId);
		c120s01bDao.deleteByMainId(mainId);
		c120s01eDao.deleteByMainId(mainId);
		c120s01qDao.deleteByMainId(mainId);
	}

	public List<C120S01Q> findC120S01QBySrcMainId(String srcMainId) {
		return c120s01qDao.findBySrcMainId(srcMainId);
	}


	@Override
	public List<C160S03A>  findC160S03AByMainIdOrderBySeqNo(String mainId){
		return c160s03aDao.findByMainIdOrderBySeqNo(mainId);
	}
	
	@Override
	public int findC160M03AMaxPackNoByCntrNo(String cntrNo){
		return c160m03aDao.findMaxPackNoByCntrNo(cntrNo);
	}
	
	@Override
	public int findC160S03AMaxSeqNoByCntrNo(String cntrNo){
		return c160s03aDao.findMaxSeqNoByCntrNo(cntrNo);
	}
	
	// UPGRADETODO: JXL 轉換為 Apache POI - 修改方法參數從 WritableSheet 改為 POI Sheet
	@Override
	public boolean parseC160S03A(Sheet sheet, C160M03A c160m03a
			, List<C160S03A> old_List, int maxSeq){
		
		List<C160S03A> list = new ArrayList<C160S03A>();
		int totalCount = 0;
		int okCount = 0;
		final int isOkColumn = 0;
		final int isPromptColumn = 1;
		final int dataStartColumn = 2;//使用者開始輸入欄位
		
		// UPGRADETODO: JXL 轉換為 Apache POI - 修改 Font 創建和顏色設定
		Font ok_font = sheet.getWorkbook().createFont();
		Font fail_font = sheet.getWorkbook().createFont();
		ok_font.setFontName("Arial");
		ok_font.setFontHeightInPoints((short) 12);
		ok_font.setBold(true);
		ok_font.setColor(IndexedColors.BLUE.getIndex());
		
		fail_font.setFontName("Arial");
		fail_font.setFontHeightInPoints((short) 12);
		fail_font.setBold(true);
		fail_font.setColor(IndexedColors.RED.getIndex());
		
		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String userId = user.getUserId();
		
		Set<String> cntrNo_M_C = get_cntrNo_M_C(c160m03a);
		Map<String, String> accNo_idDup_map = new HashMap<String, String>(); 
		
		/* 可以有 [2:本息平均,3:本金平均]. 但在華固地上權，只能輸入2
		 */
		String[] val_enum_avg_pay= {"2"};
		
		String[] val_enum_YN= {"Y", "N"};
		
		String[] val_enum_int_code = get_rate();
		
		// 1：固定, 2：機動, 3：定期浮動 
		String[] val_enum_int_type = {"1", "2", "3"}; 
		
		//Y:年, M:月
		//在填 lnf130_intchg_type 有另外判斷, 當12個月時{intchg_type=Y, intchg_cycl=1}
		String[] val_enum_intchg_type = {"M"};
		
		//每［1,3,6,9］
		String[] val_enum_intchg_cycl = {"1", "3", "6", "9"};
		
		/*
		0 - 本行自有資金
		1 - 中美基金
		4 - 郵匯局基金
		5 - 信保基金
		*/
		String[] val_enum_fndSre = {"0", "1", "4", "5"};
		String[] val_enum_fndSre_0 = {"101","111","121","201","211","221","222","231","241","242","251","261","271","281","291","401","402","403","411","412","413","414","415","501","502","503","504","505","506","507","508","510","511","A00","A10","A20","A30","A40","A50","A60","A70","A80","A90"};
		String[] val_enum_fndSre_1 = {"101","103","104","113","114","115","116","117","118","119","122","123","124","125","126","127","128","129","133","134","135","136","137","138","139","210","211","212","213","214","215","216","217","218","219","901","911","921","931","941","010","11A","11B","11C","11D","12A","12B","13A","13B","13C","13D","13E","13F","13G","21A","21B","21C"};
		String[] val_enum_fndSre_4 = {"111","121","131","141","151","161","400","410","420","430","440","461","471","901","2A1","2B1","2C1"};
		String[] val_enum_fndSre_5 ={""};		
		
		//1購置不動產, 2購置動產, 3企業投資, 4週轉金
		String[] val_enum_use_type = {"1", "2", "3", "4"};
		//String[] val_enum_CLS_LN_PURPOSE = {"2","L","M","N","1","3","O","P","Q","S","T","U","V","W","X","Y","Z","#","@"};
		String[] val_enum_LMS_LN_PURPOSE = {"A","B","C","D","E","K"};
		
		//指標
		String[] val_enum_ptr_part = {"00","99"};		
		
		//' ':不通知 ,'1':簡訊通知, '2':EMAIL 通知
		String[] val_enum_unpay_fg = {"", "1", "2"};
		
		// { 1:月,2:雙週 } EL06325 通知，只能月繳
		String[] val_enum_intrt_cycl = {"1"};
		
		String rctDate_cmp_fail_cond = "<=";
				
		Map<String, String> existMemoFirst6_or_9 = existMemoFirst6_or_9(c160m03a.getCntrNo());
		Set<String> thisXls = new HashSet<String>();
		Date nowTS = CapDate.getCurrentTimestamp();
		
		// UPGRADETODO: JXL 轉換為 Apache POI - 修正 getRows() 調用
		for (int row = 1; row < getPOISheetRows(sheet); row++) {
			int column = dataStartColumn;
			
			List<String> checkMsgE = new ArrayList<String>();
			List<String> checkMsgI_01 = new ArrayList<String>();
			List<String> checkMsgI_02 = new ArrayList<String>();
			C160S03A bean = new C160S03A();
			//=========			
			// UPGRADETODO: JXL 轉換為 Apache POI - 將 getContents(sheet.getCell()) 改為 getPOICellContents()
			String int_code = Util.trim(getPOICellContents(sheet, column++, row));
			BigDecimal int_sprd = valid_xls_BigDecimal(Util.trim(getPOICellContents(sheet, column++, row)), "利率/加減碼", checkMsgE);
			String int_type = Util.trim(getPOICellContents(sheet, column++, row));
			String intchg_type = Util.trim(getPOICellContents(sheet, column++, row));
			String intchg_cycl = Util.trim(getPOICellContents(sheet, column++, row));
			String fndSre = Util.trim(getPOICellContents(sheet, column++, row));
			String fund_type2 = Util.trim(getPOICellContents(sheet, column++, row));
			String lnPurs = Util.trim(getPOICellContents(sheet, column++, row));
			String ln_purpose = Util.trim(getPOICellContents(sheet, column++, row));
			Integer monthCnt = valid_xls_Integer(Util.trim(getPOICellContents(sheet, column++, row)), "貸款總期數", checkMsgE);
			Date due_dt = valid_xls_Date(Util.trim(getPOICellContents(sheet, column++, row)), "到期日", checkMsgE);
			Integer rt_dd = valid_xls_Integer(Util.trim(getPOICellContents(sheet, column++, row)), "還款基準日", checkMsgE);
			Date nt_rt_dt = valid_xls_Date(Util.trim(getPOICellContents(sheet, column++, row)), "下次還款日", checkMsgE);
			Integer int_rt_dd = valid_xls_Integer(Util.trim(getPOICellContents(sheet, column++, row)), "扣帳基準日", checkMsgE);
			Date int_intrt_dt = valid_xls_Date(Util.trim(getPOICellContents(sheet, column++, row)), "下次扣帳日", checkMsgE);
			String avgPay = Util.trim(getPOICellContents(sheet, column++, row));
			BigDecimal ehPayCpt = valid_xls_BigDecimal(Util.trim(getPOICellContents(sheet, column++, row)), "每期攤還本金", checkMsgE);
			String intrt_cycl = Util.trim(getPOICellContents(sheet, column++, row));
			String autoRct = Util.trim(getPOICellContents(sheet, column++, row));
			Date rctDate = valid_xls_Date(Util.trim(getPOICellContents(sheet, column++, row)), "進帳日期", checkMsgE);
			String accNo = Util.trim(getPOICellContents(sheet, column++, row));
			String swft = Util.trim(getPOICellContents(sheet, column++, row));
			BigDecimal rctAmt = valid_xls_BigDecimal(Util.trim(getPOICellContents(sheet, column++, row)), "進帳金額", checkMsgE);
			String autoPay = Util.trim(getPOICellContents(sheet, column++, row));
			String atPayNo = Util.trim(getPOICellContents(sheet, column++, row));
			//分戶
			String custId_s = Util.trim(getPOICellContents(sheet, column++, row));
			String dupNo_s = Util.trim(getPOICellContents(sheet, column++, row));
			String int_code_s = Util.trim(getPOICellContents(sheet, column++, row));
			BigDecimal int_sprd_s = valid_xls_BigDecimal(Util.trim(getPOICellContents(sheet, column++, row)), "利率/加減碼-分戶", checkMsgE);
			String int_type_s = Util.trim(getPOICellContents(sheet, column++, row));
			String intchg_type_s = Util.trim(getPOICellContents(sheet, column++, row));
			String intchg_cycl_s = Util.trim(getPOICellContents(sheet, column++, row));
			Integer monthCnt_s = valid_xls_Integer(Util.trim(getPOICellContents(sheet, column++, row)), "貸款總期數-分戶", checkMsgE);
			Date due_dt_s = valid_xls_Date(Util.trim(getPOICellContents(sheet, column++, row)), "到期日-分戶", checkMsgE);
			Integer rt_dd_s = valid_xls_Integer(Util.trim(getPOICellContents(sheet, column++, row)), "還款基準日-分戶", checkMsgE);
			Date nt_rt_dt_s = valid_xls_Date(Util.trim(getPOICellContents(sheet, column++, row)), "下次還款日-分戶", checkMsgE);
			Integer int_rt_dd_s = valid_xls_Integer(Util.trim(getPOICellContents(sheet, column++, row)), "扣帳基準日-分戶", checkMsgE);
			Date int_intrt_dt_s = valid_xls_Date(Util.trim(getPOICellContents(sheet, column++, row)), "下次扣帳日-分戶", checkMsgE);
			String avgPay_s = Util.trim(getPOICellContents(sheet, column++, row));
			BigDecimal ehPayCpt_s = valid_xls_BigDecimal(Util.trim(getPOICellContents(sheet, column++, row)), "每期攤還本金-分戶", checkMsgE);
			String intrt_cycl_s = Util.trim(getPOICellContents(sheet, column++, row));
			String autoPay_s = Util.trim(getPOICellContents(sheet, column++, row));
			String atPayNo_s = Util.trim(getPOICellContents(sheet, column++, row));
			String adr_ptr_s = Util.trim(getPOICellContents(sheet, column++, row));
			String oph_ptr_s = Util.trim(getPOICellContents(sheet, column++, row));
			String hph_ptr_s = Util.trim(getPOICellContents(sheet, column++, row));
			String mph_ptr_s = Util.trim(getPOICellContents(sheet, column++, row));
			String eml_ptr_s = Util.trim(getPOICellContents(sheet, column++, row));
			String memo_s = Util.trim(getPOICellContents(sheet, column++, row));
			String unpay_fg_s = Util.trim(getPOICellContents(sheet, column++, row));
			BigDecimal tax_rate = valid_xls_BigDecimal(Util.trim(getPOICellContents(sheet, column++, row)), "扣稅負擔值", checkMsgE);
			Integer allow_beg = valid_xls_Integer(Util.trim(getPOICellContents(sheet, column++, row)), "寬限期(起)", checkMsgE);
			Integer allow_end = valid_xls_Integer(Util.trim(getPOICellContents(sheet, column++, row)), "寬限期(迄)", checkMsgE);
			
			if (Util.isEmpty(custId_s)) {
				break;
			}
			//=========		
			if(true){
				chk_acctNo(accNo_idDup_map, accNo);
				chk_acctNo(accNo_idDup_map, atPayNo);
				chk_acctNo(accNo_idDup_map, atPayNo_s);
			}
			//=========		
			// 驗證
			if(true){
				
				if(true){
					valid_val(int_code, "利率代碼", checkMsgE, val_enum_int_code);
					//~~~~~~
					if(int_sprd==null){
						int_sprd = BigDecimal.ZERO;
					}
					//~~~~~~
					if(Util.equals("1", int_type)||Util.equals("2", int_type)){
						intchg_type = "";
						intchg_cycl = "";
					}else if(Util.equals("3", int_type)){ //定期浮動
						valid_val(intchg_type, "利率變動方式", checkMsgE, val_enum_intchg_type);
						valid_val(intchg_cycl, "利率變動週期", checkMsgE, val_enum_intchg_cycl);
						
					}else {
						valid_val(int_type, "利率方式", checkMsgE, val_enum_int_type);	
					}
					//~~~~~~
					valid_val(fndSre, "資金來源", checkMsgE, val_enum_fndSre);
					if(Util.equals("0", fndSre)){
						valid_val(fund_type2, "資金來源小類", checkMsgE, val_enum_fndSre_0);	
					}else if(Util.equals("1", fndSre)){
						valid_val(fund_type2, "資金來源小類", checkMsgE, val_enum_fndSre_1);
					}else if(Util.equals("4", fndSre)){
						valid_val(fund_type2, "資金來源小類", checkMsgE, val_enum_fndSre_4);
					}else if(Util.equals("5", fndSre)){
						valid_val(fund_type2, "資金來源小類", checkMsgE, val_enum_fndSre_5);
					}
					//~~~~~~
					valid_val(lnPurs, "用途別", checkMsgE, val_enum_use_type);
					valid_val(ln_purpose, "融資業務分類", checkMsgE, val_enum_LMS_LN_PURPOSE);
					if(Util.equals(ln_purpose, "@")){
						if(Util.equals(fndSre, "4") && Util.equals(fund_type2, "440")){
							
						}else{
							checkMsgE.add("資金來源大類為４且資金來源小類為４４０，融資業務分類才能選＠ ");	
						}
					}
					if(CrsUtil.inCollection(ln_purpose, new String[]{"1"})
							&& !Util.equals(lnPurs, "4")){
						checkMsgE.add("融資業務分類"+ln_purpose+"，用途別需為4");
					}   
					if(CrsUtil.inCollection(ln_purpose, new String[]{"L","M","N","2","3","U","#","@"})
							&& !Util.equals(lnPurs, "1")){
						checkMsgE.add("融資業務分類"+ln_purpose+"，用途別需為 1");
					}   
					//~~~~~~
					if(monthCnt==null || monthCnt<=0){
						checkMsgE.add("貸款總期數"+"需>0");
					}

					//還款基準日 固定1日
					valid_dd_val(rt_dd, "還款基準日", checkMsgE, 1);
					//扣帳基準日 固定5日
					valid_dd_val(int_rt_dd, "扣帳基準日", checkMsgE, 5);
					
					valid_baseDD_Date(checkMsgE, rt_dd, nt_rt_dt, "還款基準日", "下次還款日");
					valid_baseDD_Date(checkMsgE, int_rt_dd, int_intrt_dt, "扣帳基準日", "下次扣帳日");
					
					if(true){
						/*
						 *不能一個 2017-05, 另一個 2017-06
						 */
						if(!LMSUtil.cmp_yyyyMM(nt_rt_dt, "==", int_intrt_dt)){
							checkMsgE.add("下次還款日("+TWNDate.toAD(nt_rt_dt)+")"+"下次扣帳日("+TWNDate.toAD(int_intrt_dt)+")需同一年月");	
						}
					}
					
					
					valid_val(avgPay, "償還方式", checkMsgE, val_enum_avg_pay);
					
					valid_AvgPay_EhPayCpt(checkMsgE, avgPay, ehPayCpt
							, "本息平均", "本金平均", "每期攤還本金");				
					//~~~~~~
					valid_val(intrt_cycl, "期付金繳款週期", checkMsgE, val_enum_intrt_cycl);
					//~~~~~~
					valid_val(autoRct, "自動進帳", checkMsgE, val_enum_YN);
					if(Util.equals("Y",autoRct)){
						//帳號
						valid_val_notEmpty(accNo, "存款帳號 (進帳帳號)", checkMsgE);
					}
					
					//日期
					if(rctDate==null || LMSUtil.cmpDate(rctDate, rctDate_cmp_fail_cond, nowTS)){
						checkMsgE.add("進帳日期"+"需大於本營業日");
					}
					
					if(Util.isNotEmpty(accNo)){
						//進帳帳號 accNo 只可［主借人 or 共借人］
						String owner_idDup = accNo_idDup_map.get(accNo);						
						if(cntrNo_M_C.contains(owner_idDup)){
							//ok
						}else{
							if(owner_idDup.length()==11){
								checkMsgE.add("帳號["+accNo+"]之存戶["+owner_idDup+"]非"+c160m03a.getCntrNo()+"主借人or共借人");
							}else{
								checkMsgE.add("帳號["+accNo+"]檢核異常["+owner_idDup+"]");
							}							
						}						
					}
					
					valid_val_notEmpty(swft, "放款幣別", checkMsgE);
					if(rctAmt==null || rctAmt.compareTo(BigDecimal.ZERO)<=0){
						checkMsgE.add("進帳金額"+"需>0");
					}
					//~~~~~~
					valid_val(autoPay, "自動扣帳", checkMsgE, val_enum_YN);
					if(Util.equals("Y", autoPay)){
						valid_val_notEmpty(atPayNo, "扣帳帳號", checkMsgE);
					}
					if(Util.isNotEmpty(atPayNo)){
						if(accNo_idDup_map.get(atPayNo).length()==11){
							//found
						}else{
							checkMsgE.add("扣帳帳號["+atPayNo+"]異常["+accNo_idDup_map.get(atPayNo)+"]");	
						}
					}
					F_2700_CHK_TOT_TERM(checkMsgE, intrt_cycl, rctDate, due_dt, monthCnt, false);
				}
				
				//分戶
				if(true){
					if(true){
						boolean idDupBasicChk = true;
						if(!(custId_s.length()==8||custId_s.length()==10)){
							checkMsgE.add("分戶統編"+"長度("+custId_s.length()+")錯誤");
							idDupBasicChk = false;
						}
						if(!(dupNo_s.length()==1)){
							checkMsgE.add("分戶統編重複碼"+"長度("+dupNo_s.length()+")錯誤");
							idDupBasicChk = false;
						}
						if(idDupBasicChk){
							Map<String, Object> map0024 = iCustomerService.findByIdDupNo(custId_s, dupNo_s);
							if(Util.isEmpty(map0024)){
								checkMsgE.add("分戶("+custId_s+"-"+dupNo_s+")於0024查無資料");	
							}
						}
					}					
					
					if(eq_sub()){
						eq_comp_sub_val(int_code, int_code_s, "利率代碼", "利率代碼-分戶", checkMsgE);	
					}else{
						valid_val(int_code_s, "利率代碼-分戶", checkMsgE, val_enum_int_code);
					}
					
					//~~~~~~
					if(int_sprd_s==null){
						int_sprd_s = BigDecimal.ZERO;
					}
					if(eq_sub()){
						eq_comp_sub_val(int_sprd, int_sprd_s, "利率/加減碼", "利率/加減碼-分戶", checkMsgE);
					}
					//~~~~~~
					if(eq_sub()){
						eq_comp_sub_val(int_type, int_type_s, "利率方式", "利率方式-分戶", checkMsgE);
						eq_comp_sub_val(intchg_type, intchg_type_s, "利率變動方式", "利率變動方式-分戶", checkMsgE);
						eq_comp_sub_val(intchg_cycl, intchg_cycl_s, "利率變動週期", "利率變動週期-分戶", checkMsgE);
					}else{
						if(Util.equals("1", int_type_s)||Util.equals("2", int_type_s)){
							intchg_type_s = "";
							intchg_cycl_s = "";
						}else if(Util.equals("3", int_type_s)){ //定期浮動
							if(Util.equals("1", intchg_type_s)){
								valid_val(intchg_cycl_s, "利率變動週期-分戶", checkMsgE, val_enum_intchg_cycl);
								
							}else if(Util.equals("2", intchg_type_s)||Util.equals("3", intchg_type_s)){
								intchg_cycl_s = "";
							}else{
								valid_val(intchg_type_s, "利率變動方式-分戶", checkMsgE, val_enum_intchg_type);	
							}					
							
						}else {
							valid_val(int_type_s, "利率方式-分戶", checkMsgE, val_enum_int_type);	
						}
					}
					//~~~~~~
					if(monthCnt_s==null || monthCnt_s<=0){
						checkMsgE.add("貸款總期數-分戶"+"需>0");
					}
					if(eq_sub()){
						eq_comp_sub_val(monthCnt, monthCnt_s, "貸款總期數", "貸款總期數-分戶", checkMsgE);
					}
					if(eq_sub()){
						eq_comp_sub_val(due_dt, due_dt_s, "到期日", "到期日-分戶", checkMsgE);
					}
										
					//承購戶 還款基準日 固定1日
					valid_dd_val(rt_dd_s, "還款基準日-分戶", checkMsgE, 1);
					//承購戶 扣帳基準日 固定1日
					valid_dd_val(int_rt_dd_s, "扣帳基準日-分戶", checkMsgE, 1);
					
					
					valid_baseDD_Date(checkMsgE, rt_dd_s, nt_rt_dt_s, "還款基準日-分戶", "下次還款日-分戶");
					valid_baseDD_Date(checkMsgE, int_rt_dd_s, int_intrt_dt_s, "扣帳基準日-分戶", "下次扣帳日-分戶");
					if(true){
						/*
						 * 分戶需檢核[還款基準日, 扣帳基準日]需相同
						 * 且[下次還款日-分戶, 下次扣帳日-分戶]不能一個 2017-05-01, 另一個 2017-06-01
						 */
						
						if(!LMSUtil.cmpDate(nt_rt_dt_s, "==", int_intrt_dt_s)){
							checkMsgE.add("下次還款日-分戶("+TWNDate.toAD(nt_rt_dt_s)+")"+"下次扣帳日-分戶("+TWNDate.toAD(int_intrt_dt_s)+")需相同");	
						}
					}
					
					if(eq_sub()){
						eq_comp_sub_val(avgPay, avgPay_s, "償還方式", "償還方式-分戶", checkMsgE);
						eq_comp_sub_val(ehPayCpt, ehPayCpt_s, "每期攤還本金", "每期攤還本金-分戶", checkMsgE);
					}else{
						valid_val(avgPay_s, "償還方式-分戶", checkMsgE, val_enum_avg_pay);
						
						valid_AvgPay_EhPayCpt(checkMsgE, avgPay_s, ehPayCpt_s
									, "本息平均-分戶", "本金平均-分戶", "每期攤還本金-分戶");
					}
					//~~~~~~
					valid_val(intrt_cycl_s, "期付金繳款週期-分戶", checkMsgE, val_enum_intrt_cycl);
					//~~~~~~
					valid_val(autoPay_s, "自動扣帳-分戶", checkMsgE, val_enum_YN);
					if(Util.equals("Y", autoPay_s)){
						valid_val_notEmpty(atPayNo_s, "扣帳帳號-分戶", checkMsgE);
					}
					if(Util.isNotEmpty(atPayNo_s)){
						//或參考 CLS1161GridHandler :: AccountQuery
//						Set<String> dpSet = new HashSet<String>();						
//						for(Map<String, Object> eldpfData : dwdbBaseService.findDW_IDDP_DPF_Seqno_ByCustId(custId_s, dupNo_s)){
//							dpSet.add(Util.trim(eldpfData.get("BRNO"))
//									+Util.trim(eldpfData.get("APCD"))
//									+Util.trim(eldpfData.get("SEQNO")));
//						}
//						
//						String[] val_enum_dp_byCustId =new ArrayList<String>(dpSet).toArray(new String[0]);
//						valid_val_info(atPayNo_s , "扣帳帳號-分戶", checkMsgI, val_enum_dp_byCustId);	
						if(accNo_idDup_map.get(atPayNo_s).length()==11){
							if(Util.equals(accNo_idDup_map.get(atPayNo_s), LMSUtil.getCustKey_len10custId(custId_s, dupNo_s))){
								//OK
							}else{
								checkMsgI_02.add("扣帳帳號-分戶["+atPayNo_s+"]的擁有者為["+accNo_idDup_map.get(atPayNo_s)+"]");
							}
						}else{
							checkMsgE.add("扣帳帳號-分戶["+atPayNo_s+"]異常["+accNo_idDup_map.get(atPayNo_s)+"]");	
						}
					}
					F_2700_CHK_TOT_TERM(checkMsgE, intrt_cycl_s, rctDate, due_dt_s, monthCnt_s, true);
				}
				
				if(true){
					valid_ptr(custId_s, dupNo_s, "ADR", adr_ptr_s, "通訊地址指標-分戶", checkMsgE, val_enum_ptr_part);	
					valid_ptr(custId_s, dupNo_s, "OPH", oph_ptr_s, "公司電話指標-分戶", checkMsgE, val_enum_ptr_part);	
					valid_ptr(custId_s, dupNo_s, "HPH", hph_ptr_s, "住家電話指標-分戶", checkMsgE, val_enum_ptr_part);	
					valid_ptr(custId_s, dupNo_s, "MPH", mph_ptr_s, "行動電話指標-分戶", checkMsgE, val_enum_ptr_part);	
					valid_ptr(custId_s, dupNo_s, "EML", eml_ptr_s, "EMAIL指標-分戶", checkMsgE, val_enum_ptr_part);						
				}
				
				if(true){
					String c_memo_s = Util.truncateString(memo_s, MAXLEN_C160S03A_MEMO);
					if(!Util.equals(c_memo_s, memo_s)){
						checkMsgE.add("備註-分戶"+"不可超過20個中文字【"+c_memo_s+"】");
					}
					
					if(Util.isEmpty(memo_s)){
						// ok
					}else{
						String err_memo_s = err_memo_s(memo_s, "備註-分戶");
						if(Util.isEmpty(err_memo_s)){
							//格式符合，再判斷 cfmMsg
							cfm_memo_s(memo_s, checkMsgI_01, existMemoFirst6_or_9, thisXls);
							//========
							thisXls.add(fetch_first_6_or_9(memo_s));							
						}else{
							checkMsgE.add(err_memo_s);
						}
					}
				}				
				valid_val(unpay_fg_s, "扣帳失敗通知方式-分戶", checkMsgE, val_enum_unpay_fg);
				if(Util.equals("1", unpay_fg_s) && Util.equals("00", mph_ptr_s)){
					checkMsgE.add("簡訊通知"+"請輸入行動電話指標");
				}
				if(Util.equals("2", unpay_fg_s) && Util.equals("00", eml_ptr_s)){
					checkMsgE.add("EMAIL 通知"+"請輸入 EMAIL 指標");					
				}
				if(tax_rate==null){
					checkMsgE.add("請輸入"+"扣稅負擔值");
				}
				if(allow_beg==null){
					checkMsgE.add("請輸入"+"寬限期(起)");
				}
				if(allow_end==null){
					checkMsgE.add("請輸入"+"寬限期(迄)");
				}
			}
			//=========		
			// 組 c160s03a
			if(true){
				bean.setMainId(c160m03a.getMainId());
				bean.setCntrNo(c160m03a.getCntrNo());
				bean.setSeqNo(null);
				bean.setCreator(userId);
				bean.setCreateTime(CapDate.getCurrentTimestamp());
				//======
				bean.setInt_code(int_code);
				bean.setInt_sprd(int_sprd);
				bean.setInt_type(int_type);
				bean.setIntchg_type(intchg_type);
				bean.setIntchg_cycl(intchg_cycl);
				bean.setFndSre(fndSre);
				bean.setFund_type2(fund_type2);
				bean.setLnPurs(lnPurs);
				bean.setLn_purpose(ln_purpose);
				bean.setMonthCnt(monthCnt);
				bean.setDue_dt(due_dt);
				bean.setRt_dd(rt_dd);
				bean.setNt_rt_dt(nt_rt_dt);
				bean.setInt_rt_dd(int_rt_dd);
				bean.setInt_intrt_dt(int_intrt_dt);
				bean.setAvgPay(avgPay);
				bean.setEhPayCpt(ehPayCpt);
				bean.setIntrt_cycl(intrt_cycl);
				bean.setAutoRct(autoRct);
				bean.setRctDate(rctDate);
				bean.setAccNo(accNo);
				bean.setSwft(swft);
				bean.setRctAmt(rctAmt);
				bean.setAutoPay(autoPay);
				bean.setAtPayNo(atPayNo);
				///~~~~~~
				bean.setCustId_s(custId_s);
				bean.setDupNo_s(dupNo_s);				
				bean.setInt_code_s(int_code_s);
				bean.setInt_sprd_s(int_sprd_s);
				bean.setInt_type_s(int_type_s);
				bean.setIntchg_type_s(intchg_type_s);
				bean.setIntchg_cycl_s(intchg_cycl_s);
				bean.setMonthCnt_s(monthCnt_s);
				bean.setDue_dt_s(due_dt_s);
				bean.setRt_dd_s(rt_dd_s);
				bean.setNt_rt_dt_s(nt_rt_dt_s);
				bean.setInt_rt_dd_s(int_rt_dd_s);
				bean.setInt_intrt_dt_s(int_intrt_dt_s);
				bean.setAvgPay_s(avgPay_s);
				bean.setEhPayCpt_s(ehPayCpt_s);
				bean.setIntrt_cycl_s(intrt_cycl_s);
				bean.setAutoPay_s(autoPay_s);
				bean.setAtPayNo_s(atPayNo_s);
				///~~~~~~
				bean.setAdr_ptr_s(adr_ptr_s);
				bean.setOph_ptr_s(oph_ptr_s);
				bean.setHph_ptr_s(hph_ptr_s);
				bean.setMph_ptr_s(mph_ptr_s);
				bean.setEml_ptr_s(eml_ptr_s);
				bean.setMemo_s(memo_s);
				bean.setUnpay_fg_s(unpay_fg_s);
				bean.setTax_rate(tax_rate);
				bean.setAllow_beg(allow_beg);
				bean.setAllow_end(allow_end);
				///~~~~~~
				bean.setChkYN(checkMsgE.size() == 0?"Y":"N");				
			}
			//=========
			if(true){
				setMsgAtColumn(sheet, row, isOkColumn, 
						new StringBuffer(StringUtils.join(checkMsgE, ",")), 
						ok_font, fail_font);			
				
				List<String> checkMsgI = new ArrayList<String>();
				if(true){
					//為控制順序
					checkMsgI.addAll(checkMsgI_01);
					checkMsgI.addAll(checkMsgI_02);
				}
				setPromptMsgAtColumn(sheet, row, isPromptColumn, StringUtils.join(checkMsgI, ","), ok_font);
			}		
			list.add(bean);
			//=========
			totalCount++;
		}
		
		if(true){
			//填入 SeqNo
			int size = list.size();
			/*
			 size=2, old_List=1
			 size=2, old_List=2
			 size=2, old_List=3
			 */
			List<Integer> seqNoArr = new ArrayList<Integer>();
			int old_size = old_List==null?0:old_List.size();
			
			for(int i=0;i<Math.min(size, old_size);i++){
				seqNoArr.add(old_List.get(i).getSeqNo()*-1);
			}
			if(size>old_size){				
				for(int i=0;i<(size-old_size);i++){
					seqNoArr.add(++maxSeq);
				}	
			}
			if(size!=seqNoArr.size()){
				return false;
			}
			
			for(int i=0; i<size; i++){
				list.get(i).setSeqNo(seqNoArr.get(i));
			}

		}
		c160m03a.setFinCount(okCount);
		c160m03a.setTotCount(totalCount);
		c160m03a.setUpdater(userId);
		c160m03a.setUpdateTime(CapDate.getCurrentTimestamp());
		if(true){
			List<GenericBean> save_list = new ArrayList<GenericBean>();
			save_list.add(c160m03a);
			save_list.addAll(list);
			//=====			
			save(save_list);
		}		
		//==========
		return true;		
	}
	
	// UPGRADETODO: JXL 轉換為 Apache POI - 修正 getContents 方法為 POI 格式
	private String getContents(org.apache.poi.ss.usermodel.Cell cell) {
	    if (cell == null) {
	        return "";
	    }
	    
	    try {
	        switch (cell.getCellType()) {
	            case STRING:
	                return cell.getStringCellValue();
	            case NUMERIC:
	                if (DateUtil.isCellDateFormatted(cell)) {
	                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
	                    return sdf.format(cell.getDateCellValue());
	                } else {
	                    // 處理數值格式，避免科學記號
	                    double numericValue = cell.getNumericCellValue();
	                    if (numericValue == (long) numericValue) {
	                        return String.valueOf((long) numericValue);
	                    } else {
	                        return String.valueOf(numericValue);
	                    }
	                }
	            case BOOLEAN:
	                return String.valueOf(cell.getBooleanCellValue());
	            case FORMULA:
	                try {
	                    return cell.getStringCellValue();
	                } catch (Exception e) {
	                    return String.valueOf(cell.getNumericCellValue());
	                }
	            case BLANK:
	            default:
	                return "";
	        }
	    } catch (Exception e) {
	        return "";
	    }
	}
	private Set<String> get_cntrNo_M_C(C160M03A c160m03a){
		Set<String> set = new HashSet<String>();
		//主借人
		set.add(LMSUtil.getCustKey_len10custId(c160m03a.getCustId(), c160m03a.getDupNo()));
		//共借人
		for(Map<String, Object> row :misELLNGTEEService.findByCntrNo(c160m03a.getCntrNo())){
			if(Util.equals(UtilConstants.lngeFlag.共同借款人, MapUtils.getString(row, "LNGEFLAG"))){
				
				set.add(LMSUtil.getCustKey_len10custId(Util.trim(MapUtils.getString(row, "LNGEID")), 
						Util.trim(MapUtils.getString(row, "DUPNO1"))));
			}
		}
		
		return  set;
	}
	private String[] get_rate(){
		Map<String, String> m = new HashMap<String, String>();
		m.put("15", "本行１個月定存固定利率");
		m.put("16", "本行１個月期定存機動利率");
		m.put("17", "本行３個月期定存固定利率");
		m.put("18", "本行３個月期定存機動利率");
		m.put("19", "本行６個月定存固定利率");
		m.put("20", "本行６個月期定存機動利率");
		m.put("21", "本行９個月定存固定利率");
		m.put("22", "本行９個月定存機動利率");
		m.put("23", "本行一年期定存固定利率");
		m.put("24", "本行一年期定存機動利率");
		m.put("30", "本行一年期定儲固定利率");
		m.put("31", "本行一年期定儲機動利率");
		m.put("32", "本行二年期定儲固定利率");
		m.put("33", "本行二年期定儲機動利率");
		m.put("34", "本行三年期定儲固定利率");
		m.put("35", "本行三年期定儲機動利率");
		m.put("40", "本行聯行息（臺幣）");
		m.put("45", "本行５個月期定存機動利率");
		m.put("64", "本行基放利率");
		m.put("76", "本行二年期定存固定利率");
		m.put("77", "本行二年期定存機動利率");
		m.put("78", "本行三年期定存固定利率");
		m.put("79", "本行三年期定存機動利率");
		m.put("AT", "一年期定存機動利率大額");
		m.put("I7", "中華郵政股份有限公司半年期定存機動利率");
		m.put(CrsUtil.RATE_TYPE_MI, "１１１０３２３新國宅利率");
		m.put(CrsUtil.RATE_TYPE_MK, "新國宅利率（Ｐ７＋０．０４２％）");
		m.put("ML", "政策留貸寬限期內不補助（Ｐ６＋１．４％）");
		m.put("MM", "政策留貸寬限期內半額補助（（Ｐ６＋１．４％）／２）");
		m.put("MN", "政策留貸寬限期滿後（Ｐ６＋１．１５％）");
		m.put("MO", "９７年前季調政策留貸寬限期滿後（Ｐ６＋１．１５％）");
		m.put("M1", "舊國宅利率（Ｐ７＋０．５７５％）");
		m.put(CrsUtil.RATE_TYPE_M2, "行員房貸利率"); // "M2"
		m.put(CrsUtil.RATE_TYPE_M3, "行員消貸利率"); // "M3"
		m.put(CrsUtil.RATE_TYPE_MR, "行員利率"); // "MR"
		m.put("M5", "央行重貼現率與擔保放款融通利率平均值");
		m.put("M6", "中鋼擔保透支─（大額定存機動利率＋０．２５％）／０．９４６");
		m.put("M8", "９７年前季調政策留貸寬限期內不補助（Ｐ６＋１．４％）");
		m.put("M9", "９７年前季調政策留貸寬限期內半額補助（Ｐ６＋１．４％）／２");
		m.put(CrsUtil.RATE_TYPE_N2, "行員理財型貸款利率"); // "N2"
		m.put("P4", "中華郵政股份有限公司郵匯局一個月期定存機動利率");
		m.put("P6", "中華郵政股份有限公司一年期定儲機動利率");
		m.put(CrsUtil.RATE_TYPE_P7, "中華郵政股份有限公司二年期定儲機動利率"); // "P7"
		m.put("P9", "中華郵政股份有限公司九個月期定存機動利率");
		m.put("QH", "ＴＡＩＢＯＲ一週");
		m.put("QI", "ＴＡＩＢＯＲ二週");
		m.put("QJ", "ＴＡＩＢＯＲ一個月");
		m.put("QK", "ＴＡＩＢＯＲ二個月");
		m.put("QL", "ＴＡＩＢＯＲ三個月");
		m.put("QM", "ＴＡＩＢＯＲ六個月");
		m.put("QN", "ＴＡＩＢＯＲ九個月");
		m.put("QO", "ＴＡＩＢＯＲ十二個月");
		m.put("QP", "ＴＡＩＢＯＲ一個月平均利率");
		m.put("QQ", "ＴＡＩＢＯＲ三個月平均利率");
		m.put("QR", "ＴＡＩＢＯＲ六個月平均利率");
		m.put("RN", "ＴＡＩＢＩＲ０１初級３０天期");
		m.put("RO", "ＴＡＩＢＩＲ０１初級６０天期");
		m.put("RP", "ＴＡＩＢＩＲ０１初級９０天期");
		m.put("RQ", "ＴＡＩＢＩＲ０１初級１８０天期");
		m.put("RR", "ＴＡＩＢＩＲ０２次級３０天期");
		m.put("RS", "ＴＡＩＢＩＲ０２次級６０天期");
		m.put("RT", "ＴＡＩＢＩＲ０２次級９０天期");
		m.put("RU", "ＴＡＩＢＩＲ０２次級１８０天期");
		m.put("S8", "新臺幣基礎放款利率");
		m.put("ZH", "台銀一個月期定存機動利率");
		m.put("ZI", "台銀活存利率");
		m.put("ZJ", "台銀一年期定儲機動利率");
		m.put("Z1", "央行重貼現率");
		m.put("Z2", "擔保放款融通利率");
		m.put("Z3", "土銀基放");
		m.put("Z4", "台企銀基放利率");
		m.put(CrsUtil.RATE_TYPE_Z5, "１１１０３２３政府優惠房貸利率");
		m.put(CrsUtil.RATE_TYPE_Z6, "青安減碼利率"); 
		//Z7 只限勞工紓困舊案,C160S03A 整批自動開戶分戶檔 不適用 m.put("Z7", "勞工紓困貸款利率");
		m.put("Z8", "交銀基放利率");
		m.put("Z9", "台銀基放利率");
		m.put("03", "本行活存利率");
		m.put("05", "本行活儲利率");
		m.put("6B", "競爭性利率");
		m.put("6C", "季變動消費金融指標利率");
		m.put("6D", "基準利率");
		m.put("6K", "遠期信用狀利率");
		m.put("6L", "前一營業日金融業隔日夜拆款加權平均利率");
		m.put("6M", "本行拆放參考利率");
		m.put("6N", "交銀定儲利率指數");
		m.put("6P", "７００億優惠利率");
		m.put("6Q", "一年定存機動大額利率／０．９４６");
		m.put(CrsUtil.RATE_TYPE_6R, "月變動消費金融指標利率"); // "6R"
		m.put("6S", "基準利率月指標利率");
		m.put("7C", "舊季調兆豐金控集團職員貸款利率（６Ｃ＋０．１５％）");
		m.put(CrsUtil.RATE_TYPE_7D, "新兆豐金控集團職員貸款利率（６Ｒ）"); // "7D"
		m.put("7R", "舊兆豐金控集團職員貸款利率（６Ｒ＋０．１５％）");
		m.put("8A", "一年定存機動利率／０．９５");
		m.put("8B", "一年定存機動利率／０．９４６");
		m.put("8C", "二年定存機動利率／０．９４６");
		m.put("8D", "三年定存機動利率／０．９４６");
		m.put("8E", "二年定儲機動利率／０．９４６");
		m.put("8F", "一年定儲機動利率／０．９４６");
		m.put(CrsUtil.RATE_TYPE_01, "自訂利率"); // "01"
		return new ArrayList<String>(m.keySet()).toArray(new String[0]);
	} 
	                
	private void valid_val_notEmpty(String val, String desc, List<String> checkMsg){
		if(Util.isEmpty(val)){
			checkMsg.add(desc+"不可空白");
		}
	}
	private void valid_val(String val, String desc, List<String>  checkMsg, String[] arr){
		boolean match = false;
		for(String each: arr){
			if(Util.equals(each, val)){
				match = true;
				break;
			}
		}
		
		if(!match){
			String allowVal = StringUtils.join(arr, ",");
			if(arr.length>5){
				List<String> abbr_list = new ArrayList<String>();
				abbr_list.add(arr[0]);
				abbr_list.add(arr[1]);
				abbr_list.add(arr[2]);
				abbr_list.add(arr[3]);
				allowVal = StringUtils.join(abbr_list, ",")+"...";
			}
			checkMsg.add(desc+"應選["+allowVal+"]");
		}
	}
	private void valid_dd_val(int val, String desc, List<String>  checkMsgE, int needEq){
		if(val==needEq){
			//ok
		}else{
			checkMsgE.add(desc+"限為"+needEq+"日");
		}
	}
	private void valid_ptr(String custId_s, String dupNo_s
			, String flag, String val, String desc, List<String>  checkMsg, String[] arr_part){
		if(CrsUtil.inCollection(val, arr_part)){
			
		}else{
			int v = Util.parseInt(val); 
			if(Util.equals("ADR", flag)){
				if(!valid_adr_ptr(custId_s, dupNo_s, v)){
					checkMsg.add(desc+"["+val+"]不存在");
				}
			}else if(Util.equals("OPH", flag)){
				if(!valid_oph_ptr(custId_s, dupNo_s, v)){
					checkMsg.add(desc+"["+val+"]不存在");
				}
			}else if(Util.equals("HPH", flag)){
				if(!valid_hph_ptr(custId_s, dupNo_s, v)){
					checkMsg.add(desc+"["+val+"]不存在");
				}				
			}else if(Util.equals("MPH", flag)){
				if(!valid_mph_ptr(custId_s, dupNo_s, v)){
					checkMsg.add(desc+"["+val+"]不存在");
				}				
			}else if(Util.equals("EML", flag)){
				if(!valid_eml_ptr(custId_s, dupNo_s, v)){
					checkMsg.add(desc+"["+val+"]不存在");
				}
			}			
		}
	}
	
	private boolean valid_adr_ptr(String custId_s, String dupNo_s, int v){
		List<Map<String, Object>> rowData = mis.find(null
				, new Object[] { "CMFCUS21", "CM21_ID_NO=? and CM21_DUP_NO=? and CM21_MADR_FLAG=?" }
				, new Object[] {custId_s, dupNo_s, v});		
		
		if(rowData.size()>0){
			return true;
		}		
		return false;
	}

	private boolean valid_oph_ptr(String custId_s, String dupNo_s, int v){
		List<Map<String, Object>> rowData = mis.find(null
				, new Object[] { "CMFCUS28", "CM28_ID_NO=? and CM28_DUP_NO=? and CM28_OPHONE_FLAG=?" }
				, new Object[] {custId_s, dupNo_s, v});		
		
		if(rowData.size()>0){
			return true;
		}		
		return false;
	}

	private boolean valid_hph_ptr(String custId_s, String dupNo_s, int v){
		List<Map<String, Object>> rowData = mis.find(null
				, new Object[] { "CMFCUS27", "CM27_ID_NO=? and CM27_DUP_NO=? and CM27_HPHONE_FLAG=?" }
				, new Object[] {custId_s, dupNo_s, v});		
		
		if(rowData.size()>0){
			return true;
		}	
		return false;
	}
	
	private boolean valid_mph_ptr(String custId_s, String dupNo_s, int v){
		List<Map<String, Object>> rowData = mis.find(null
				, new Object[] { "CMFCUS23", "CM23_ID_NO=? and CM23_DUP_NO=? and CM23_MPHONE_FLAG=?" }
				, new Object[] {custId_s, dupNo_s, v});		
		
		if(rowData.size()>0){
			return true;
		}	
		return false;
	}
	
	private boolean valid_eml_ptr(String custId_s, String dupNo_s, int v){
		List<Map<String, Object>> rowData = mis.find(null
				, new Object[] { "CMFCUS22", "CM22_ID_NO=? and CM22_DUP_NO=? and CM22_MADR_FLAG=?" }
				, new Object[] {custId_s, dupNo_s, v});		
		
		if(rowData.size()>0){
			return true;
		}		
		return false;
	}

	private BigDecimal valid_xls_BigDecimal(String val, String desc, List<String> checkMsg){
		if(Util.isEmpty(val)){
			return null;
		}
		if (!Util.isNumeric(val)) {
			checkMsg.add(desc+"需為數字!");
		}
		return CapMath.getBigDecimal(val);
	}
	private Integer valid_xls_Integer(String val, String desc, List<String> checkMsg){
		if(Util.isEmpty(val)){
			return null;
		}
		if (!Util.isNumeric(val)) {
			checkMsg.add(desc+"需為數字!");
		}
		return Util.parseInt(val);
	}
	private Date valid_xls_Date(String val, String desc, List<String> checkMsg){
		if(Util.isEmpty(val)){
			return null;			
		}
		Date r = null;
		String msg = "";
		try{
			r = CapDate.parseDate(val);
		}catch(Exception e){
			msg = ("請檢查"+desc+"的日期格式!");
		}
		if(!Util.isEmpty(val) && Util.isEmpty(msg) && r==null){
			//若輸入 2017-05-32, 沒有進到上面的 Exception
			//所以另外加判斷
			msg = desc+"["+val+"]日期無法判斷";
		}
		if(Util.isNotEmpty(msg)){
			checkMsg.add(msg);
		}
		return r;
	}

	/**
	 * ref LOAN.TEST.SOURCE(LNP0017)
	 * 			2700-CHK-TOT-TERM
	 */
	@SuppressWarnings("unused")
	private void F_2700_CHK_TOT_TERM(List<String> checkMsgE, String INTRT_CYCL
			, Date WK_B_DATE, Date WK_E_DATE, Integer XLS_WK_TOTAL, boolean isSub ){
		if(WK_B_DATE==null || WK_E_DATE==null || XLS_WK_TOTAL==null){
			return;
		}
		String desc = "到期日"+(isSub?"-分戶":"");
		Date WK_DUE_DATE_E = null;
		Date WK_DUE_DATE_B = null;
		int WK_TOTAL = 0;
		String calcStr = "";
		String calcUnit = "";
		if(Util.equals("1", INTRT_CYCL)){
			Date WK_PROC_DATE = null;
						
			String mmEDate = date_MM(WK_E_DATE);
			String ddEDate = date_dd(WK_E_DATE);
			
			if( ( CrsUtil.inCollection(mmEDate, new String[]{"01","03","05","07","08","10","12"}) && (CrsUtil.inCollection(ddEDate, new String[]{"31"}) ))
			|| ( CrsUtil.inCollection(mmEDate, new String[]{"04","06","09","11"}) && (CrsUtil.inCollection(ddEDate, new String[]{"30"}) ))
			|| ( CrsUtil.inCollection(mmEDate, new String[]{"02"}) && (CrsUtil.inCollection(ddEDate, new String[]{"28","29"}) ))
			|| (Util.equals(ddEDate, date_dd(WK_B_DATE)))){
				WK_PROC_DATE = WK_E_DATE;
			}else{
				WK_PROC_DATE = CapDate.shiftDays(WK_E_DATE, 1);
			}			                        
			int WK_YEAR = Util.parseInt(date_yyyy(WK_PROC_DATE))-Util.parseInt(date_yyyy(WK_B_DATE));
			int WK_MONTH = Util.parseInt(date_MM(WK_PROC_DATE))-Util.parseInt(date_MM(WK_B_DATE));
			WK_TOTAL = WK_YEAR*12 + WK_MONTH;
			
			
			int WK_DATE_B = Util.parseInt(date_dd(WK_B_DATE));
			int WK_DATE_E = Util.parseInt(date_dd(WK_PROC_DATE));
			if(WK_DATE_E - WK_DATE_B>0){
				++WK_TOTAL;
			}
			WK_DUE_DATE_E = CapDate.addMonth(WK_B_DATE, WK_TOTAL);
			WK_DUE_DATE_B = CapDate.addMonth(WK_B_DATE, WK_TOTAL-1);
			//======
			calcStr = XLS_WK_TOTAL+"(月)="+TWNDate.toAD(CapDate.addMonth(WK_B_DATE, XLS_WK_TOTAL));
			calcUnit = "月";
		}else if(Util.equals("2", INTRT_CYCL)){
			/*
				SELECT  DAYS('2017-03-15') - DAYS('2017-03-14') FROM   SYSIBM.SYSDUMMY1
				-----------
          				1     
          		
          				
          		<USER> <GROUP>('2017-04-20') + (240*14) days FROM SYSIBM.SYSDUMMY1   
			 */
			int WK_DAYS = CapDate.calculateDays(WK_E_DATE, WK_B_DATE);
			
			if(Util.notEquals(date_dd(WK_B_DATE), date_dd(WK_E_DATE))){
				++WK_DAYS;
			}
			WK_TOTAL = WK_DAYS/14+( (WK_DAYS%14>0) ?1:0);
			
			WK_DUE_DATE_E = CapDate.shiftDays(WK_B_DATE, WK_TOTAL*14);
			WK_DUE_DATE_B = CapDate.shiftDays(WK_B_DATE, (WK_TOTAL-1)*14);
			//======
			calcStr = XLS_WK_TOTAL+"(*14天)="+TWNDate.toAD(CapDate.shiftDays(WK_B_DATE, XLS_WK_TOTAL*14));
			calcUnit = "雙週";
		} 
		
		if(XLS_WK_TOTAL!=WK_TOTAL){
			checkMsgE.add("總期數為"+XLS_WK_TOTAL+"("+calcUnit+")，"+desc+"("+TWNDate.toAD(WK_E_DATE)+")輸入錯誤。"
					+TWNDate.toAD(WK_B_DATE)+"+"+calcStr);				 
		}
	}
	
	private void valid_AvgPay_EhPayCpt(List<String> checkMsgE
			, String avgPay, BigDecimal ehPayCpt
			, String label_avgPay2, String label_avgPay3, String label_ehPayCpt){
		if(Util.equals("2", avgPay)){ //本息平均
			if(ehPayCpt==null || (ehPayCpt!=null && ehPayCpt.compareTo(BigDecimal.ZERO)!=0)){
				checkMsgE.add(label_avgPay2+" 的 "+label_ehPayCpt+" 必須為0");
			}
		}else if(Util.equals("3", avgPay)){ //本金平均
			if(ehPayCpt!=null && ehPayCpt.compareTo(BigDecimal.ZERO)>0){
				
			}else{
				checkMsgE.add(label_avgPay3+" 的 "+label_ehPayCpt+" 需>0");
			}
		}
	}
	
	private void valid_baseDD_Date(List<String> checkMsgE
			, Integer dd, Date full_date
			, String label_dd, String label_full_date){
		boolean fail = true;
		if(full_date==null || dd==null){
			
		}else{
			String a = StringUtils.substring(Util.trim(TWNDate.toAD(full_date)), 8, 10);
			String b = Util.getRightStr("00"+dd, 2);
			if(Util.equals(a, b)){
				fail = false;
			}else{
				
			}
		}
		if(fail){
			checkMsgE.add(label_dd+"("+dd+") 需和 "+label_full_date+"("+TWNDate.toAD(full_date)+")的(日)相同");
		}
	}
	
	private String err_memo_s(String val, String desc){
		if(val.length() < 6){
			return (desc+"長度至少6碼");
		}
			
		if(_valid_memo_s_first6(val) || _valid_memo_s_first9(val)){
			return "";	
		}else{
			return (desc+"格式錯誤");
		}		
		
	}
	private void cfm_memo_s(String val, List<String> checkMsgI
			, Map<String, String> existMemoFirst6_or_9, Set<String> thisXls){
		if(thisXls.contains(val)){
			checkMsgI.add("戶號"+val+"重複(本次檔案)");
		}else if(existMemoFirst6_or_9.containsKey(val)){
			checkMsgI.add("戶號"+val+"重複("+existMemoFirst6_or_9.get(val)+")");
		}
	}
	private Map<String, String> existMemoFirst6_or_9(String cntrNo){
		Map<String, String> r = new HashMap<String, String>();
		
		Map<String, C160M03A> mainId_bean_map = new HashMap<String, C160M03A>();
		for(C160M03A c160m03a: c160m03aDao.findByCntrNo(cntrNo)){
			mainId_bean_map.put(c160m03a.getMainId(), c160m03a);
		}
		
		for(C160S03A c160s03a : c160s03aDao.findByCntrNo_hasMemo(cntrNo)){
			String mainId = c160s03a.getMainId();
			String msg = "seqNo="+c160s03a.getSeqNo();
			if(mainId_bean_map.containsKey(mainId)){
				C160M03A c160m03a = mainId_bean_map.get(mainId);
				msg = "批號:"+Util.trim(c160m03a.getPackNo())+"_"+Util.trim(c160s03a.getCustId_s());
			}
			r.put(fetch_first_6_or_9(c160s03a.getMemo_s()), msg);
		}

		return r;
	}
	
	private String fetch_first_6_or_9(String memo){
		//(108) 第 2841 號 ,  J-108-0285, 華固新天地地上權融資案,修改整批自動開戶之備註-分戶欄位
		if(_valid_memo_s_first6(memo)){
			return Util.trim(StringUtils.substring(memo, 0, 6));	
		}else if(_valid_memo_s_first9(memo)){
			return Util.trim(StringUtils.substring(memo, 0, 9));	
		}

		//default 
		return Util.trim(StringUtils.substring(memo, 0, 6));	
		
	}
	private boolean _valid_memo_s_first6(String val){
		String v1 = StringUtils.substring(val, 0, 1);
		String v23 = StringUtils.substring(val, 1, 3);
		String v4 = StringUtils.substring(val, 3, 4);
		String v56 = StringUtils.substring(val, 4, 6);
		//========
		boolean match_v1 = false;
		String[] v1_arr = {"A", "B", "C", "D", "E"};
		for(String each: v1_arr){
			if(Util.equals(each, v1)){
				match_v1 = true;
				break;
			}
		}
		
		if(match_v1 == false){
			return false;
		}
		//========
		if(!StringUtils.isNumeric(v23)){
			return false;
		}
		//========
		if(!Util.equals(v4, "-")){
			return false;
		}
		//========
		if(!StringUtils.isNumeric(v56)){
			return false;
		}
		//========
		return true;
	}
	private boolean _valid_memo_s_first9(String val){
//		if(val.startsWith(" ")){
//			return false;
//		}
//		
		if(Util.trim(val).length()<9){
			return false;	
		}
		
		if(_valid_memo_s_first6(StringUtils.substring(val, 3, 9))){ //檢核9碼的 末6碼
			return true;
		}
		return false;
	}
	
	private String date_yyyy(Date d){
		return StringUtils.substring(TWNDate.toAD(d), 0, 4);
	}
	private String date_MM(Date d){
		return StringUtils.substring(TWNDate.toAD(d), 5, 7);
	}
	private String date_dd(Date d){
		return StringUtils.substring(TWNDate.toAD(d), 8, 10);
	}
	private void chk_acctNo(Map<String, String> accNo_idDup_map, String raw_acctNo){
		String acctNo = Util.trim(raw_acctNo);
		if(Util.isEmpty(acctNo)){
			return;			 
		}
		if(accNo_idDup_map.containsKey(acctNo)){
			return;			 
		}
		
		Map<String, Object> sp_result = msps.callLNPS188(acctNo);
		String val = "E";
		String spOutArea = Util.trim((String) sp_result.get("SP_OUTPUT_AREA"));
		if ("YES".equals(sp_result.get("SP_RETURN")) && Util.notEquals(spOutArea, "")) {
			/*
			回應代碼　　　　　　　　　　　　　　　　　   
            '00' : NORMAL
			'01' : NO DATA
			'02' : DB2 ERROR
			'03' : INVALID AC-TYPE
			'04' : INVALID AC-NO
			'05' : INVALID FXRATE
			'06' : INVALID DATE
			'07' : INVALID COLLNO
			'10' : NO DATA (COLLCNTR)
			'12' : FILE ERROR
			'99' : INITIAL RETURN CODE            
			*/
			String first2char = spOutArea.substring(1, 3);			
			if(Util.equals("00", first2char)){
				val = spOutArea.substring(63, 74);	
			}else{
				val = first2char;
			}
		}
		accNo_idDup_map.put(acctNo, val);
	}
	
	private List<ELF533> toELF533(List<Map<String, Object>> rowData){
		List<ELF533> list = new ArrayList<ELF533>();
		for (Map<String, Object> row : rowData) {
			ELF533 model = new ELF533();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}
	
	private ELF533 _findELF533(String cntrNo, int seqNo){
		List<Map<String, Object>> rowData = mis.find(null
				, new Object[] { "ELF533", "elf533_cntrno=? and elf533_seq_no=? " }
				, new Object[] {cntrNo, seqNo});		
		List<ELF533> list = toELF533(rowData);
		if(list.size()==1){
			return list.get(0);
		}else{
			return null;
		}
	}
	
	@Override
	public void upELF533(C160M03A meta, List<C160S03A> list){		
		List<ELF533> data = new ArrayList<ELF533>();
		
		for(C160S03A c160s03a : list){
			String cntrNo = c160s03a.getCntrNo();
			int seqNo = c160s03a.getSeqNo();
			
			ELF533 elf533 = new ELF533();
			String elf533_loan_no = "";
			Timestamp elf533_tmestamp = meta.getApproveTime();
			Timestamp elf533_aloantimes = null;			
			//~~~
			ELF533 exist533 = _findELF533(cntrNo, seqNo);
			if(exist533!=null){
				if(Util.isNotEmpty(Util.trim(exist533.getElf533_loan_no()))){
					//已被引入 a-loan，不要蓋掉
					//不然無法 trace 問題
					continue;	
				}
				
				//若ELF500_TMESTAMP有值則保留上面的值
				elf533_loan_no = Util.trim(exist533.getElf533_loan_no());
				elf533_tmestamp = elf533.getElf533_tmestamp();
				elf533_aloantimes = elf533.getElf533_aloantimes();
			}
			//~~~
			elf533.setElf533_cntrno(cntrNo);        
			elf533.setElf533_seq_no(seqNo);
			elf533.setElf533_loan_no(elf533_loan_no);       
			elf533.setElf533_lnap("503");      
			elf533.setElf533_int_code(c160s03a.getInt_code());
			elf533.setElf533_int_sprd(c160s03a.getInt_sprd());
			elf533.setElf533_int_type(c160s03a.getInt_type());
			elf533.setElf533_intchg_type(c160s03a.getIntchg_type());
			elf533.setElf533_intchg_cycl(c160s03a.getIntchg_cycl());
			elf533.setElf533_fndsre(c160s03a.getFndSre());        
			elf533.setElf533_fund_type2(c160s03a.getFund_type2());  
			elf533.setElf533_lnpurs(c160s03a.getLnPurs());        
			elf533.setElf533_ln_purpose(c160s03a.getLn_purpose());    
			elf533.setElf533_monthcnt(c160s03a.getMonthCnt());   
			elf533.setElf533_due_dt(c160s03a.getDue_dt()); 
			elf533.setElf533_rt_dd(c160s03a.getRt_dd());   
			elf533.setElf533_nt_rt_dt(c160s03a.getNt_rt_dt());
			elf533.setElf533_int_rt_dd(c160s03a.getInt_rt_dd());
			elf533.setElf533_int_intrt_dt(c160s03a.getInt_intrt_dt());
			elf533.setElf533_avgpay(c160s03a.getAvgPay());			
			elf533.setElf533_ehpaycpt(c160s03a.getEhPayCpt());
			elf533.setElf533_intrt_cycl(c160s03a.getIntrt_cycl());
			elf533.setElf533_autorct(c160s03a.getAutoRct());			
			elf533.setElf533_rctdate(c160s03a.getRctDate());       
			elf533.setElf533_accno(c160s03a.getAccNo());      
			elf533.setElf533_swft(c160s03a.getSwft());      
			elf533.setElf533_rctamt(c160s03a.getRctAmt());   
			elf533.setElf533_autopay(c160s03a.getAutoPay());  
			elf533.setElf533_atpayno(c160s03a.getAtPayNo());    
			elf533.setElf533_custid_s(c160s03a.getCustId_s());        
			elf533.setElf533_dupno_s(c160s03a.getDupNo_s());
			elf533.setElf533_int_code_s(c160s03a.getInt_code_s());
			elf533.setElf533_int_sprd_s(c160s03a.getInt_sprd_s());
			elf533.setElf533_int_type_s(c160s03a.getInt_type_s());
			elf533.setElf533_intchg_type_s(c160s03a.getIntchg_type_s());
			elf533.setElf533_intchg_cycl_s(c160s03a.getIntchg_cycl_s());
			elf533.setElf533_monthcnt_s(c160s03a.getMonthCnt_s());   
			elf533.setElf533_due_dt_s(c160s03a.getDue_dt_s());   
			elf533.setElf533_rt_dd_s(c160s03a.getRt_dd_s());   
			elf533.setElf533_nt_rt_dt_s(c160s03a.getNt_rt_dt_s());   
			elf533.setElf533_int_rt_dd_s(c160s03a.getInt_rt_dd_s());   
			elf533.setElf533_int_intrt_dt_s(c160s03a.getInt_intrt_dt_s());   
			elf533.setElf533_avgpay_s(c160s03a.getAvgPay_s());     
			elf533.setElf533_ehpaycpt_s(c160s03a.getEhPayCpt_s());
			elf533.setElf533_intrt_cycl_s(c160s03a.getIntrt_cycl_s());   
			elf533.setElf533_autopay_s(c160s03a.getAutoPay_s());    
			elf533.setElf533_atpayno_s(c160s03a.getAtPayNo_s());    
			elf533.setElf533_cmpid_s(LMSUtil.getCustKey_len10custId(meta.getCustId(), meta.getDupNo()));
			elf533.setElf533_lotno_s(meta.getPackNo()); 
			elf533.setElf533_adr_ptr_s(c160s03a.getAdr_ptr_s());
			elf533.setElf533_oph_ptr_s(c160s03a.getOph_ptr_s());
			elf533.setElf533_hph_ptr_s(c160s03a.getHph_ptr_s());
			elf533.setElf533_mph_ptr_s(c160s03a.getMph_ptr_s());
			elf533.setElf533_eml_ptr_s(c160s03a.getEml_ptr_s());
			elf533.setElf533_memo_s(c160s03a.getMemo_s());
			elf533.setElf533_unpay_fg_s(c160s03a.getUnpay_fg_s());
			elf533.setElf533_teller(meta.getUpdater());    
			elf533.setElf533_supvno(meta.getApprover());
			elf533.setElf533_eloantimes(CapDate.getCurrentTimestamp());			
			elf533.setElf533_tmestamp(elf533_tmestamp);
			elf533.setElf533_aloantimes(elf533_aloantimes);
			elf533.setElf533_tax_rate(c160s03a.getTax_rate());
			elf533.setElf533_allow_beg(c160s03a.getAllow_beg());
			elf533.setElf533_allow_end(c160s03a.getAllow_end());
			//~~~
			data.add(elf533);	
		}
		
		if(data.size()>0){
			MISRows<ELF533> misRowsELF533 = new MISRows<ELF533>(
					ELF533.class);
			misRowsELF533.setValues(data);
			
			this.upMisToServer(misRowsELF533, "MIS");	
		}		
	}	
	
	@Override
	public void daoSaveC160M03A(C160M03A meta){
		c160m03aDao.save(meta);
	}

	@Override
	public boolean check_can_delElf533(List<C160S03A> list){
		for(C160S03A c160s03a: list){
			String cntrNo = c160s03a.getCntrNo();
			Integer seqNo = c160s03a.getSeqNo();
			//======
			List<Map<String, Object>> rowData = mis.find(null
					, new Object[] { "ELF533", "elf533_cntrno=? and elf533_seq_no=? " +
							"and (elf533_loan_no>'' or elf533_aloantimes is not null)" }
					, new Object[] {cntrNo, seqNo});
			List<ELF533> r = toELF533(rowData);
			if(r.size()>0){
				return false;
			}
		}
		return true;
	}
	
	@Override
	public void delElf533(List<C160S03A> list)
	throws CapException{
		for(C160S03A c160s03a: list){
			String cntrNo = c160s03a.getCntrNo();
			Integer seqNo = c160s03a.getSeqNo();
			//======
			int cnt = mis.delete(
					new Object[] { "MIS.ELF533", "elf533_cntrno=? and elf533_seq_no=? " +
							"and (elf533_aloantimes is null)" }
					, new Object[] {cntrNo, seqNo});
			if(cnt!=1){
				throw new CapException("["+cntrNo+", "+seqNo+"] can not delete", getClass() );
			}
		}
	}

	private boolean eq_sub(){
		return true;
	}
	private void eq_comp_sub_val(String comp_v, String sub_v
			, String comp_label, String sub_label, List<String> checkMsgE){
		if(!Util.equals(comp_v, sub_v)){
			checkMsgE.add(sub_label+"["+sub_v+"]應與 "+comp_label+"["+comp_v+"]相同");
		}
	}
	private void eq_comp_sub_val(Date d_comp_v, Date d_sub_v
			, String comp_label, String sub_label, List<String> checkMsgE){
		String comp_v = TWNDate.toAD(d_comp_v);
		String sub_v = TWNDate.toAD(d_sub_v);
		//~~~
		eq_comp_sub_val(comp_v, sub_v, comp_label, sub_label, checkMsgE);
	}
	private void eq_comp_sub_val(Integer i_comp_v, Integer i_sub_v
			, String comp_label, String sub_label, List<String> checkMsgE){
		String comp_v = String.valueOf(i_comp_v);
		String sub_v = String.valueOf(i_sub_v);
		//~~~
		eq_comp_sub_val(comp_v, sub_v, comp_label, sub_label, checkMsgE);
	}
	private void eq_comp_sub_val(BigDecimal b_comp_v, BigDecimal b_sub_v
			, String comp_label, String sub_label, List<String> checkMsgE){
		String comp_v = LMSUtil.pretty_numStr(b_comp_v);
		String sub_v = LMSUtil.pretty_numStr(b_sub_v);
		//~~~
		eq_comp_sub_val(comp_v, sub_v, comp_label, sub_label, checkMsgE);
	}

	@Override
	public void export_Superficies(ByteArrayOutputStream outputStream, String custId, 
			Date procDateB, Date procDateE, String tableNm, String formId) throws IOException{
		exportLNF916S_LNF918S_txt(outputStream, custId, procDateB, procDateE, tableNm,  formId);
	}
	
	private void exportLNF916S_LNF918S_txt(ByteArrayOutputStream outputStream, String custId, 
			Date procDateB, Date procDateE, String tableNm, String formId) throws IOException{
		String errMsg = "";
		if(Util.isEmpty(errMsg) && procDateB==null){
			errMsg = "起日空白";
		}

		if(Util.isEmpty(errMsg) && procDateE==null){
			errMsg = "迄日空白";
		}
		
		List<String> lines = new ArrayList<String>(); 
		//CLS9041FormHandler::S1TxtSequencs
		if(Util.isEmpty(errMsg)){
			String col_sep_sign = ",";
			if(Util.equals("LNF916S", tableNm)){
				int cnt_916s = 24;
				for(String[] arr :_rows_LNF916S_xls(mis.findLNF916S(procDateB, procDateE, custId, formId), cnt_916s)){
					lines.add(StringUtils.join(arr, col_sep_sign));
				}
			}else if(Util.equals("LNF917S", tableNm)){
				int cnt_917s = 14;
				for(String[] arr :_rows_LNF917S_xls(mis.findLNF917S(procDateB, procDateE, custId, formId), cnt_917s)){
					lines.add(StringUtils.join(arr, col_sep_sign));
				}
			}else if(Util.equals("LNF919S", tableNm)){
				int cnt_919s = 20;
				for(String[] arr :_rows_LNF919S_xls(mis.findLNF919S(procDateB, procDateE, custId, formId), cnt_919s)){
					lines.add(StringUtils.join(arr, col_sep_sign));
				}
			}
			
			if(lines.size()==0){
				lines.add("資料產生日期："+TWNDate.toAD(procDateB)+"~"+TWNDate.toAD(procDateE)
						+(custId.length()==0?"":"客戶ID："+ custId)
						+"，查到0筆資料");
			}
		}else{
			lines.add(errMsg);
		}
		IOUtils.writeLines(lines, "\r\n", outputStream, "Big5");
	}
	
	private String numStr(String v, int size){
		if(v.length()>=size){
			return v;
		}
		String prefix = StringUtils.repeat(" ", size);
		return Util.getRightStr(prefix+v, size);
		
	}
	private String numStr_15_2(String v){
		return numStr(v, 13);
	}
	
	private List<String[]> _rows_LNF916S_xls(List<LNF916S> list, int totalColSize){
		List<String[]> rows = new ArrayList<String[]>();  
		
		NumberFormat nf_6 = NumberFormat.getInstance();
		nf_6.setMaximumFractionDigits(6);
		nf_6.setMinimumFractionDigits(6);
		
		for(LNF916S bean:list){
			// ---
			String[] arr = new String[totalColSize];
			for (int i_col = 0; i_col < totalColSize; i_col++) {
				arr[i_col] = "";
			}
			int i=0;			
			//======
			arr[i++] = Util.addSpaceWithValue(Util.trim(TWNDate.toAD(bean.getLnf916s_proc_date())), 10); //資料產生日期
			arr[i++] = Util.addSpaceWithValue(Util.trim(bean.getLnf916s_formid()), 8); //收據FORM ID
			arr[i++] = Util.addSpaceWithValue(Util.trim(bean.getLnf916s_loan_no()), 14); //放款帳號
			arr[i++] = Util.addSpaceWithValue(Util.trim(bean.getLnf916s_loan_seq()), 10); //戶別
			arr[i++] = Util.addSpaceWithValue(Util.trim(bean.getLnf916s_cust_id()), 11); //客戶ID
			arr[i++] = fillString(Util.trim(bean.getLnf916s_cust_name()), 12); //客戶姓名
			arr[i++] = fillString(Util.trim(bean.getLnf916s_mail_name()), 50); //收件人姓名
			arr[i++] = fillString(Util.trim(bean.getLnf916s_addr1()), 40); //通訊地址1
			arr[i++] = fillString(Util.trim(bean.getLnf916s_addr2()), 62); //通訊地址2
			arr[i++] = Util.addSpaceWithValue(Util.trim(bean.getLnf916s_rt_date()), 7); //還款日期
			arr[i++] = Util.addSpaceWithValue(Util.trim(bean.getLnf916s_dt_tm()), 19); //計息起訖
			arr[i++] = Util.addSpaceWithValue(Util.trim(bean.getLnf916s_rt_term()), 7); //償還期數
			arr[i++] = numStr(nf_6.format(bean.getLnf916s_int_rate().doubleValue()), 9); //利率
			arr[i++] = numStr_15_2(LMSUtil.pretty_numStr(bean.getLnf916s_rt_int())); //期付金利息
			arr[i++] = numStr(Util.trim(bean.getLnf916s_ov_days()), 3); //逾期日數
			arr[i++] = numStr_15_2(LMSUtil.pretty_numStr(bean.getLnf916s_accu_int())); //累計應收利息
			arr[i++] = numStr_15_2(LMSUtil.pretty_numStr(bean.getLnf916s_int_tot())); //利息合計
			arr[i++] = numStr_15_2(LMSUtil.pretty_numStr(bean.getLnf916s_lst_bal())); //上期本金餘額
			arr[i++] = numStr_15_2(LMSUtil.pretty_numStr(bean.getLnf916s_rt_bal())); //期付金本金
			arr[i++] = numStr_15_2(LMSUtil.pretty_numStr(bean.getLnf916s_cur_bal())); //本期償還後本金餘額
			arr[i++] = numStr_15_2(LMSUtil.pretty_numStr(bean.getLnf916s_tot_amt())); //總計需繳金額
			arr[i++] = Util.addSpaceWithValue(Util.trim(bean.getLnf916s_dp_act_no()), 11); //存款帳號
			arr[i++] = numStr_15_2(LMSUtil.pretty_numStr(bean.getLnf916s_1st_bal())); //初貸金額
			arr[i++] = fillString(Util.trim(bean.getLnf916s_grp_name()), 42); //建商名稱
			// ---
			rows.add(arr);
		}
		return rows;
	}
	
	private List<String[]> _rows_LNF917S_xls(List<LNF917S> list, int totalColSize){
		List<String[]> rows = new ArrayList<String[]>();  
		
		NumberFormat nf_6 = NumberFormat.getInstance();
		nf_6.setMaximumFractionDigits(6);
		nf_6.setMinimumFractionDigits(6);
		
		for(LNF917S bean:list){
			// ---
			String[] arr = new String[totalColSize];
			for (int i_col = 0; i_col < totalColSize; i_col++) {
				arr[i_col] = "";
			}
			int i=0;			
			//======
			arr[i++] = Util.addSpaceWithValue(Util.trim(TWNDate.toAD(bean.getLnf917s_proc_date())), 10); //資料產生日期
			arr[i++] = Util.addSpaceWithValue(Util.trim(bean.getLnf917s_formid()), 8); //收據FORM ID
			arr[i++] = Util.addSpaceWithValue(Util.trim(bean.getLnf917s_loan_no()), 14); //放款帳號
			arr[i++] = Util.addSpaceWithValue(Util.trim(bean.getLnf917s_loan_seq()), 10); //戶別
			arr[i++] = Util.addSpaceWithValue(Util.trim(bean.getLnf917s_cust_id()), 11); //客戶ID
			arr[i++] = fillString(Util.trim(bean.getLnf917s_cust_name()), 50); //客戶姓名
			arr[i++] = fillString(Util.trim(bean.getLnf917s_mail_name()), 50); //收件人姓名
			arr[i++] = fillString(Util.trim(bean.getLnf917s_addr1()), 82); //通訊地址1
			arr[i++] = fillString(Util.trim(bean.getLnf917s_addr2()), 82); //通訊地址2
			arr[i++] = Util.addSpaceWithValue(Util.trim(TWNDate.toAD(bean.getLnf917s_int_date())), 10); //利率調整日
			arr[i++] = fillString(Util.trim(bean.getLnf917s_intcd_txt()), 50); //利率代碼中文
			arr[i++] = numStr(nf_6.format(bean.getLnf917s_int_rate_o().doubleValue()), 9); //調整前利率
			arr[i++] = numStr(nf_6.format(bean.getLnf917s_int_rate_n().doubleValue()), 9); //調整後利率
			arr[i++] = fillString(Util.trim(bean.getLnf917s_grp_name()), 42); //建商名稱
			// ---
			rows.add(arr);
		}
		return rows;
	}
	
	private List<String[]> _rows_LNF919S_xls(List<LNF919S> list, int totalColSize){
		List<String[]> rows = new ArrayList<String[]>();  

		NumberFormat nf_6 = NumberFormat.getInstance();
		nf_6.setMaximumFractionDigits(6);
		nf_6.setMinimumFractionDigits(6);
		
		for(LNF919S bean:list){
			// ---
			String[] arr = new String[totalColSize];
			for (int i_col = 0; i_col < totalColSize; i_col++) {
				arr[i_col] = "";
			}
			int i=0;			
			//======
			arr[i++] = Util.addSpaceWithValue(Util.trim(TWNDate.toAD(bean.getLnf919s_proc_date())), 10); //資料產生日期
			arr[i++] = Util.addSpaceWithValue(Util.trim(bean.getLnf919s_formid()), 8); //攤還表FORM ID
			arr[i++] = Util.addSpaceWithValue(Util.trim(bean.getLnf919s_loan_no()), 14); //放款帳號
			arr[i++] = numStr(Util.trim(bean.getLnf919s_rt_term()), 3); //償還期數
			arr[i++] = Util.addSpaceWithValue(Util.trim(bean.getLnf919s_loan_seq()), 10); //戶別
			arr[i++] = Util.addSpaceWithValue(Util.trim(bean.getLnf919s_cust_id()), 11); //客戶ID
			arr[i++] = fillString(Util.trim(bean.getLnf919s_cust_name()), 50); //客戶姓名
			arr[i++] = Util.addSpaceWithValue(Util.trim(bean.getLnf919s_zip()), 3); //郵遞區號
			arr[i++] = fillString(Util.trim(bean.getLnf919s_addr1()), 82); //通訊地址1
			arr[i++] = fillString(Util.trim(bean.getLnf919s_addr2()), 82); //通訊地址2
			arr[i++] = numStr(Util.trim(bean.getLnf919s_left_term()), 3); //剩餘期數
			arr[i++] = numStr_15_2(LMSUtil.pretty_numStr(bean.getLnf919s_b_bal())); //上期本金餘額
			arr[i++] = Util.addSpaceWithValue(Util.trim(TWNDate.toAD(bean.getLnf919s_rt_date())), 10); //應還款日期
			arr[i++] = numStr(nf_6.format(bean.getLnf919s_int_rate().doubleValue()), 9); //利率
			arr[i++] = numStr_15_2(LMSUtil.pretty_numStr(bean.getLnf919s_rt_int())); //當期應繳利息
			arr[i++] = numStr_15_2(LMSUtil.pretty_numStr(bean.getLnf919s_rt_bal())); //當期應繳本金
			arr[i++] = numStr_15_2(LMSUtil.pretty_numStr(bean.getLnf919s_accu_int())); //累計應收利息
			arr[i++] = numStr_15_2(LMSUtil.pretty_numStr(bean.getLnf919s_rt_amt())); //應繳期付金合計
			arr[i++] = numStr_15_2(LMSUtil.pretty_numStr(bean.getLnf919s_e_bal())); //本期償還後本金餘額
			arr[i++] = Util.addSpaceWithValue(Util.trim(bean.getLnf919s_dp_act_no()), 1); //存款帳號
			// ---
			rows.add(arr);
		}
		return rows;
		
	}
	
	private String fillString(String txt, int len) {
		try {
			int length = txt.getBytes("BIG5").length;
			if (length > len) {
				txt = new String(txt.getBytes("BIG5"), 0, len, "BIG5");
			} else {
				txt = new String(
						(txt + StringUtils.repeat(" ", len)).getBytes("BIG5"),
						0, len, "BIG5");
			}
			return txt;
		} catch (UnsupportedEncodingException e) {
			logger.error(e.toString());
		}
		return StringUtils.repeat(" ", len);
	}

	private List<Object>  _krm040_sumArr(String custId, String dupNo, String prodId, Date _date){
		String qDate = Util.trim(TWNDate.toTW(_date));
		List<Map<String, Object>>  list = ejcicService.getKRM040_data(custId, prodId, qDate);
		
		int PAY_STAT_3_cnt = 0;
		int PAY_STAT_4_cnt = 0;
		int CASH_LENT_cnt = 0;
		int PAY_STAT_2_cnt = 0;
		Map<String, BigDecimal> revol_bal_ym_value = new HashMap<String, BigDecimal>();
		BigDecimal revol_bal_chose = BigDecimal.ZERO;
		
		int PAY_STAT_1N_cnt = 0;
		int PAY_STAT_XX_cnt = 0;
		int total_cnt = 0;

		for(Map<String, Object> rowMap : list){
			++ total_cnt;
			//=========
			String PAY_STAT = Util.trim(MapUtils.getString(rowMap, "PAY_STAT"));
			String PAY_CODE = Util.trim(MapUtils.getString(rowMap, "PAY_CODE"));
			String BILL_DATE = Util.trim(MapUtils.getString(rowMap, "BILL_DATE"));
			String BILL_DATE_YM = Util.trim(StringUtils.substring(BILL_DATE, 0, 5));
			if(Util.equals("4", PAY_STAT)){
				PAY_STAT_4_cnt++;
			}else if(Util.equals("3", PAY_STAT)){
				PAY_STAT_3_cnt++;
			}else if(Util.equals("2", PAY_STAT)){
				PAY_STAT_2_cnt++;
			}else if(Util.equals("1", PAY_STAT)){
				if(Util.equals("N", PAY_CODE)){
					PAY_STAT_1N_cnt++;
				}
			}else if(Util.equals("X", PAY_STAT)){
				if(Util.equals("X", PAY_CODE)){
					PAY_STAT_XX_cnt++;
				}
			}
			//=========
			BigDecimal cash_lent = (BigDecimal)MapUtils.getObject(rowMap, "CASH_LENT");
			if(cash_lent!=null && cash_lent.compareTo(BigDecimal.ZERO)>0){
				CASH_LENT_cnt++;
			}
			BigDecimal revol_bal = (BigDecimal)MapUtils.getObject(rowMap, "REVOL_BAL");
			if(!revol_bal_ym_value.containsKey(BILL_DATE_YM)){
				revol_bal_ym_value.put(BILL_DATE_YM, BigDecimal.ZERO);
			}
			revol_bal_ym_value.put(BILL_DATE_YM, revol_bal_ym_value.get(BILL_DATE_YM).add(revol_bal));
		}
		if(revol_bal_ym_value.size()>0){
			revol_bal_chose = new TreeSet<BigDecimal>(revol_bal_ym_value.values()).last();
			
			logger.trace("_krm040_sumArr_0["+custId+"]ym_bal="+revol_bal_ym_value); 
		}
		logger.trace("_krm040_sumArr_1["+custId+"][total="+total_cnt+"]"
				+CASH_LENT_cnt+", "+PAY_STAT_3_cnt+", "+PAY_STAT_4_cnt+", "+PAY_STAT_2_cnt+", "
				+revol_bal_chose+", "
				+PAY_STAT_1N_cnt+", "+PAY_STAT_XX_cnt); 
		
		List<Object> result_list = new ArrayList<Object>(); // J-107-0130 顯示KRM040的彙總統計[預借現金, 逾期, 1:全額繳清, 2:不須繳款]
		result_list.add(CASH_LENT_cnt>0?"Y":"");
		result_list.add(PAY_STAT_3_cnt>0?"Y":"");
		result_list.add(PAY_STAT_4_cnt>0?"Y":"");		
		result_list.add(PAY_STAT_2_cnt>0?"Y":"");
		if(true){
			if(revol_bal_chose.compareTo(BigDecimal.ZERO)>0){
				result_list.add(revol_bal_chose);		
			}else{
				result_list.add("");
			}
		}
		result_list.add((PAY_STAT_1N_cnt>0 && total_cnt==PAY_STAT_1N_cnt+PAY_STAT_XX_cnt)?"Y":"");
		result_list.add((PAY_STAT_XX_cnt>0 && total_cnt==PAY_STAT_XX_cnt)?"Y":"");
		
		logger.trace("_krm040_sumArr_2["+custId+"][total="+total_cnt+"]"+StringUtils.join(result_list, ", ")); 
		return result_list;
	}
	
	@Override
	public void sync_C160M01A_totAmt(String mainId){
		C160M01A c160m01a = clsService.findC160M01A_mainId(mainId);
		if(c160m01a!=null && Util.equals(UtilConstants.Usedoc.caseType2.一般, c160m01a.getCaseType()) ){
			BigDecimal totAmt = null;
			//====================
			Map<String, String> l140m01a_mainId_map_l120m01a_mainId = new HashMap<String, String>(); 
			for(C160M01B c160m01b : clsService.findC160M01B_mainId(c160m01a.getMainId())){
				L140M01A l140m01a = clsService.findL140M01A_mainId(c160m01b.getRefmainId());
				if(l140m01a!=null){
					L120M01C l120m01c = l140m01a.getL120m01c();
					if(l120m01c!=null){
						l140m01a_mainId_map_l120m01a_mainId.put(c160m01b.getRefmainId(), l120m01c.getMainId());
					}
				}				
			}
			
			//1.要在同一份簽報書
			TreeSet<String> l120m01a_set = new TreeSet<String>();
			for(String k : l140m01a_mainId_map_l120m01a_mainId.keySet()){
				String l120m01a_mainId = l140m01a_mainId_map_l120m01a_mainId.get(k);
				l120m01a_set.add(l120m01a_mainId);
			}
			if(l120m01a_set.size()==1){
				/*
				 當1份簽報書, 有N個額度(非不變, 非取消)
				 ● 一次動用N個(全部動用)
				 ● 一次動用(N-X)個(部分動用)
				 
				 參考 CLS1161GridHandler :: L140M01AQuery(...)
				*/
				String l120m01a_mainId = l120m01a_set.first();
				L120M01A l120m01a = clsService.findL120M01A_mainId(l120m01a_mainId);
				String itemType = lmsService.checkL140M01AItemType(l120m01a);
				
				Set<String> approvedL140M01A = new HashSet<String>();
				Map<String, BigDecimal> approvedL140M01A_LoanTotAmt_map = new HashMap<String, BigDecimal>();
				boolean allTWDLoanTotCurr = true;
				int notChangeCnt = 0;
				for(L140M01A l140m01a:l140m01aDao.findL140m01aListByL120m01cMainId(l120m01a_mainId
						, itemType, FlowDocStatusEnum.已核准.getCode())){
					if(Util.equals(UtilConstants.Cntrdoc.Property.取消, l140m01a.getProPerty())){
						continue;
					}
					if(Util.equals(UtilConstants.Cntrdoc.Property.不變, l140m01a.getProPerty())){
						++notChangeCnt;
						continue;
					}
					if(l140m01a.getCurrentApplyAmt()!=null 
							&& l140m01a.getCurrentApplyAmt().compareTo(BigDecimal.ZERO)>0){
						approvedL140M01A.add(l140m01a.getMainId());
						approvedL140M01A_LoanTotAmt_map.put(LMSUtil.getCustKey_len10custId(l140m01a.getCustId(), l140m01a.getDupNo()), l140m01a.getLoanTotAmt());
						String loanTotCurr = Util.trim(l140m01a.getLoanTotCurr());
						if(!Util.isEmpty(loanTotCurr) && !Util.equals(loanTotCurr, "TWD")  ){
							allTWDLoanTotCurr = false;
						}
					}
				}
				
				if(notChangeCnt > 0){
					/*
					 * 若1額度續約, 1額度不變 => 授信額度合計金額 =[續約+不變]
					 */
					totAmt = null;				
				}else{
					Set<String> c160 = l140m01a_mainId_map_l120m01a_mainId.keySet();
					if(LMSUtil.elm_onlyLeft(approvedL140M01A, c160).size()>0
							|| LMSUtil.elm_onlyRight(approvedL140M01A, c160).size()>0){
						//部分動用
					}else{
						if(allTWDLoanTotCurr){
							if(approvedL140M01A_LoanTotAmt_map.size()>0){
								totAmt = BigDecimal.ZERO;
								/*
								 * 一份簽報書, 甲、乙等2個人共簽
								 * 甲的額度[023, 024]
								 * 乙的額度[035]
								 * => 甲、乙的 LoanTotAmt 各不相同
								 */
								for(String idDup : approvedL140M01A_LoanTotAmt_map.keySet()){
									BigDecimal approvedL140M01A_LoanTotAmt = approvedL140M01A_LoanTotAmt_map.get(idDup);
									if(approvedL140M01A_LoanTotAmt!=null){
										totAmt = totAmt.add(approvedL140M01A_LoanTotAmt);
									}
								}
							}							
						}else{
							totAmt = null;//多幣別
						}
					}	
				}
			}else{
				totAmt = null;
			}
			
			if(totAmt!=null){
				//由 DEC(17, 2) -> DEC(15,0)
				totAmt = Arithmetic.round(totAmt, 0);
			}
			//====================
			c160m01a.setTotAmt(totAmt);
			c160m01a.setBfTotAmt(totAmt);
			clsService.save(c160m01a);
		}
	}
	//J-108-0217_10702_B1001 新增特定金錢信託受益權自行設質擔保授信 檢核
	public boolean getProjClassFromL250M01A(String oid){
		boolean checkprojClass=false;
		
		if(!Util.isEmpty(oid)){
			L250M01A meta = lms2501Service.findModelByOid(L250M01A.class,oid);
			List<L250M01B> l250m01bs = (List<L250M01B>) lms2501Service
				.findListByMainId(L250M01B.class, meta.getMainId());
			
			for(L250M01B l250m01b:l250m01bs){
				String reMainId=l250m01b.getReMainId();
				L140M01A l140m01a = findModelByMainId(L140M01A.class, reMainId);
				String projClass = l140m01a.getProjClass();
				if(Util.equals(projClass, LMSUtil.特定金錢信託受益權自行設質擔保授信)){
					checkprojClass=true;
				}
			}
		}
		return checkprojClass;
	}
	
	public void saveIVRFlag(String oid, List<String> addIVRList) throws CapException{
		try{
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			for(String data:addIVRList)
			{
				String[] eachdata=data.split(";");
				int cnt = (eachdata==null?0:eachdata.length);
				if(cnt==2){
					String newCustId = cnt > 0 ? eachdata[0] : "";
					String newIVRFlag =  cnt > 1 ? eachdata[1] : "";
					L250M01A meta = lms2501Service.findModelByOid(L250M01A.class,oid);
					List<L250M01B> l250m01bs = (List<L250M01B>) lms2501Service
						.findListByMainId(L250M01B.class, meta.getMainId());
					for(L250M01B l250m01b:l250m01bs){
						String reMainId=l250m01b.getReMainId();
						L140M01A l140m01a = findModelByMainId(L140M01A.class, reMainId);
						String custId = l140m01a.getCustId();
						String projClass = l140m01a.getProjClass();
						if(newCustId.equals(custId) && Util.equals(projClass, LMSUtil.特定金錢信託受益權自行設質擔保授信))
						{
							String ivrFlag=l250m01b.getIVRFlag();
							if(ivrFlag==null){
								l250m01b.setIVRFlag(newIVRFlag);
							}
							else if (ivrFlag !=null && !ivrFlag.contains(newIVRFlag)){
								l250m01b.setIVRFlag(Util.trim(ivrFlag)+ "," +newIVRFlag);
							}
							l250m01b.setUpdater( user.getUserId());
							l250m01b.setUpdateTime(CapDate.getCurrentTimestamp());
							lms2501Service.save(l250m01b);
						}
					}
				}
			}
		} catch (Throwable t1) {
			throw new CapMessageException(t1.getMessage(), getClass());
		}
	}
	
	public void deleteIVRFlag(String oid, String deleteCustId, String fileName) throws CapException{
		try{
			if(!Util.isEmpty(fileName)){
				MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
				L250M01A meta = lms2501Service.findModelByOid(L250M01A.class,oid);
				List<L250M01B> l250m01bs = (List<L250M01B>) lms2501Service
				.findListByMainId(L250M01B.class, meta.getMainId());
				for(L250M01B l250m01b:l250m01bs){
					String reMainId=l250m01b.getReMainId();
					L140M01A l140m01a = findModelByMainId(L140M01A.class, reMainId);
					String projClass = l140m01a.getProjClass();
					String custId=l140m01a.getCustId();
					String ivrFlag=l250m01b.getIVRFlag();
					if(Util.equals(projClass, LMSUtil.特定金錢信託受益權自行設質擔保授信) && custId.equals(deleteCustId) && ivrFlag !=null && ivrFlag.contains(fileName))
					{
						if(ivrFlag.contains(","))
						{
							l250m01b.setIVRFlag(ivrFlag.replace(fileName + ",", "").replace("," + fileName, ""));
						}
						else
						{
							l250m01b.setIVRFlag(null);
						}
						l250m01b.setUpdater( user.getUserId());
						l250m01b.setUpdateTime(CapDate.getCurrentTimestamp());
						lms2501Service.save(l250m01b);
					}
				}
			}
		} catch (Throwable t1) {
			throw new CapMessageException(t1.getMessage(), getClass());
		}
	}
	
	public List<Map<String, Object>> getIVRgrid(String oid) throws CapException{
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		try{
			L250M01A meta = lms2501Service.findModelByOid(L250M01A.class,oid);
			List<L250M01B> l250m01bs = (List<L250M01B>) lms2501Service
				.findListByMainId(L250M01B.class, meta.getMainId());
			
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			String lightId =user.getLightId();
			String userId =user.getUserId();
			String url = sysParameterService.getParamValue(SysParamConstants.IVR_REC_GW_URL);
			
			
			for(L250M01B l250m01b:l250m01bs){
				String reMainId=l250m01b.getReMainId();
				L140M01A l140m01a = findModelByMainId(L140M01A.class, reMainId);
				if(!Util.isEmpty(l250m01b.getIVRFlag())){
					String[] IVRFlag=l250m01b.getIVRFlag()!=null ? l250m01b.getIVRFlag().split(",") : null;
					for(String ivrFlag:IVRFlag){
						Map<String, Object> row = new HashMap<String, Object>();
						row.put("custId", Util.trim(l140m01a.getCustId()));
						row.put("custName", Util.trim(l140m01a.getCustName()));
						row.put("record_FileName", Util.trim(ivrFlag));
						row.put("record_LightId", lightId);
						row.put("record_UserID", userId);
						row.put("record_Url", url);
						row.put("record_FileName2", Util.trim(ivrFlag).replace(".wav", ""));
						list.add(row);
					}
				}
			}
		} catch (Throwable t1) {
			throw new CapMessageException(t1.getMessage(), getClass());
		}
		
		return list;
	}
	
	public List<Map<String, Object>> getIVRFiltergrid(String oid) throws CapException{

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		try{
			L250M01A meta = lms2501Service.findModelByOid(L250M01A.class,oid);
			String SRCMAINID =meta.getSrcMainId();
	
			List<L250M01B> l250m01bs = (List<L250M01B>) lms2501Service
				.findListByMainId(L250M01B.class, meta.getMainId());
			
			String branCode = user.getUnitNo();
			Date sDate = new Date();
	        Calendar rightNow = Calendar.getInstance();  
	        rightNow.setTime(sDate);  
	        rightNow.add(Calendar.MONTH, NumberUtils.toInt(sysParameterService.getParamValue(SysParamConstants.IVR_Query_StartMonth)));
	        sDate = rightNow.getTime();  
			Date dDate = new Date();
			SimpleDateFormat ft = new SimpleDateFormat ("yyyyMMdd");
			String startDate = ft.format(sDate);
			String endDate = ft.format(dDate);
			String lightId =user.getLightId();
			String userId =user.getUserId();
			//branCode="";
			
			for(L250M01B l250m01b:l250m01bs){
				String reMainId=l250m01b.getReMainId();
				L140M01A l140m01a = findModelByMainId(L140M01A.class, reMainId);
				String custId = l140m01a.getCustId();
				String projClass = l140m01a.getProjClass();
					
				IVRGwReqMessage req = new IVRGwReqMessage(custId,startDate, endDate, lightId, userId , branCode);
				if(Util.notEquals(custId, "") && Util.notEquals(startDate, "") && Util.notEquals(endDate, "") && Util.equals(projClass, LMSUtil.特定金錢信託受益權自行設質擔保授信)){
					List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
					lists=cls3301Service.find(req);
					for(Map<String, Object> each:lists){
						list.add(each);
					}
				}
			}
		} catch (Throwable t1) {
			throw new CapMessageException(t1.getMessage(), getClass());
		}
		return list;
	}


	private Map<String, C160M01C> convert_C160M01C_itemCode_obj(List<C160M01C> list){
		Map<String, C160M01C> map = new HashMap<String, C160M01C>();
		for(C160M01C c160m01c : list){
			map.put(c160m01c.getItemCode(), c160m01c);
		}
		return map;
	}
	
	@Override
	public boolean is_only_apply_prod69(String c160_mainId){
		int cnt_prod69 = 0;
		int cnt_prodOthers = 0;
		Set<String> prod69_idDup = new HashSet<String>();
		for(C160M01B c160m01b : clsService.findC160M01B_mainId(c160_mainId)){
			L140M01A l140m01a = clsService.findL140M01A_mainId(c160m01b.getRefmainId());
			if(l140m01a!=null && clsService.is_only_apply_prod69(l140m01a)){
				++cnt_prod69;
				prod69_idDup.add(LMSUtil.getCustKey_len10custId(l140m01a.getCustId(), l140m01a.getDupNo()));
			}else{
				++cnt_prodOthers;
			}				
		}
		if(cnt_prod69>0 && cnt_prodOthers==0){
			return true;
		}
		return false;
	}
	
	@Override
	public String useProd69Fmt(String c160_mainId){
		String useProd69Fmt = "N";
		if(is_only_apply_prod69(c160_mainId)){
			List<C160M01C> list = clsService.findC160M01C_mainId(c160_mainId);
			Map<String, C160M01C> map_c160s01c = convert_C160M01C_itemCode_obj(list);
			if(map_c160s01c.containsKey("47")
					&& map_c160s01c.containsKey("48")
					&& map_c160s01c.containsKey("49")
					&& map_c160s01c.containsKey("50")){
				useProd69Fmt = "Y";
			}
		}
		return useProd69Fmt;
	}
	//J-109-0150_10702_B1001 Web e-Loan IVR頁籤由模擬動審移至動審表
	public boolean getProjClassFromC160M01A(String oid){
		boolean checkprojClass=false;

		if(!Util.isEmpty(oid)){
			C160M01A meta = findModelByOid(C160M01A.class,oid);
			if(meta == null){
				return checkprojClass;
			}
			List<C160M01B> c160m01bs = (List<C160M01B>) findListByMainId(C160M01B.class, meta.getMainId());

			for(C160M01B c160m01b:c160m01bs){
				String reMainId=c160m01b.getRefmainId();
				if(Util.isEmpty(reMainId)){
					continue;
				}
				L140M01A l140m01a = l140m01aDao.findByMainId(reMainId);
				if(l140m01a == null){
					continue;
				}
				String projClass = l140m01a.getProjClass();
				if(Util.equals(projClass, LMSUtil.特定金錢信託受益權自行設質擔保授信)){
					checkprojClass=true;
				}

				//J-110-0283_10702_B1001 Web e-Loan IVR動審表引入月份放寬，比對與前次額度明細表幣別不同
//				if(LMSUtil.isContainValue(Util.trim(l140m01a.getProPerty()),
//						UtilConstants.Cntrdoc.Property.變更條件)
//						|| LMSUtil.isContainValue(Util.trim(l140m01a.getProPerty()),
//						UtilConstants.Cntrdoc.Property.增額)){
//					Map<String, Object> map = eloandbBASEService
//							.findLastTimeCaseByCutIdAndCntrNoOrdByUpdateTime(
//									l140m01a.getCustId(), l140m01a.getDupNo(), Util.trim(l140m01a.getCntrNo()), UtilConstants.Casedoc.DocType.個金, Util.trim(l140m01a.getMainId()));
//					L140M01A l140m01a_old = null;
//					String oldL140M01AmainId = "";
//					if (map != null) {
//						oldL140M01AmainId = Util.trim(map.get("MAINID"));
//						l140m01a_old = findModelByMainId(L140M01A.class,oldL140M01AmainId);
//					}
//					if(Util.isNotEmpty(l140m01a_old)){
//						if(!Util.equals(l140m01a.getCurrentApplyCurr(),l140m01a_old.getCurrentApplyCurr())){
//							checkprojClass = true;
//						}
//					}
//				}
			}
		}
		return checkprojClass;
	}

	public void saveIVRFlag2(String oid, List<String> addIVRList) throws CapException{
		try{
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			for(String data:addIVRList)
			{
				String[] eachdata=data.split(";");
				int cnt = (eachdata==null?0:eachdata.length);
				if(cnt==2){
					String newCustId = cnt > 0 ? eachdata[0] : "";
					String newIVRFlag =  cnt > 1 ? eachdata[1] : "";
					C160M01A meta = findModelByOid(C160M01A.class,oid);
					if(meta == null){
						continue;
					}
					List<C160M01B> c160m01bs = (List<C160M01B>) findListByMainId(C160M01B.class, meta.getMainId());
					for(C160M01B c160m01b:c160m01bs){
						String reMainId=c160m01b.getRefmainId();
						if(Util.isEmpty(reMainId)){
							continue;
						}
						L140M01A l140m01a = l140m01aDao.findByMainId(reMainId);
						if(l140m01a == null){
							continue;
						}
						String custId = l140m01a.getCustId();
						String projClass = l140m01a.getProjClass();
						
//						custId="A123456789";
						
						if(newCustId.equals(custId) && Util.equals(projClass, LMSUtil.特定金錢信託受益權自行設質擔保授信))
						{
							String ivrFlag=c160m01b.getIVRFlag();
							if(ivrFlag==null){
								c160m01b.setIVRFlag(newIVRFlag);
							}
							else if (ivrFlag !=null && !ivrFlag.contains(newIVRFlag)){
								c160m01b.setIVRFlag(Util.trim(ivrFlag)+ "," +newIVRFlag);
							}
							c160m01b.setUpdater( user.getUserId());
							c160m01b.setUpdateTime(CapDate.getCurrentTimestamp());
							this.save(c160m01b);
						}
					}
				}
			}
		} catch (Throwable t1) {
			throw new CapMessageException(t1.getMessage(), getClass());
		}
	}
	
	public void deleteIVRFlag2(String oid, String deleteCustId, String fileName) throws CapException{
		try{
			if(!Util.isEmpty(fileName)){
				MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
				C160M01A meta = findModelByOid(C160M01A.class,oid);
				if(meta == null){
					return;
				}
				List<C160M01B> c160m01bs = (List<C160M01B>) findListByMainId(C160M01B.class, meta.getMainId());
				for(C160M01B c160m01b:c160m01bs){
					String reMainId=c160m01b.getRefmainId();
					if(Util.isEmpty(reMainId)){
						continue;
					}
					L140M01A l140m01a = l140m01aDao.findByMainId(reMainId);
					if(l140m01a == null){
						continue;
					}
					String projClass = l140m01a.getProjClass();
					String custId=l140m01a.getCustId();
					String ivrFlag=c160m01b.getIVRFlag();
					if(Util.equals(projClass, LMSUtil.特定金錢信託受益權自行設質擔保授信) && custId.equals(deleteCustId) && ivrFlag !=null && ivrFlag.contains(fileName))
					{
						if(ivrFlag.contains(","))
						{
							c160m01b.setIVRFlag(ivrFlag.replace(fileName + ",", "").replace("," + fileName, ""));
						}
						else
						{
							c160m01b.setIVRFlag(null);
						}
						c160m01b.setUpdater( user.getUserId());
						c160m01b.setUpdateTime(CapDate.getCurrentTimestamp());
						this.save(c160m01b);
					}
				}
			}
		} catch (Throwable t1) {
			throw new CapMessageException(t1.getMessage(), getClass());
		}
	}
	
	public List<Map<String, Object>> getIVRgrid2(String oid) throws CapException{
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		try{
			C160M01A meta = findModelByOid(C160M01A.class,oid);
			if(meta == null){
				return list;
			}
			List<C160M01B> c160m01bs = (List<C160M01B>) findListByMainId(C160M01B.class, meta.getMainId());
			
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			String lightId =user.getLightId();
			String userId =user.getUserId();
			String url = sysParameterService.getParamValue(SysParamConstants.IVR_REC_GW_URL);
			
			
			for(C160M01B c160m01b:c160m01bs){
				String reMainId=c160m01b.getRefmainId();
				if(Util.isEmpty(reMainId)){
					continue;
				}
				L140M01A l140m01a = l140m01aDao.findByMainId(reMainId);
				if(l140m01a == null){
					continue;
				}
				if(!Util.isEmpty(c160m01b.getIVRFlag())){
					String[] IVRFlag=c160m01b.getIVRFlag()!=null ? c160m01b.getIVRFlag().split(",") : null;
					for(String ivrFlag:IVRFlag){
						Map<String, Object> row = new HashMap<String, Object>();
						row.put("custId", Util.trim(l140m01a.getCustId()));
						row.put("custName", Util.trim(l140m01a.getCustName()));
						row.put("record_FileName", Util.trim(ivrFlag));
						row.put("record_LightId", lightId);
						row.put("record_UserID", userId);
						row.put("record_Url", url);
						row.put("record_FileName2", Util.trim(ivrFlag).replace(".wav", ""));
						list.add(row);
					}
				}
			}
		} catch (Throwable t1) {
			throw new CapMessageException(t1.getMessage(), getClass());
		}
		
		return list;
	}
	
	public List<Map<String, Object>> getIVRFiltergrid2(String oid) throws CapException{

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		try{
			C160M01A meta = findModelByOid(C160M01A.class,oid);
			if(meta == null){
				return list;
			}
			String SRCMAINID =meta.getSrcMainId();
	
			List<C160M01B> c160m01bs = (List<C160M01B>) findListByMainId(C160M01B.class, meta.getMainId());
			
			String branCode = user.getUnitNo();
			Date sDate = new Date();
	        Calendar rightNow = Calendar.getInstance();  
	        rightNow.setTime(sDate);  
	        rightNow.add(Calendar.MONTH, NumberUtils.toInt(sysParameterService.getParamValue(SysParamConstants.IVR_Query_StartMonth)));
	        sDate = rightNow.getTime();  
			Date dDate = new Date();
			SimpleDateFormat ft = new SimpleDateFormat ("yyyyMMdd");
			String startDate = ft.format(sDate);
			String endDate = ft.format(dDate);
			String lightId =user.getLightId();
			String userId =user.getUserId();
			
			for(C160M01B c160m01b:c160m01bs){
				String reMainId=c160m01b.getRefmainId();
				if(Util.isEmpty(reMainId)){
					continue;
				}
				L140M01A l140m01a = l140m01aDao.findByMainId(reMainId);
				if(l140m01a == null){
					continue;
				}
				String custId = l140m01a.getCustId();
				String projClass = l140m01a.getProjClass();
				
//				branCode="";
//				startDate="20190601";
//				endDate="20200527";
//				custId="A123456789";
				
				IVRGwReqMessage req = new IVRGwReqMessage(custId,startDate, endDate, lightId, userId , branCode);
				if(Util.notEquals(custId, "") && Util.notEquals(startDate, "") && Util.notEquals(endDate, "") && Util.equals(projClass, LMSUtil.特定金錢信託受益權自行設質擔保授信)){
					List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
					lists=this.find(req);
					for(Map<String, Object> each:lists){
						list.add(each);
					}
				}
			}
		} catch (Throwable t1) {
			throw new CapMessageException(t1.getMessage(), getClass());
		}
		return list;
	}
	
	public List<Map<String, Object>> find(IVRGwReqMessage req) {
		/*
			http://192.168.211.127/megabank/web/admin/midi_rec_edit.php?lightID=ISEhfDAwODAzNHxFTHwwODZBOTQzNDRBRjk2QTY2NTA2NzJFQThCMTJBODdBQXwxNTIwNDE5ODAwMDR3&&UserID=008034&RECORD_FILENAME=201906050090039684320
		 */

		//ivrGwClient.init();
		List<Map<String, Object>> list = ivrGwClient.send(req);
		
		return list;
	}
	
	public L140M01A findByMainId(String reMainId){
		return l140m01aDao.findByMainId(reMainId);
	}

	public List<C101S04W> findC101S04WByMainid(String l120m01aMainId,String custId){
		C340M01A c340m01a = c340m01aDao.findByCaseMainid(l120m01aMainId, new String[]{ContractDocConstants.C340M01A_CtrType.Type_L});
		if(c340m01a!=null){
			return c101s04wDao.findBy(c340m01a.getMainId(), custId);
		}
		return null;
	}

	@Override
	public C340M01A find_c340m01a_CtrTypeL_CtrStatus9(String l120m01aMainId){
		return c340m01aDao.findByCaseMainid(l120m01aMainId, new String[]{ContractDocConstants.C340M01A_CtrType.Type_L});
	}
	
	@Override
	public C340M01A find_c340m01a_CtrTypeA_ploanCtrStatus9_orderBy_ploanCtrBegDateDesc(L140M01A l140m01a){
		List<C340M01A> c340m01a_list = c340m01aDao.findByCustId_ctrTypeA_ctrTypeB_ploanCtrStatus9_tabMainId_orderBy_ploanCtrBegDateDesc(l140m01a.getCustId(), l140m01a.getMainId(),new String[]{ContractDocConstants.C340M01A_CtrType.Type_A,ContractDocConstants.C340M01A_CtrType.Type_B});
		if(c340m01a_list!=null && c340m01a_list.size()>0){
			C340M01A c340m01a = c340m01a_list.get(0);
			return c340m01a;
		}
		return null;
	}
	
	private C160S01A makeC160S01A(C100M01 c100m01, int seqNo, String mainId, MegaSSOUserDetails user, C160M01A c160m01a,L140M01A l140m01a){
		C160S01A c160s01a = new C160S01A();
		// 配合J-103-0264將單位統一為仟元
		c160s01a.setUnitChg("Y");
		try {
			DataParse.copy(c100m01, c160s01a);
		} catch (CapException e) {
			logger.error("[makeC160S01A]copyBean Exception", e);
		}
		BigDecimal priAmt = c100m01.getPriAmt(); // 前順位抵押金額>>無條件進位
		if (priAmt == null) {
			priAmt = BigDecimal.ZERO;
		} else {
			priAmt = Arithmetic.ceil((priAmt.divide(仟元)), 0);
		}
		c160s01a.setPriAmt(priAmt);
		c160s01a.setMainId(mainId);
		c160s01a.setCreateTime(CapDate.getCurrentTimestamp());
		c160s01a.setCreator(user.getUserId());
		c160s01a.setSeqNo(seqNo);
		c160s01a.setRefmainId(l140m01a.getMainId());
		c160s01a.setCmsDesc(this.makeCmsDesc(l140m01a.getCurrentApplyAmt()));
		c160s01a.setCmsOid(c100m01.getOid());
		BigDecimal inAmt = BigDecimal.ZERO;
		BigDecimal totalLoanAmt = BigDecimal.ZERO;
		totalLoanAmt = Util.parseBigDecimal(Util.trim(c100m01.getLoanAmt())); // 押值
		inAmt = Util.parseBigDecimal(c100m01.getTimeVal()); // 時價	
		c160s01a.setTotalLoanAmt(totalLoanAmt);
		if ("Y".equals(c160s01a.getUnitChg())) {// 將估值轉成仟元單位 >>無條件捨去
			c160s01a.setTotLnAmt(Arithmetic.div_floor(totalLoanAmt,
					new BigDecimal(1000), 0));
		}
		c160s01a.setInAmt(inAmt);
		c160s01a.setSetordStr("");
		c160s01a.setSetAmt(new BigDecimal("0"));
		c160s01a.setSet1("");
		c160s01a.setIns1("");
		c160s01a.setInsId("");
		c160s01a.setFireIns("");
		c160s01a.setTerm1("");
		c160s01a.setTerm2("");
		c160s01a.setPayPercent(new BigDecimal("100")); //勞工紓困，固定寫100
		
		return c160s01a;
	} 
	
	private String makeCmsDesc(BigDecimal loadAmount){
			StringBuffer sb_prod69 = new StringBuffer();
			loadAmount = loadAmount.divide(new BigDecimal("10000")).setScale(0);
			if(true){
				List<String> l140m01b_3_list = new ArrayList<String>();
				List<CodeType> codeType_list = codeTypeService.findByCodeTypeList("default_prodKind69_l140m01b_3");
				for(CodeType codeType : codeType_list){
					String codeDesc_raw = codeType.getCodeDesc();
					String codeDesc2 = Util.trim(codeType.getCodeDesc2()); 
					String codeDesc = codeDesc_raw;
					if(codeDesc2.startsWith("@") && codeDesc_raw.indexOf("{0}")>=0 && codeDesc_raw.indexOf("{1}")==0){
					}else if(codeDesc2.startsWith("@") && codeDesc_raw.indexOf("{0}")>=0 && codeDesc_raw.indexOf("{1}")>0){
						if(Util.equals("@amt", codeDesc2)){
							String str_amt = NumConverter.addComma(LMSUtil.pretty_numStr(loadAmount.multiply(new BigDecimal(10000))));
							codeDesc = MessageFormat.format(codeDesc_raw, str_amt, str_amt );
						}else if(Util.equals("@amt1000", codeDesc2)){
							String str_amt1000 = NumConverter.addComma(LMSUtil.pretty_numStr(loadAmount.multiply(new BigDecimal(10)))); //單位:仟元
							codeDesc = MessageFormat.format(codeDesc_raw, str_amt1000, str_amt1000 );
						}
					}
					l140m01b_3_list.add(codeDesc);
				}
				sb_prod69.append(StringUtils.join(l140m01b_3_list, UtilConstants.Mark.HTMLBR));	
			}
		return sb_prod69.toString();
	}
	
	/**
	 * 取得聯徵查詢結果比較資料MAP
	 * @param mainId C160M01A.mainId
	 * @return
	 */
	@Override
	public List<Map<String, Object>> getJcicResultCompareMap(String mainId) {
		List<Map<String, Object>> list = new ArrayList<Map<String,Object>>();
		List<CodeType> codeTypes = codeTypeService.findByCodeTypeList("C101S02A_ejcicItem");
		C160M01A c160m01a = clsService.findC160M01A_mainId(mainId);
		String custId = c160m01a.getCustId();
		String dupNo = c160m01a.getDupNo();
		for (int i = 0; i < codeTypes.size(); i++) {
			CodeType codeType = codeTypes.get(i);
			String item = codeType.getCodeValue();
			String key = item.substring(item.indexOf("_") + 1);
			JSONObject c120JSON = new JSONObject();
			JSONObject c160JSON = new JSONObject();
			String c120DataDate = "";
			String c160DataDate = "";
			String fileLink = "";
			String dataSrcMemo = "";
			String dataType = "";
			String dataStatus = "";
			String oid = "";
			C120S02A c120s02a = clsService.findC120S02AByItem(c160m01a.getSrcMainId(), custId, dupNo, item);
			if (c120s02a != null) {
				c120JSON = JSONObject.fromObject(c120s02a.getJsonOb());
				c120DataDate = CapDate.formatDate(c120s02a.getDataDate(), "yyyy-MM-dd");
			}
			C160S02A c160s02a = clsService.findC160S02AByItem(mainId, custId, dupNo, item);
			if (c160s02a != null) {
				c160JSON = JSONObject.fromObject(c160s02a.getJsonOb());
				c160DataDate = CapDate.formatDate(c160s02a.getDataDate(), "yyyy-MM-dd");
			}
			String c120Val = c120JSON.optString(key);
			String c160Val = c160JSON.optString(key);
			String worse = "";
			if ("BAM095_CONTRACT_AMT1".equals(item)) {
				// BAM095_CONTRACT_AMT1 總計新增3,000仟元以上
				if (CapMath.compare(CapMath.subtract(c160Val, c120Val), "3000") >= 0) {
					worse = "Y";
				}
			} else if ("DAS001_IS_BCHEK".equals(item)) {
				// DAS001_IS_BCHEK 大額退票異常紀錄:Y
				if ("Y".equals(c160Val)) {
					worse = "Y";
				}
			} else if ("DAS002_IS_REJC".equals(item)) {
				// DAS002_IS_REJC  拒絕往來紀錄 :Y
				if ("Y".equals(c160Val)) {
					worse = "Y";
				}
			} else if ("STM022_count".equals(item)) {
				// STM022_count : 被查詢次數3次以上
				if (CapMath.compare(c160Val, "3") >= 0) {
					worse = "Y";
				}
			} else if ("VAM106_count".equals(item)) {
				// VAM106_count: 筆數變多
				if (CapMath.compare(c160Val, c120Val) == 1) {
					worse = "Y";
				}
			} else if ("VAM107_count".equals(item)) {
				// VAM107_count: 筆數變多
				if (CapMath.compare(c160Val, c120Val) == 1) {
					worse = "Y";
				}
			} else if ("KRM040_code".equals(item)) {
				// KRM040_code:繳款狀況出現31、41、32、42、33、43、34、44、35、45、36、46、37、47
				List<String> codes = Arrays.asList(c160Val.split(","));
				List<String> worseCodes = Arrays.asList(new String[] { "31",
						"41", "32", "42", "33", "43", "34", "44", "35", "45",
						"36", "46", "37", "47" });
				for (String code : worseCodes) {
					if (codes.contains(code)) {
						worse = "Y";
						break;
					}
				}
			} else if("STM017_IS_QUERIED".equals(item)){
				fileLink = this.getI18nMsg("C160S02A.open");
				dataSrcMemo = "C101S01U";
				dataType = "ejcic";
				List<C101S01U> c101s01uList = this.clsService.findC101S01U(mainId, custId, dupNo);
				c160DataDate = !c101s01uList.isEmpty() ? CapDate.formatDate(c101s01uList.get(0).getSendTime(), "yyyy-MM-dd") : "";
			} else if("BAS006_IS_CREDIT_AD".equals(item)){
				fileLink = this.getI18nMsg("C160S02A.open");
				dataSrcMemo = "C101S01U";
				dataType = "ejcic";
				List<C101S01U> c101s01uList = this.clsService.findC101S01U(mainId, custId, dupNo);
				c160DataDate = !c101s01uList.isEmpty() ? CapDate.formatDate(c101s01uList.get(0).getSendTime(), "yyyy-MM-dd") : "";
			} else if("FA_DATA_STATUS".equals(item)){
				fileLink = this.getI18nMsg("C160S02A.open");
				dataSrcMemo = "C101S04W";
				C101S04W c101s04w = this.rpaProcessService.getC101S04WBy(mainId, custId);
				dataStatus = c101s04w != null ? this.rpaProcessService.getDataStatus(c101s04w.getStatus(), c101s04w.getReturnData()) : "";
				dataStatus = RPAProcessServiceImpl.statusNameMap.get(dataStatus);
				oid = c101s04w != null ? c101s04w.getOid() : oid ;
				c160DataDate = c101s04w != null ? CapDate.formatDate(c101s04w.getQueryTime(), "yyyy-MM-dd") : "";
			} else if("DA_IS_STAKEHOLDER".equals(item)){
				fileLink = this.getI18nMsg("C160S02A.open");
				dataSrcMemo = "C101S01S";
				dataType = ClsConstants.C101S01S_dataType.客戶是否為利害關係人資料;
				List<C101S01S> c101s01sList = this.c101s01sDao.findByIdDupDataType(mainId, custId, dupNo, ClsConstants.C101S01S_dataType.客戶是否為利害關係人資料);
				c160DataDate = !c101s01sList.isEmpty() ? CapDate.formatDate(c101s01sList.get(0).getDataCreateTime(), "yyyy-MM-dd") : "";
			}

			Map<String, Object> map = new HashMap<String, Object>();
			map.put("worse", worse);
			map.put("ejcicItem", codeType.getCodeDesc());
			map.put("ejcicContentC120", c120Val);
			map.put("ejcicContentC160", c160Val);
			map.put("dataDateC120", c120DataDate);
			map.put("dataDateC160", c160DataDate);
			map.put("openFile", fileLink);//開啟
			map.put("dataSrcMemo", dataSrcMemo);
			map.put("dataType", dataType);
			map.put("dataStatus", dataStatus);
			map.put("oid", oid);
			list.add(map);
		}
		return list;
	}
	
	@Override
	public boolean isAddAllocateFundsCheckList(String mainId_c160s01b, boolean isCheckEjcicB29B33){
		
		List<C160M01B> c160m01bList = this.c160m01bDao.findByMainId(mainId_c160s01b);
		
		if (c160m01bList != null) {
			for (C160M01B c160m01b : c160m01bList) {
				
				L140M01A l140m01a = this.findByMainId(c160m01b.getRefmainId());
				if (l140m01a != null) {
					
					L140M01M l140m01m = this.l140m01mDao.findByMainId(l140m01a.getMainId());
					if(l140m01m != null){
						
						if(this.clsService.isAddAllocateFundsCheckListAndQueryEjcicB29B33(l140m01a.getCntrNo(), l140m01m, isCheckEjcicB29B33)){
							return true;
						}
					}
				}
			}
		}
		
		return false;
	}
	
	@Override
	public void checkBeforeEjcicInquiry(MegaSSOUserDetails user, String c101m01a_mainId, String c160m01a_mainId) throws CapMessageException{
		
		if (!user.isEJCICAuth()) {
			throw new CapMessageException("需具有 EJ(聯徵查詢系統) LN01(授信查詢)權限", getClass());
		}
		
		C101M01A c101m01a = clsService.findC101M01A_mainId(c101m01a_mainId);
		if (c101m01a == null) {
			throw new CapMessageException("mainId[" + c101m01a_mainId + "] not found", getClass());
		}
		
		C160M01A c160m01a = clsService.findC160M01A_mainId(c160m01a_mainId);
		if (c160m01a == null) {
			throw new CapMessageException("mainId[" + c160m01a_mainId + "] not found", getClass());
		}
		
		_verify_call_outerSys("EJ");
	}
	
	@Override
	public void queryEjcicBeforeSendMoney(String userId, String unitNo, String mainId_c160m01a, String custId, String dupNo) throws CapMessageException, ClientProtocolException, IOException{
		
		for(String txId : CLS1161ServiceImpl.Ejcic_TxId){
			this.queryEjcicBy(txId, userId, unitNo, mainId_c160m01a, custId, dupNo);
		}
	}
	
	private void _verify_call_outerSys(String paramArr) throws CapMessageException {
		
		Map<String, String> map = clsService.get_codeTypeWithOrder("J-108-0277_stop_call");
		List<String> errMsg = new ArrayList<String>();
		
		for (String param : paramArr.split("\\|")) {
			if (!map.containsKey(param)) {
				continue;
			}
			String desc = Util.trim(map.get(param));
			if (Util.isNotEmpty(desc)) {
				errMsg.add(desc);
			}
		}
		
		if (errMsg.size() > 0) {
			throw new CapMessageException(StringUtils.join(errMsg, "<br/>"), getClass());
		}
	}
	
	@Override
	public void queryEjcicBy(String txId, String userId, String unitNo, String mainId, String custId, String dupNo) throws CapMessageException, ClientProtocolException, IOException{
		String empname = lmsService.getUserName(userId);
		String deptid = unitNo;
		String cbdeptid = "";
		String deptnm = "";
		String pur_A4G = "A4A";	/*  撥款前查詢聯徵紀錄
								 *  查詢理由:A4A => 
								 *	第一層 A.新業務申請
								 *  第二層 4.放款業務(c) 
								 *	第三層 A.取得當事人書面同意
								 */
		cbdeptid = this.get_cbdeptid_totLen4_BrNo_ChkNo(unitNo);
		
		if (Util.isEmpty(cbdeptid)) {
			throw new CapMessageException("cbdeptid=", getClass());
		}
		
		IBranch iBranch_deptid = branchService.getBranch(deptid);
		if (iBranch_deptid != null) {			
			deptnm = iBranch_deptid.getBrName();		
		}
		
		EJCICGwReqMessage ejcicReq_ST = new EJCICGwReqMessage();
		ejcicReq_ST.setSysId(UtilConstants.CaseSchema.個金);
		ejcicReq_ST.setMsgId(IDGenerator.getUUID());
		ejcicReq_ST.setQueryid(custId);
		ejcicReq_ST.setEmpid(userId);
		ejcicReq_ST.setEmpname(empname);
		ejcicReq_ST.setDeptid(deptid);
		ejcicReq_ST.setCbdeptid(cbdeptid);
		ejcicReq_ST.setBranchnm(deptnm);
		ejcicReq_ST.setPur(pur_A4G);
		if (true) {
			ejcicReq_ST.setProdid("");
			ejcicReq_ST.setTxid(txId);
			ejcicReq_ST.setQkey1(custId);// custId
			ejcicReq_ST.setQkey2("");
		}
		String url_S11 = Util.trim(ejcicClient.get_callAPI_URL(ejcicReq_ST)); // 取得 url
		keep_url_htmloutput_to_c101s01u(mainId, custId, dupNo, txId, url_S11);
	}
	
	private String get_cbdeptid_totLen4_BrNo_ChkNo(String given_cbdeptid){
		IBranch iBranch_cbdeptid = branchService.getBranch(given_cbdeptid);
		if(iBranch_cbdeptid!=null){
			return iBranch_cbdeptid.getBrNo() + iBranch_cbdeptid.getChkNo();	
		}
		return "";
	}
	
	private void keep_url_htmloutput_to_c101s01u(String mainId, String custId, String dupNo, String txId, String url) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		InputStream in = null;
		String resp_html = "";
		try {
			in = new URL(url).openStream();
			StringWriter writer = new StringWriter();
			IOUtils.copy(in, writer, "Big5_HKSCS");
			resp_html = writer.toString();

			List<C101S01U> c101s01u_list = clsService.findC101S01U_txid(mainId, custId, dupNo, txId);
			Timestamp nowTS = CapDate.getCurrentTimestamp();
			C101S01U c101s01u = null;
			if (c101s01u_list.size() > 0) {
				c101s01u = c101s01u_list.get(0);
			} else {
				c101s01u = new C101S01U();
				c101s01u.setMainId(mainId);
				c101s01u.setCustId(custId);
				c101s01u.setDupNo(dupNo);
				c101s01u.setTxid(txId);
				c101s01u.setCreator(user.getUserId());
				c101s01u.setCreateTime(nowTS);
			}
			c101s01u.setHtmlData(resp_html);
			c101s01u.setSendTime(nowTS);
			c101s01u.setUpdater(user.getUserId());
			c101s01u.setUpdateTime(nowTS);
			clsService.daoSave(c101s01u);
		} catch (IOException ioe) {
			logger.error("url={}, resp_html={}", url, resp_html);
			logger.error(StrUtils.getStackTrace(ioe));
		} catch (Exception e) {
			logger.error("url={}, resp_html={}", url, resp_html);
			logger.error(StrUtils.getStackTrace(e));
		} finally {
			IOUtils.closeQuietly(in);
		}
	}
	
	@Override
	public void setC160M02AExcelStatus(C160M02A c160m02a,String status){
		if(c160m02a != null){
			c160m02a.setImportExcelStatus(status);
			c160m02aDao.save(c160m02a);
		}
	}
	
	@Override
	public Map<String, Object> getEjcicReusltRecordForPreMoneyAllocatingInquiry(String custId, String prodId, String qDate) {
		Map<String, Object> map = new HashMap<String, Object>();
		List<CodeType> codeTypes = codeTypeService.findByCodeTypeList("C101S02A_ejcicItem");
		
		Map<String, Object> lnf320Map = this.mis.getLNF320ByNextBusinessDay(CapDate.getCurrentDate("yyyy-MM-dd"));
		Date preBussinessDate = lnf320Map == null ? null : (Date)lnf320Map.get("LNF320_QUERY_DATE");
		
		for (CodeType codeType : codeTypes) {
			
			String item = codeType.getCodeValue();
			JSONObject json = new JSONObject();
			
			if("STM017_IS_QUERIED".equals(item)){
				//近2個營業日有無他行查詢聯徵紀錄
				List<Map<String, Object>> list = this.ejcicService.getEjcicQueryRecoredByOtherBank(custId, prodId, qDate);
				boolean isQueriedByOtherBank = false;
				for(Map<String, Object> m : list){
					String queryDate_yyyyMMdd = CapDate.formatDateFormatToyyyyMMdd(String.valueOf(m.get("QUERY_DATE")), "YYYMMDD");
					Date queryDate = CapDate.parseDate(queryDate_yyyyMMdd);
					if(preBussinessDate != null && queryDate.compareTo(preBussinessDate) >= 0){
						isQueriedByOtherBank = true;
					}
				}
				json.put("IS_QUERIED", isQueriedByOtherBank ? "Y" : "N");
			}
			else if("BAS006_IS_CREDIT_AD".equals(item)){
				//是否有新增額度/撥款清償提示資訊
				List<Map<String, Object>> list = this.ejcicService.getNewQuotaOrRepaymentInfo(custId, prodId, qDate);
				String isNewQuotaOrRepaymentInfo = "N";
				for(Map<String, Object> m : list){
					isNewQuotaOrRepaymentInfo = "Y".equals(m.get("CREDIT_AD_FLAG")) ? "Y" : isNewQuotaOrRepaymentInfo;
				}
				json.put("IS_CREDIT_AD", isNewQuotaOrRepaymentInfo);
			}
			
			map.put(item, json.toString());
		}
		
		return map;
	}
	
	@Override
	public boolean isRunInQueryBeforeMoneyAllocating(String mainId_c160m01b){
		List<C160M01B> c160m01bList = this.c160m01bDao.findByMainId(mainId_c160m01b);
		if (c160m01bList != null) {
			for (C160M01B c160m01b : c160m01bList) {
				
				L140M01A l140m01a = this.clsService.findL140M01A_mainId(c160m01b.getRefmainId());
				if (l140m01a != null) {
					
					if (LMSUtil.isContainValue(l140m01a.getProPerty(),
							UtilConstants.Cntrdoc.Property.取消)
							|| LMSUtil.isContainValue(l140m01a.getProPerty(),
									UtilConstants.Cntrdoc.Property.不變)) {
						continue;
					}
					
					List<L140S02A> l140s02aList = this.clsService.findL140S02A(l140m01a);
					for(L140S02A l140s02a : l140s02aList){
						if("07".equals(l140s02a.getProdKind()) || "71".equals(l140s02a.getProdKind())){
							return true;
						}
					}
				}
			}
		}
		
		return false;
	}
}
